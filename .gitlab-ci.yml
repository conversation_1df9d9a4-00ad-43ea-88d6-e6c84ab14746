image: node:20
variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ''
  KUBE_LATEST_VERSION: v1.25.8
  IMAGE_PULL_POLICY: 'Always'
  IMAGE: $CI_REGISTRY_IMAGE
  IMAGE_TAG: $CI_COMMIT_REF_SLUG
  APP_ENV_IMAGE_TAG: $CI_COMMIT_REF_SLUG
  APP_ENV_COMMIT_SHA: $CI_COMMIT_SHORT_SHA
  APP_ENV_NODE_ENV: 'production'

stages:
  - build
  - deploy

.docker-build-template: &docker-build-template
  stage: build
  image: docker:26.0.0
  services:
    - docker:26.0.0-dind
  script:
    - docker build -t "$CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG" .
      --build-arg VITE_APP_ENV=$VITE_APP_ENV
      --build-arg VITE_CLIENT_URL=$VITE_CLIENT_URL
      --build-arg VITE_PRELOAD_SERVER_URL=$VITE_PRELOAD_SERVER_URL
      --build-arg VITE_TONCONNECT_MANIFEST=$VITE_TONCONNECT_MANIFEST
      --build-arg VITE_REFLINK_ORIGIN=$VITE_REFLINK_ORIGIN
      --build-arg VITE_GTM_ID=$VITE_GTM_ID
      --build-arg VITE_MAIN_WALLET_ADDR=$VITE_MAIN_WALLET_ADDR
      --build-arg VITE_TON_STATION_BOT_LINK=$VITE_TON_STATION_BOT_LINK
    - docker login -u gitlab-ci-token -p "$CI_JOB_TOKEN" "$CI_REGISTRY"
    - docker push "$CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG"

build dev:
  <<: *docker-build-template
  variables:
    VITE_APP_ENV: 'dev'
    VITE_CLIENT_URL: 'https://unijump-development.k8s.oriongames.lol'
    VITE_PRELOAD_SERVER_URL: 'https://unijump-development.k8s.oriongames.lol'
    VITE_TONCONNECT_MANIFEST: 'tonconnect-manifest'
    VITE_REFLINK_ORIGIN: 't.me/umpingGames_dev2_bot/dev'
    VITE_GTM_ID: 'NONE'
    VITE_MAIN_WALLET_ADDR: 'UQBQQGRkPQfq-YZ-bZxCYTQbIoQYB3E_pB-8-aNS1osIjmwr'
    VITE_TON_STATION_BOT_LINK: 'https://t.me/tonstationgames_dev_bot/app?startapp=ref_m3sqhs7gq4pxhqadhdw7p1'
  only:
    - dev
    - release-dev

build feature:
  <<: *docker-build-template
  variables:
    VITE_APP_ENV: 'dev'
    VITE_CLIENT_URL: 'https://unijump-feature-1.k8s.oriongames.lol'
    VITE_PRELOAD_SERVER_URL: 'https://unijump-feature-1.k8s.oriongames.lol'
    VITE_TONCONNECT_MANIFEST: 'tonconnect-manifest'
    VITE_REFLINK_ORIGIN: 'https://t.me/oriongames_dev_bot/featureTest'
    VITE_GTM_ID: 'NONE'
    VITE_MAIN_WALLET_ADDR: 'UQBQQGRkPQfq-YZ-bZxCYTQbIoQYB3E_pB-8-aNS1osIjmwr'
    VITE_TON_STATION_BOT_LINK: 'https://t.me/tonstationgames_dev_bot/app?startapp=ref_m3sqhs7gq4pxhqadhdw7p1'
  only:
    - /^feature\/.*/

build fix:
  <<: *docker-build-template
  variables:
    VITE_APP_ENV: 'dev'
    VITE_CLIENT_URL: 'https://unijump-feature-1.k8s.oriongames.lol'
    VITE_PRELOAD_SERVER_URL: 'https://unijump-feature-1.k8s.oriongames.lol'
    VITE_TONCONNECT_MANIFEST: 'tonconnect-manifest'
    VITE_REFLINK_ORIGIN: 't.me/umpingGames_dev2_bot/feature'
    VITE_GTM_ID: 'NONE'
    VITE_MAIN_WALLET_ADDR: 'UQBQQGRkPQfq-YZ-bZxCYTQbIoQYB3E_pB-8-aNS1osIjmwr'
    VITE_TON_STATION_BOT_LINK: 'https://t.me/tonstationgames_dev_bot/app?startapp=ref_m3sqhs7gq4pxhqadhdw7p1'
  only:
    - /^fix\/.*/

build staging:
  <<: *docker-build-template
  variables:
    VITE_APP_ENV: 'prod'
    VITE_CLIENT_URL: 'https://unijump-staging.k8s.oriongames.lol'
    VITE_PRELOAD_SERVER_URL: 'https://unijump-staging.k8s.oriongames.lol'
    VITE_TONCONNECT_MANIFEST: 'tonconnect-manifest'
    VITE_REFLINK_ORIGIN: 'https://t.me/oriongames_staging_bot/orionGamesStaging'
    VITE_GTM_ID: 'NONE'
    VITE_MAIN_WALLET_ADDR: 'UQBQQGRkPQfq-YZ-bZxCYTQbIoQYB3E_pB-8-aNS1osIjmwr'
    VITE_TON_STATION_BOT_LINK: 'https://t.me/tonstationgames_dev_bot/app?startapp=ref_m3sqhs7gq4pxhqadhdw7p1'
  only:
    - master
    - release

build prod:
  <<: *docker-build-template
  variables:
    VITE_APP_ENV: 'prod'
    VITE_CLIENT_URL: 'https://unijump.xyz'
    VITE_PRELOAD_SERVER_URL: 'https://unijump.xyz'
    VITE_TONCONNECT_MANIFEST: 'tonconnect-manifest'
    VITE_REFLINK_ORIGIN: 'https://t.me/unijump_bot/game'
    VITE_GTM_ID: 'G-XDC7TS3MDQ'
    VITE_MAIN_WALLET_ADDR: 'UQCPFbIY6sIopqSVSE0vxKNnLBh77LoEt-Y4SocRdVJVe0PB'
    VITE_TON_STATION_BOT_LINK: 'https://t.me/tonstationgames_bot/app?startapp=ref_fasfgqcqc9ddktchzk1dro'
  only:
    - tags

.deploy-template: &deploy-template
  image: node:20
  stage: deploy
  script:
    - ./install-kubectl.sh
    - kubectl config use-context native-games/k8s-agent:k8s-prod
    - ./deploy.sh

deploy dev:
  <<: *deploy-template
  variables:
    KUBE_NAMESPACE: 'unijump-dev'
    DEPLOYMENT_NAME: 'dev'
    APP_ENV_DOMAIN: 'unijump-development.k8s.oriongames.lol'
  environment:
    name: dev
    url: https://unijump-development.k8s.oriongames.lol
  only:
    - dev
    - release-dev

deploy feature:
  <<: *deploy-template
  variables:
    KUBE_NAMESPACE: 'unijump-dev'
    SERVICE_NAME_SUFFIX: '-feature'
    DEPLOYMENT_NAME: 'dev'
    APP_ENV_DOMAIN: 'unijump-feature-1.k8s.oriongames.lol'
  environment:
    name: feature
    url: https://unijump-feature-1.k8s.oriongames.lol
  only:
    - /^feature\/.*/
  when: manual

deploy staging:
  <<: *deploy-template
  variables:
    KUBE_NAMESPACE: 'unijump-staging'
    DEPLOYMENT_NAME: 'staging'
    APP_ENV_DOMAIN: 'unijump-staging.k8s.oriongames.lol'
  environment:
    name: staging
    url: https://unijump-staging.k8s.oriongames.lol
  only:
    - master
    - release


deploy production:
  image: node:20
  stage: deploy
  script:
    - ./install-kubectl.sh
    - kubectl config use-context native-games/k8s-agent:k8s-prod
    - ./deploy.sh
  variables:
    KUBE_NAMESPACE: 'unijump-prod'
    DEPLOYMENT_NAME: 'production'
    PROD_RELEASE: "true"
    APP_ENV_DOMAIN: 'unijump.xyz'
  environment:
    name: production
    url: https://unijump.xyz
  only:
    - tags

{"skeleton": {"hash": "K/xVeP123/w", "spine": "4.2.43", "x": -513.5, "y": -661.59, "width": 1079, "height": 1846, "images": "./images/box/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "main", "parent": "root", "icon": "square"}, {"name": "boxMain", "parent": "main", "color": "ffbe06ff", "icon": "square"}, {"name": "box", "parent": "boxMain", "length": 516.48, "rotation": 89.26, "x": 32.84, "y": 32.81, "color": "ffbe06ff"}, {"name": "box_up", "parent": "box", "x": 349.49, "y": 15.87, "color": "ffbe06ff", "icon": "circle"}, {"name": "box_down", "parent": "box", "x": 14.9, "y": 11.56, "color": "ffbe06ff", "icon": "circle"}, {"name": "ray_main", "parent": "main", "x": 40.92, "y": 233.33, "scaleX": 0.7, "scaleY": 0.7, "color": "fe2502ff", "icon": "ik"}, {"name": "glow", "parent": "main", "x": 40.92, "y": 233.33, "color": "0dffceff", "icon": "diamond"}, {"name": "ray_00", "parent": "main", "x": 40.92, "y": 651.94, "scaleX": 0.7, "scaleY": 0.7, "color": "fe2502ff", "icon": "ik"}, {"name": "r25", "parent": "ray_00", "length": 1313.59, "rotation": 90.75}, {"name": "r27", "parent": "ray_00", "length": 1313.59, "rotation": 60.75, "scaleX": 1.2679, "scaleY": 1.542}, {"name": "r29", "parent": "ray_00", "length": 1313.59, "rotation": 30.75, "scaleY": 0.7423}, {"name": "r31", "parent": "ray_00", "length": 1313.59, "rotation": 0.75, "scaleX": 0.8186, "scaleY": 1.06}, {"name": "r33", "parent": "ray_00", "length": 1313.59, "rotation": -29.25, "scaleX": 1.4499}, {"name": "r35", "parent": "ray_00", "length": 1313.59, "rotation": -59.25, "scaleX": 0.7914, "scaleY": 1.5216}, {"name": "r37", "parent": "ray_00", "length": 1313.59, "rotation": -89.25, "scaleX": 1.2283, "scaleY": 0.7707}, {"name": "r39", "parent": "ray_00", "length": 1313.59, "rotation": -119.25, "scaleX": 0.8672, "scaleY": 0.3944}, {"name": "r41", "parent": "ray_00", "length": 1313.59, "rotation": -149.25, "scaleX": 1.4314, "scaleY": 1.9406}, {"name": "r43", "parent": "ray_00", "length": 1313.59, "rotation": -179.25, "scaleX": 1.1522, "scaleY": 0.6956}, {"name": "r45", "parent": "ray_00", "length": 1313.59, "rotation": -209.25, "scaleX": 1.1036, "scaleY": 1.4144}, {"name": "r47", "parent": "ray_00", "length": 1313.59, "rotation": -239.25, "scaleX": 1.4049, "scaleY": 0.8303}, {"name": "stars", "parent": "main", "color": "ecff00ff", "icon": "star"}, {"name": "star", "parent": "stars", "x": 143.27, "y": 554.89, "color": "ecff00ff", "icon": "star"}, {"name": "star2", "parent": "stars", "x": -256.17, "y": 402.98, "color": "ecff00ff", "icon": "star"}, {"name": "star3", "parent": "stars", "x": 325.53, "y": 226.39, "color": "ecff00ff", "icon": "star"}, {"name": "star4", "parent": "stars", "x": 323.23, "y": 136.26, "color": "ecff00ff", "icon": "star"}, {"name": "star5", "parent": "stars", "x": -24.59, "y": -47.73, "color": "ecff00ff", "icon": "star"}, {"name": "star6", "parent": "stars", "x": -256.17, "y": 402.98, "color": "ecff00ff", "icon": "star"}, {"name": "fxs", "parent": "main"}, {"name": "boom", "parent": "fxs", "color": "d100ffff"}, {"name": "ellipse", "parent": "fxs", "x": 40.92, "y": 651.94, "icon": "warning"}, {"name": "b1_stroke_glow", "parent": "boom", "x": 40.5, "y": 664.41, "color": "d100ffff"}, {"name": "b2_stroke", "parent": "boom", "x": 40.5, "y": 664.41, "color": "d100ffff"}, {"name": "b1_elipse", "parent": "boom", "x": 40.5, "y": 664.41, "color": "d100ffff"}, {"name": "box_shadow", "parent": "boxMain", "x": 63.9, "y": 71.44, "color": "ffbe06ff", "icon": "square"}, {"name": "Uni", "parent": "root", "color": "fff608ff", "icon": "suiteClubs"}, {"name": "uni_ch", "parent": "Uni", "length": 420.11, "rotation": 90, "color": "fff608ff"}, {"name": "UniRay", "parent": "Uni", "length": 1248.94, "rotation": -88.23, "x": -18.92, "y": 582.91, "color": "fff608ff"}, {"name": "starsUni", "parent": "Uni", "color": "e200ffff", "icon": "star"}, {"name": "star7", "parent": "starsUni", "x": 143.27, "y": 554.89, "color": "e200ffff", "icon": "star"}, {"name": "star8", "parent": "starsUni", "x": -256.17, "y": 402.98, "color": "e200ffff", "icon": "star"}, {"name": "star9", "parent": "starsUni", "x": 325.53, "y": 226.39, "color": "e200ffff", "icon": "star"}, {"name": "star10", "parent": "starsUni", "x": 323.23, "y": 136.26, "color": "e200ffff", "icon": "star"}, {"name": "star11", "parent": "starsUni", "x": -24.59, "y": -47.73, "color": "e200ffff", "icon": "star"}, {"name": "star12", "parent": "starsUni", "x": -256.17, "y": 402.98, "color": "e200ffff", "icon": "star"}], "slots": [{"name": "mask", "bone": "root"}, {"name": "rays/glow_01", "bone": "glow", "attachment": "glow_01", "blend": "additive"}, {"name": "rays/rey_025", "bone": "r25", "blend": "additive"}, {"name": "rays/rey_027", "bone": "r27", "blend": "additive"}, {"name": "rays/rey_029", "bone": "r29", "blend": "additive"}, {"name": "rays/rey_031", "bone": "r31", "blend": "additive"}, {"name": "rays/rey_033", "bone": "r33", "blend": "additive"}, {"name": "rays/rey_035", "bone": "r35", "blend": "additive"}, {"name": "rays/rey_037", "bone": "r37", "blend": "additive"}, {"name": "rays/rey_039", "bone": "r39", "blend": "additive"}, {"name": "rays/rey_041", "bone": "r41", "blend": "additive"}, {"name": "rays/rey_043", "bone": "r43", "blend": "additive"}, {"name": "rays/rey_045", "bone": "r45", "blend": "additive"}, {"name": "rays/rey_047", "bone": "r47", "blend": "additive"}, {"name": "sine/rays", "bone": "ray_main", "blend": "additive"}, {"name": "sine/particle", "bone": "main"}, {"name": "box/shadow", "bone": "box_shadow", "attachment": "shadow"}, {"name": "box/lucky_box", "bone": "box", "attachment": "box"}, {"name": "box/lucky_box2", "bone": "box", "attachment": "box", "blend": "additive"}, {"name": "stars/star", "bone": "star"}, {"name": "stars/star7", "bone": "star7"}, {"name": "stars/star2", "bone": "star2"}, {"name": "stars/star8", "bone": "star8"}, {"name": "stars/star6", "bone": "star6"}, {"name": "stars/star12", "bone": "star12"}, {"name": "stars/star3", "bone": "star3"}, {"name": "stars/star9", "bone": "star9"}, {"name": "stars/star4", "bone": "star4"}, {"name": "stars/star10", "bone": "star10"}, {"name": "stars/star5", "bone": "star5"}, {"name": "stars/star11", "bone": "star11"}, {"name": "FX/ellipse_01_glow_01", "bone": "b1_elipse", "attachment": "ellipse_01_glow_01"}, {"name": "FX/ellipse_01_glow_02", "bone": "b1_elipse", "attachment": "ellipse_01_glow_02"}, {"name": "FX/ellips_stroke", "bone": "b2_stroke", "attachment": "ellips_stroke"}, {"name": "FX/ellips_stroke_glow", "bone": "b1_stroke_glow", "attachment": "ellips_stroke_glow"}, {"name": "FX/ellipse_01", "bone": "b1_elipse", "attachment": "ellipse_01"}, {"name": "FX/ellipse_02", "bone": "ellipse", "blend": "additive"}, {"name": "FX/ellipse_03", "bone": "ellipse", "blend": "additive"}, {"name": "FX/ellipse_04", "bone": "ellipse", "blend": "additive"}, {"name": "FX/ellipse_05", "bone": "ellipse", "blend": "additive"}, {"name": "uni/green_glow_01", "bone": "Uni"}, {"name": "uni/green_glow_02", "bone": "Uni"}, {"name": "uni/green_ray", "bone": "UniRay", "blend": "additive"}, {"name": "uni/green_uni_01", "bone": "uni_ch"}, {"name": "uni/green_uni_02", "bone": "uni_ch"}, {"name": "uni/green_uni_03", "bone": "uni_ch"}, {"name": "uni/green_uni_04", "bone": "uni_ch"}, {"name": "uni/green_uni_05", "bone": "uni_ch"}, {"name": "white_screen", "bone": "main"}], "skins": [{"name": "default", "attachments": {"white_screen": {"white_screen": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1221.5, -841.59, -1221.5, -841.59, -1221.5, 1716.41, 1221.5, 1716.41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 611, "height": 640}}, "box/shadow": {"shadow": {"x": -11.9, "y": -14.53, "width": 829, "height": 205}}, "FX/ellipse_01": {"ellipse_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [187, -195, -204, -195, -204, 195, 187, 195], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 391, "height": 390}}, "FX/ellipse_01_glow_01": {"ellipse_01_glow_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [335.5, -344, -352.5, -344, -352.5, 344, 335.5, 344], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 688, "height": 688}}, "FX/ellipse_01_glow_02": {"ellipse_01_glow_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [488.5, -497, -505.5, -497, -505.5, 497, 488.5, 497], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 746, "height": 746}}, "FX/ellipse_02": {"ellipse_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [343.58, -267.52, -361.42, -267.52, -361.42, 437.48, 343.58, 437.48], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 705, "height": 705}}, "FX/ellipse_03": {"ellipse_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [343.58, -267.52, -361.42, -267.52, -361.42, 437.48, 343.58, 437.48], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 705, "height": 705}}, "FX/ellipse_04": {"ellipse_03_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [343.58, -267.52, -361.42, -267.52, -361.42, 437.48, 343.58, 437.48], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 705, "height": 705}}, "FX/ellipse_05": {"ellipse_03_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [343.58, -267.52, -361.42, -267.52, -361.42, 331.48, 343.58, 331.48], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 705, "height": 599}}, "FX/ellips_stroke": {"ellips_stroke": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [283.5, -292, -300.5, -292, -300.5, 292, 283.5, 292], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 438, "height": 438}}, "FX/ellips_stroke_glow": {"ellips_stroke_glow": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [511.5, -520, -528.5, -520, -528.5, 520, 511.5, 520], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 780, "height": 780}}, "rays/glow_01": {"glow_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [524.58, -894.91, -554.42, -894.91, -554.42, 860.09, 524.58, 860.09], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 540, "height": 878}}, "rays/rey_025": {"rey_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [0, 2, 3, 0, 1, 2], "vertices": [-19.34, -110.82, -16.18, 131.15, 1448.69, 112.04, 1445.53, -129.94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 121, "height": 733}}, "rays/rey_027": {"rey_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [0, 2, 3, 0, 1, 2], "vertices": [-19.34, -110.82, -16.18, 131.15, 1448.69, 112.04, 1445.53, -129.94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 121, "height": 733}}, "rays/rey_029": {"rey_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 0, 1, 3], "vertices": [-19.34, -110.82, -16.18, 131.15, 1448.69, 112.04, 1445.53, -129.94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 121, "height": 733}}, "rays/rey_031": {"rey_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 0, 2, 3, 0], "vertices": [-19.34, -110.82, -16.18, 131.15, 1448.69, 112.04, 1445.53, -129.94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 121, "height": 733}}, "rays/rey_033": {"rey_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [0, 2, 3, 0, 1, 2], "vertices": [-19.34, -110.82, -16.18, 131.15, 1448.69, 112.04, 1445.53, -129.94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 121, "height": 733}}, "rays/rey_035": {"rey_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 0, 2, 3, 0], "vertices": [-19.34, -110.82, -16.18, 131.15, 1448.69, 112.04, 1445.53, -129.94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 121, "height": 733}}, "rays/rey_037": {"rey_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [0, 2, 3, 0, 1, 2], "vertices": [-19.34, -110.82, -16.18, 131.15, 1448.69, 112.04, 1445.53, -129.94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 121, "height": 733}}, "rays/rey_039": {"rey_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 0, 1, 3], "vertices": [-19.34, -110.82, -16.18, 131.15, 1448.69, 112.04, 1445.53, -129.94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 121, "height": 733}}, "rays/rey_041": {"rey_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [0, 2, 3, 0, 1, 2], "vertices": [-19.34, -110.82, -16.18, 131.15, 1448.69, 112.04, 1445.53, -129.94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 121, "height": 733}}, "rays/rey_043": {"rey_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 0, 2, 3, 0], "vertices": [-19.34, -110.82, -16.18, 131.15, 1448.69, 112.04, 1445.53, -129.94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 121, "height": 733}}, "rays/rey_045": {"rey_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 0, 2, 3, 0], "vertices": [-19.34, -110.82, -16.18, 131.15, 1448.69, 112.04, 1445.53, -129.94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 121, "height": 733}}, "rays/rey_047": {"rey_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [0, 2, 3, 0, 1, 2], "vertices": [-19.34, -110.82, -16.18, 131.15, 1448.69, 112.04, 1445.53, -129.94], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 121, "height": 733}}, "sine/particle": {"particle": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [0, 2, 3, 0, 1, 2], "vertices": [72.5, 7.41, -9.5, 7.41, -9.5, 757.41, 72.5, 757.41], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 82, "height": 750}}, "sine/rays": {"rays": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1206.34, -1290.48, -1236.66, -1290.48, -1236.66, 1267.52, 1206.34, 1267.52], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 917, "height": 959}}, "stars/star": {"star": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [49.23, -53.47, -47.77, -53.47, -47.77, 43.53, 49.23, 43.53], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 97, "height": 97}}, "stars/star2": {"star": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2.33, -63.94, -58.24, -6.58, -0.88, 53.99, 59.69, -3.37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 97, "height": 97}}, "stars/star3": {"star": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [20.86, -16.74, -11.04, -25.1, -19.4, 6.8, 12.5, 15.16], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 97, "height": 97}}, "stars/star4": {"star": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [34.68, -40.94, -33.22, -40.94, -33.22, 26.96, 34.68, 26.96], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 97, "height": 97}}, "stars/star5": {"star": {"type": "mesh", "color": "ffffffcf", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [34.68, -38.92, -33.22, -38.92, -33.22, 28.98, 34.68, 28.98], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 97, "height": 97}}, "stars/star6": {"star": {"type": "mesh", "color": "ffffffa9", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [56.06, -130.94, -2.14, -130.94, -2.14, -72.74, 56.06, -72.74], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 97, "height": 97}}, "stars/star7": {"star": {"x": 0.73, "y": -4.97, "width": 97, "height": 97}}, "stars/star8": {"star": {"x": 0.73, "y": -4.97, "scaleX": 0.86, "scaleY": 0.86, "rotation": -43.44, "width": 97, "height": 97}}, "stars/star9": {"star": {"x": 0.73, "y": -4.97, "scaleX": 0.34, "scaleY": 0.34, "rotation": 14.68, "width": 97, "height": 97}}, "stars/star10": {"star": {"x": 0.73, "y": -6.99, "scaleX": 0.7, "scaleY": 0.7, "width": 97, "height": 97}}, "stars/star11": {"star": {"color": "ffffffcf", "x": 0.73, "y": -4.97, "scaleX": 0.7, "scaleY": 0.7, "width": 97, "height": 97}}, "stars/star12": {"star": {"color": "ffffffa9", "x": 26.96, "y": -101.84, "scaleX": 0.6, "scaleY": 0.6, "width": 97, "height": 97}}}}, {"name": "box/green", "attachments": {"box/lucky_box": {"box": {"name": "lucky_box", "type": "mesh", "uvs": [0.59819, 0.16979, 0.92923, 0.1926, 1, 0.24506, 1, 0.39531, 1, 0.56401, 1, 0.66447, 1, 0.82672, 0.76066, 0.99999, 0.12864, 0.99131, 0, 0.89028, 0, 0.74886, 0, 0.60911, 0, 0.4204, 0, 0.30665, 0.05822, 0.23594, 0.35398, 0, 0.51408, 0, 0.85597, 0.44777, 0.80713, 0.78764, 0.83691, 0.6386], "triangles": [17, 1, 2, 12, 13, 14, 17, 0, 1, 3, 17, 2, 15, 16, 0, 14, 15, 0, 12, 14, 0, 12, 0, 17, 17, 3, 4, 19, 11, 12, 17, 19, 12, 19, 17, 4, 19, 4, 5, 18, 10, 11, 19, 18, 11, 18, 19, 5, 18, 5, 6, 8, 9, 10, 18, 8, 10, 7, 8, 18, 7, 18, 6], "vertices": [2, 3, 439.45, -59.14, 0.85, 4, 89.96, -75, 0.15, 2, 3, 428.47, -218.19, 0.85, 4, 78.98, -234.06, 0.15, 2, 3, 398.95, -252.54, 0.85, 4, 49.46, -268.41, 0.15, 2, 3, 313.17, -253.64, 0.95, 4, -36.32, -269.51, 0.05, 1, 3, 216.85, -254.88, 1, 1, 3, 159.49, -255.62, 1, 2, 3, 66.86, -256.82, 0.85, 5, 51.96, -268.38, 0.15, 2, 3, -33.55, -143.22, 0.85, 5, -48.45, -154.78, 0.15, 2, 3, -32.5, 160.19, 0.85, 5, -47.4, 148.63, 0.15, 2, 3, 24.38, 222.68, 0.85, 5, 9.49, 211.12, 0.15, 1, 3, 105.13, 223.72, 1, 1, 3, 184.92, 224.74, 1, 2, 3, 292.67, 226.13, 0.95, 4, -56.83, 210.26, 0.05, 2, 3, 357.61, 226.97, 0.85, 4, 8.12, 211.1, 0.15, 2, 3, 398.34, 199.54, 0.85, 4, 48.85, 183.67, 0.15, 1, 3, 534.88, 59.32, 1, 1, 3, 535.87, -17.51, 1, 2, 3, 282.33, -184.9, 0.95, 4, -67.17, -200.77, 0.05, 1, 3, 87.98, -163.96, 1, 1, 3, 173.25, -177.16, 1], "hull": 17, "edges": [26, 28, 28, 30, 30, 32, 32, 0, 0, 2, 2, 4, 12, 14, 14, 16, 18, 16, 24, 26, 4, 6, 34, 6, 18, 20, 10, 12, 36, 10, 24, 34, 20, 36, 20, 22, 22, 24, 22, 38, 6, 8, 8, 10, 38, 8], "width": 480, "height": 571}}, "box/lucky_box2": {"box": {"name": "lucky_box", "type": "mesh", "uvs": [0.59819, 0.16979, 0.92923, 0.1926, 1, 0.24506, 1, 0.39531, 1, 0.56401, 1, 0.66447, 1, 0.82672, 0.76066, 0.99999, 0.12864, 0.99131, 0, 0.89028, 0, 0.74886, 0, 0.60911, 0, 0.4204, 0, 0.30665, 0.05822, 0.23594, 0.35398, 0, 0.51408, 0, 0.85597, 0.44777, 0.80713, 0.78764, 0.83691, 0.6386], "triangles": [17, 1, 2, 12, 13, 14, 17, 0, 1, 3, 17, 2, 15, 16, 0, 14, 15, 0, 12, 14, 0, 12, 0, 17, 17, 3, 4, 19, 11, 12, 17, 19, 12, 19, 17, 4, 19, 4, 5, 18, 10, 11, 19, 18, 11, 18, 19, 5, 18, 5, 6, 8, 9, 10, 18, 8, 10, 7, 8, 18, 7, 18, 6], "vertices": [2, 3, 439.45, -59.14, 0.85, 4, 89.96, -75, 0.15, 2, 3, 428.47, -218.19, 0.85, 4, 78.98, -234.06, 0.15, 2, 3, 398.95, -252.54, 0.85, 4, 49.46, -268.41, 0.15, 2, 3, 313.17, -253.64, 0.95, 4, -36.32, -269.51, 0.05, 1, 3, 216.85, -254.88, 1, 1, 3, 159.49, -255.62, 1, 2, 3, 66.86, -256.82, 0.85, 5, 51.96, -268.38, 0.15, 2, 3, -33.55, -143.22, 0.85, 5, -48.45, -154.78, 0.15, 2, 3, -32.5, 160.19, 0.85, 5, -47.4, 148.63, 0.15, 2, 3, 24.38, 222.68, 0.85, 5, 9.49, 211.12, 0.15, 1, 3, 105.13, 223.72, 1, 1, 3, 184.92, 224.74, 1, 2, 3, 292.67, 226.13, 0.95, 4, -56.83, 210.26, 0.05, 2, 3, 357.61, 226.97, 0.85, 4, 8.12, 211.1, 0.15, 2, 3, 398.34, 199.54, 0.85, 4, 48.85, 183.67, 0.15, 1, 3, 534.88, 59.32, 1, 1, 3, 535.87, -17.51, 1, 2, 3, 282.33, -184.9, 0.95, 4, -67.17, -200.77, 0.05, 1, 3, 87.98, -163.96, 1, 1, 3, 173.25, -177.16, 1], "hull": 17, "edges": [26, 28, 28, 30, 30, 32, 32, 0, 0, 2, 2, 4, 12, 14, 14, 16, 18, 16, 24, 26, 4, 6, 34, 6, 18, 20, 10, 12, 36, 10, 24, 34, 20, 36, 20, 22, 22, 24, 22, 38, 6, 8, 8, 10, 38, 8], "width": 480, "height": 571}}}}, {"name": "box/magic", "attachments": {"box/lucky_box": {"box": {"name": "magic_box", "type": "mesh", "uvs": [0.59819, 0.16979, 0.92923, 0.1926, 1, 0.24506, 1, 0.39531, 1, 0.56401, 1, 0.66447, 1, 0.82672, 0.76066, 0.99999, 0.12864, 0.99131, 0, 0.89028, 0, 0.74886, 0, 0.60911, 0, 0.4204, 0, 0.30665, 0.05822, 0.23594, 0.35398, 0, 0.51408, 0, 0.85597, 0.44777, 0.80713, 0.78764, 0.83691, 0.6386], "triangles": [17, 1, 2, 12, 13, 14, 17, 0, 1, 3, 17, 2, 15, 16, 0, 14, 15, 0, 12, 14, 0, 12, 0, 17, 17, 3, 4, 19, 11, 12, 17, 19, 12, 19, 17, 4, 19, 4, 5, 18, 10, 11, 19, 18, 11, 18, 19, 5, 18, 5, 6, 8, 9, 10, 18, 8, 10, 7, 8, 18, 7, 18, 6], "vertices": [2, 3, 439.45, -59.14, 0.85, 4, 89.96, -75, 0.15, 2, 3, 428.47, -218.19, 0.85, 4, 78.98, -234.06, 0.15, 2, 3, 398.95, -252.54, 0.85, 4, 49.46, -268.41, 0.15, 2, 3, 313.17, -253.64, 0.95, 4, -36.32, -269.51, 0.05, 1, 3, 216.85, -254.88, 1, 1, 3, 159.49, -255.62, 1, 2, 3, 66.86, -256.82, 0.85, 5, 51.96, -268.38, 0.15, 2, 3, -33.55, -143.22, 0.85, 5, -48.45, -154.78, 0.15, 2, 3, -32.5, 160.19, 0.85, 5, -47.4, 148.63, 0.15, 2, 3, 24.38, 222.68, 0.85, 5, 9.49, 211.12, 0.15, 1, 3, 105.13, 223.72, 1, 1, 3, 184.92, 224.74, 1, 2, 3, 292.67, 226.13, 0.95, 4, -56.83, 210.26, 0.05, 2, 3, 357.61, 226.97, 0.85, 4, 8.12, 211.1, 0.15, 2, 3, 398.34, 199.54, 0.85, 4, 48.85, 183.67, 0.15, 1, 3, 534.88, 59.32, 1, 1, 3, 535.87, -17.51, 1, 2, 3, 282.33, -184.9, 0.95, 4, -67.17, -200.77, 0.05, 1, 3, 87.98, -163.96, 1, 1, 3, 173.25, -177.16, 1], "hull": 17, "edges": [26, 28, 28, 30, 30, 32, 32, 0, 0, 2, 2, 4, 12, 14, 14, 16, 18, 16, 24, 26, 4, 6, 34, 6, 18, 20, 10, 12, 36, 10, 24, 34, 20, 36, 20, 22, 22, 24, 22, 38, 6, 8, 8, 10, 38, 8], "width": 480, "height": 571}}, "box/lucky_box2": {"box": {"name": "magic_box", "type": "mesh", "uvs": [0.59819, 0.16979, 0.92923, 0.1926, 1, 0.24506, 1, 0.39531, 1, 0.56401, 1, 0.66447, 1, 0.82672, 0.76066, 0.99999, 0.12864, 0.99131, 0, 0.89028, 0, 0.74886, 0, 0.60911, 0, 0.4204, 0, 0.30665, 0.05822, 0.23594, 0.35398, 0, 0.51408, 0, 0.85597, 0.44777, 0.80713, 0.78764, 0.83691, 0.6386], "triangles": [17, 1, 2, 12, 13, 14, 17, 0, 1, 3, 17, 2, 15, 16, 0, 14, 15, 0, 12, 14, 0, 12, 0, 17, 17, 3, 4, 19, 11, 12, 17, 19, 12, 19, 17, 4, 19, 4, 5, 18, 10, 11, 19, 18, 11, 18, 19, 5, 18, 5, 6, 8, 9, 10, 18, 8, 10, 7, 8, 18, 7, 18, 6], "vertices": [2, 3, 439.45, -59.14, 0.85, 4, 89.96, -75, 0.15, 2, 3, 428.47, -218.19, 0.85, 4, 78.98, -234.06, 0.15, 2, 3, 398.95, -252.54, 0.85, 4, 49.46, -268.41, 0.15, 2, 3, 313.17, -253.64, 0.95, 4, -36.32, -269.51, 0.05, 1, 3, 216.85, -254.88, 1, 1, 3, 159.49, -255.62, 1, 2, 3, 66.86, -256.82, 0.85, 5, 51.96, -268.38, 0.15, 2, 3, -33.55, -143.22, 0.85, 5, -48.45, -154.78, 0.15, 2, 3, -32.5, 160.19, 0.85, 5, -47.4, 148.63, 0.15, 2, 3, 24.38, 222.68, 0.85, 5, 9.49, 211.12, 0.15, 1, 3, 105.13, 223.72, 1, 1, 3, 184.92, 224.74, 1, 2, 3, 292.67, 226.13, 0.95, 4, -56.83, 210.26, 0.05, 2, 3, 357.61, 226.97, 0.85, 4, 8.12, 211.1, 0.15, 2, 3, 398.34, 199.54, 0.85, 4, 48.85, 183.67, 0.15, 1, 3, 534.88, 59.32, 1, 1, 3, 535.87, -17.51, 1, 2, 3, 282.33, -184.9, 0.95, 4, -67.17, -200.77, 0.05, 1, 3, 87.98, -163.96, 1, 1, 3, 173.25, -177.16, 1], "hull": 17, "edges": [26, 28, 28, 30, 30, 32, 32, 0, 0, 2, 2, 4, 12, 14, 14, 16, 18, 16, 24, 26, 4, 6, 34, 6, 18, 20, 10, 12, 36, 10, 24, 34, 20, 36, 20, 22, 22, 24, 22, 38, 6, 8, 8, 10, 38, 8], "width": 480, "height": 571}}}}, {"name": "box/pink", "attachments": {"box/lucky_box": {"box": {"name": "rainbow_box", "type": "mesh", "uvs": [0.59799, 0.16979, 0.92834, 0.1926, 0.99896, 0.24506, 0.99896, 0.39531, 0.99896, 0.56401, 0.99896, 0.66447, 0.99896, 0.82672, 0.76012, 0.99999, 0.12941, 0.99131, 0.00104, 0.89028, 0.00104, 0.74886, 0.00104, 0.60911, 0.00104, 0.4204, 0.00104, 0.30665, 0.05914, 0.23594, 0.35429, 0, 0.51405, 0, 0.85523, 0.44777, 0.80649, 0.78764, 0.83621, 0.6386], "triangles": [17, 1, 2, 12, 13, 14, 17, 0, 1, 3, 17, 2, 15, 16, 0, 14, 15, 0, 12, 14, 0, 12, 0, 17, 17, 3, 4, 19, 11, 12, 17, 19, 12, 19, 17, 4, 19, 4, 5, 18, 10, 11, 19, 18, 11, 18, 19, 5, 18, 5, 6, 8, 9, 10, 18, 8, 10, 7, 8, 18, 7, 18, 6], "vertices": [2, 3, 439.45, -59.14, 0.85, 4, 89.96, -75, 0.15, 2, 3, 428.47, -218.19, 0.85, 4, 78.98, -234.06, 0.15, 2, 3, 398.95, -252.54, 0.85, 4, 49.46, -268.41, 0.15, 2, 3, 313.17, -253.64, 0.95, 4, -36.32, -269.51, 0.05, 1, 3, 216.85, -254.88, 1, 1, 3, 159.49, -255.62, 1, 2, 3, 66.86, -256.82, 0.85, 5, 51.96, -268.38, 0.15, 2, 3, -33.55, -143.22, 0.85, 5, -48.45, -154.78, 0.15, 2, 3, -32.5, 160.19, 0.85, 5, -47.4, 148.63, 0.15, 2, 3, 24.38, 222.68, 0.85, 5, 9.49, 211.12, 0.15, 1, 3, 105.13, 223.72, 1, 1, 3, 184.92, 224.74, 1, 2, 3, 292.67, 226.13, 0.95, 4, -56.83, 210.26, 0.05, 2, 3, 357.61, 226.97, 0.85, 4, 8.12, 211.1, 0.15, 2, 3, 398.34, 199.54, 0.85, 4, 48.85, 183.67, 0.15, 1, 3, 534.88, 59.32, 1, 1, 3, 535.87, -17.51, 1, 2, 3, 282.33, -184.9, 0.95, 4, -67.17, -200.77, 0.05, 1, 3, 87.98, -163.96, 1, 1, 3, 173.25, -177.16, 1], "hull": 17, "edges": [26, 28, 28, 30, 30, 32, 32, 0, 0, 2, 2, 4, 12, 14, 14, 16, 18, 16, 24, 26, 4, 6, 34, 6, 18, 20, 10, 12, 36, 10, 24, 34, 20, 36, 20, 22, 22, 24, 22, 38, 6, 8, 8, 10, 38, 8], "width": 481, "height": 571}}, "box/lucky_box2": {"box": {"name": "rainbow_box", "type": "mesh", "uvs": [0.59799, 0.16979, 0.92834, 0.1926, 0.99896, 0.24506, 0.99896, 0.39531, 0.99896, 0.56401, 0.99896, 0.66447, 0.99896, 0.82672, 0.76012, 0.99999, 0.12941, 0.99131, 0.00104, 0.89028, 0.00104, 0.74886, 0.00104, 0.60911, 0.00104, 0.4204, 0.00104, 0.30665, 0.05914, 0.23594, 0.35429, 0, 0.51405, 0, 0.85523, 0.44777, 0.80649, 0.78764, 0.83621, 0.6386], "triangles": [17, 1, 2, 12, 13, 14, 17, 0, 1, 3, 17, 2, 15, 16, 0, 14, 15, 0, 12, 14, 0, 12, 0, 17, 17, 3, 4, 19, 11, 12, 17, 19, 12, 19, 17, 4, 19, 4, 5, 18, 10, 11, 19, 18, 11, 18, 19, 5, 18, 5, 6, 8, 9, 10, 18, 8, 10, 7, 8, 18, 7, 18, 6], "vertices": [2, 3, 439.45, -59.14, 0.85, 4, 89.96, -75, 0.15, 2, 3, 428.47, -218.19, 0.85, 4, 78.98, -234.06, 0.15, 2, 3, 398.95, -252.54, 0.85, 4, 49.46, -268.41, 0.15, 2, 3, 313.17, -253.64, 0.95, 4, -36.32, -269.51, 0.05, 1, 3, 216.85, -254.88, 1, 1, 3, 159.49, -255.62, 1, 2, 3, 66.86, -256.82, 0.85, 5, 51.96, -268.38, 0.15, 2, 3, -33.55, -143.22, 0.85, 5, -48.45, -154.78, 0.15, 2, 3, -32.5, 160.19, 0.85, 5, -47.4, 148.63, 0.15, 2, 3, 24.38, 222.68, 0.85, 5, 9.49, 211.12, 0.15, 1, 3, 105.13, 223.72, 1, 1, 3, 184.92, 224.74, 1, 2, 3, 292.67, 226.13, 0.95, 4, -56.83, 210.26, 0.05, 2, 3, 357.61, 226.97, 0.85, 4, 8.12, 211.1, 0.15, 2, 3, 398.34, 199.54, 0.85, 4, 48.85, 183.67, 0.15, 1, 3, 534.88, 59.32, 1, 1, 3, 535.87, -17.51, 1, 2, 3, 282.33, -184.9, 0.95, 4, -67.17, -200.77, 0.05, 1, 3, 87.98, -163.96, 1, 1, 3, 173.25, -177.16, 1], "hull": 17, "edges": [26, 28, 28, 30, 30, 32, 32, 0, 0, 2, 2, 4, 12, 14, 14, 16, 18, 16, 24, 26, 4, 6, 34, 6, 18, 20, 10, 12, 36, 10, 24, 34, 20, 36, 20, 22, 22, 24, 22, 38, 6, 8, 8, 10, 38, 8], "width": 481, "height": 571}}}}, {"name": "uni/uniBlue", "attachments": {"uni/green_glow_01": {"uni/green_glow_01": {"name": "uni/blue_glow_02", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [582, -302, -582, -302, -582, 302, 582, 302], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 582, "height": 302}}, "uni/green_glow_02": {"uni/green_glow_02": {"name": "uni/blue_glow_02", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [582, -302, -582, -302, -582, 302, 582, 302], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 582, "height": 302}}, "uni/green_ray": {"uni/green_ray": {"name": "uni/blue_ray", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 0, 1, 3], "vertices": [1344.05, 129.52, 1334.67, -174.34, -177.61, -127.66, -168.23, 176.19], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 304, "height": 1513}}, "uni/green_uni_01": {"uni/green_uni_01": {"name": "uni/blue_uni_01", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-33.7, -333.76, -33.7, 218.24, 551.3, 218.24, 551.3, -333.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 552, "height": 585}}, "uni/green_uni_02": {"uni/green_uni_02": {"name": "uni/blue_uni_02", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-33.7, -333.76, -33.7, 218.24, 551.3, 218.24, 551.3, -333.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 552, "height": 585}}, "uni/green_uni_03": {"uni/green_uni_03": {"name": "uni/blue_uni_03", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-33.7, -333.76, -33.7, 218.24, 551.3, 218.24, 551.3, -333.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 552, "height": 585}}, "uni/green_uni_04": {"uni/green_uni_04": {"name": "uni/blue_uni_04", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-33.7, -333.76, -33.7, 218.24, 551.3, 218.24, 551.3, -333.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 552, "height": 585}}, "uni/green_uni_05": {"uni/green_uni_05": {"name": "uni/blue_uni_05", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-33.7, -333.76, -33.7, 218.24, 551.3, 218.24, 551.3, -333.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 552, "height": 585}}}}, {"name": "uni/uniGreen", "attachments": {"uni/green_glow_01": {"uni/green_glow_01": {"name": "uni/green_glow_02", "type": "mesh", "path": "uni/green_glow_01", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [882.5, -970.5, -882.5, -970.5, -882.5, 970.5, 882.5, 970.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 662, "height": 728}}, "uni/green_glow_02": {"uni/green_glow_02": {"name": "uni/green_glow_03", "type": "mesh", "path": "uni/green_glow_02", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [583, -303, -583, -303, -583, 303, 583, 303], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 875, "height": 455}}, "uni/green_ray": {"uni/green_ray": {"name": "uni/green_ray2", "type": "mesh", "path": "uni/green_ray", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 0, 1, 3], "vertices": [1345.08, 130.49, 1335.64, -175.37, -178.64, -128.63, -169.2, 177.22], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 153, "height": 758}}, "uni/green_uni_01": {"uni/green_uni_01": {"name": "uni/green_uni_02", "type": "mesh", "path": "uni/green_uni_01", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.7, -334.76, -34.7, 219.24, 552.3, 219.24, 552.3, -334.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 554, "height": 587}}, "uni/green_uni_02": {"uni/green_uni_02": {"name": "uni/green_uni_03", "type": "mesh", "path": "uni/green_uni_02", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.7, -334.76, -34.7, 219.24, 552.3, 219.24, 552.3, -334.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 554, "height": 587}}, "uni/green_uni_03": {"uni/green_uni_03": {"name": "uni/green_uni_04", "type": "mesh", "path": "uni/green_uni_03", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.7, -334.76, -34.7, 219.24, 552.3, 219.24, 552.3, -334.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 554, "height": 587}}, "uni/green_uni_04": {"uni/green_uni_04": {"name": "uni/green_uni_05", "type": "mesh", "path": "uni/green_uni_04", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.7, -334.76, -34.7, 219.24, 552.3, 219.24, 552.3, -334.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 554, "height": 587}}, "uni/green_uni_05": {"uni/green_uni_05": {"name": "uni/green_uni_06", "type": "mesh", "path": "uni/green_uni_05", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-34.7, -334.76, -34.7, 219.24, 552.3, 219.24, 552.3, -334.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 554, "height": 587}}}}, {"name": "uni/uniViolet", "attachments": {"uni/green_glow_01": {"uni/green_glow_01": {"name": "uni/violet_glow_01", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [881.5, -969.5, -881.5, -969.5, -881.5, 969.5, 881.5, 969.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 882, "height": 970}}, "uni/green_glow_02": {"uni/green_glow_02": {"name": "uni/violet_glow_02", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [582, -302, -582, -302, -582, 302, 582, 302], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 1164, "height": 604}}, "uni/green_ray": {"uni/green_ray": {"name": "uni/violet_ray", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 0, 1, 3], "vertices": [1344.05, 129.52, 1334.67, -174.34, -177.61, -127.66, -168.23, 176.19], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 304, "height": 1513}}, "uni/green_uni_01": {"uni/green_uni_01": {"name": "uni/violet_uni_01", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-33.7, -333.76, -33.7, 218.24, 551.3, 218.24, 551.3, -333.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 552, "height": 585}}, "uni/green_uni_02": {"uni/green_uni_02": {"name": "uni/violet_uni_02", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-33.7, -333.76, -33.7, 218.24, 551.3, 218.24, 551.3, -333.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 552, "height": 585}}, "uni/green_uni_03": {"uni/green_uni_03": {"name": "uni/violet_uni_03", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-33.7, -333.76, -33.7, 218.24, 551.3, 218.24, 551.3, -333.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 552, "height": 585}}, "uni/green_uni_04": {"uni/green_uni_04": {"name": "uni/violet_uni_04", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-33.7, -333.76, -33.7, 218.24, 551.3, 218.24, 551.3, -333.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 552, "height": 585}}, "uni/green_uni_05": {"uni/green_uni_05": {"name": "uni/violet_uni_05", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-33.7, -333.76, -33.7, 218.24, 551.3, 218.24, 551.3, -333.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 552, "height": 585}}}}, {"name": "uni/uniY<PERSON>w", "attachments": {"uni/green_glow_01": {"uni/green_glow_01": {"name": "uni/yellow_glow_01", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1001.5, -1050, -1001.5, -1050, -1001.5, 1050, 1001.5, 1050], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 1002, "height": 1050}}, "uni/green_glow_02": {"uni/green_glow_02": {"name": "uni/yellow_glow_02", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [582, -302, -582, -302, -582, 302, 582, 302], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 1164, "height": 604}}, "uni/green_ray": {"uni/green_ray": {"name": "uni/yellow_ray", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 0, 1, 3], "vertices": [1344.05, 129.52, 1334.67, -174.34, -177.61, -127.66, -168.23, 176.19], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 304, "height": 1513}}, "uni/green_uni_01": {"uni/green_uni_01": {"name": "uni/yellow_uni_01", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-33.7, -333.76, -33.7, 218.24, 551.3, 218.24, 551.3, -333.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 552, "height": 585}}, "uni/green_uni_02": {"uni/green_uni_02": {"name": "uni/yellow_uni_02", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-33.7, -333.76, -33.7, 218.24, 551.3, 218.24, 551.3, -333.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 552, "height": 585}}, "uni/green_uni_03": {"uni/green_uni_03": {"name": "uni/yellow_uni_03", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-33.7, -333.76, -33.7, 218.24, 551.3, 218.24, 551.3, -333.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 552, "height": 585}}, "uni/green_uni_04": {"uni/green_uni_04": {"name": "uni/yellow_uni_04", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-33.7, -333.76, -33.7, 218.24, 551.3, 218.24, 551.3, -333.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 552, "height": 585}}, "uni/green_uni_05": {"uni/green_uni_05": {"name": "uni/yellow_uni_05", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-33.7, -333.76, -33.7, 218.24, 551.3, 218.24, 551.3, -333.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 552, "height": 585}}}}], "animations": {"action_gift": {"slots": {"white_screen": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00", "curve": [1.7, 1, 1.767, 1, 1.7, 1, 1.767, 1, 1.7, 1, 1.767, 1, 1.678, 0.71, 1.71, 0.95]}, {"time": 1.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": [2.1, 1, 2.4, 1, 2.1, 1, 2.4, 1, 2.1, 1, 2.4, 1, 2.1, 1, 2.4, 0]}, {"time": 2.5, "color": "ffffff00"}], "attachment": [{"name": "white_screen"}]}, "box/lucky_box": {"rgba": [{"color": "ffffff00", "curve": [0.011, 1, 0.022, 1, 0.011, 1, 0.022, 1, 0.011, 1, 0.022, 1, 0.011, 0, 0.005, 0.99]}, {"time": 0.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.7, "color": "ffffff00"}]}, "box/lucky_box2": {"rgba": [{"color": "ffffff00", "curve": [0.022, 1, 0.056, 1, 0.022, 1, 0.056, 1, 0.022, 1, 0.056, 1, 0.022, 0, 0.033, 1]}, {"time": 0.0667, "color": "ffffffff", "curve": [0.2, 1, 0.334, 1, 0.2, 1, 0.334, 1, 0.2, 1, 0.334, 1, 0.277, 1, 0.297, 0.17]}, {"time": 0.4667, "color": "ffffff00", "curve": [0.49, 1, 0.512, 1, 0.49, 1, 0.512, 1, 0.49, 1, 0.512, 1, 0.487, -0.02, 0.509, 0.53]}, {"time": 0.5333, "color": "ffffff88", "curve": [0.578, 1, 0.623, 1, 0.578, 1, 0.623, 1, 0.578, 1, 0.623, 1, 0.581, 0.53, 0.626, 0]}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": [1.278, 1, 1.322, 1, 1.278, 1, 1.322, 1, 1.278, 1, 1.322, 1, 1.278, 0.07, 1.322, 0.05]}, {"time": 1.3667, "color": "ffffff34", "curve": [1.422, 1, 1.478, 1, 1.422, 1, 1.478, 1, 1.422, 1, 1.478, 1, 1.422, 0.39, 1.478, 1]}, {"time": 1.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff", "curve": [1.678, 1, 1.689, 1, 1.678, 1, 1.689, 1, 1.678, 1, 1.689, 1, 1.678, 1, 1.689, 0]}, {"time": 1.7, "color": "ffffff00"}]}, "box/shadow": {"rgba": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffff7e"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffffbb"}, {"time": 0.8, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3667, "color": "ffffffff", "curve": [1.433, 1, 1.5, 1, 1.433, 1, 1.5, 1, 1.433, 1, 1.5, 1, 1.467, 1, 1.466, 0]}, {"time": 1.5667, "color": "ffffff00"}]}, "FX/ellipse_01": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.3667, "color": "ffffff00"}, {"time": 1.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7, "color": "ffffffff"}, {"time": 1.8, "color": "ffffff04"}], "attachment": [{"name": "ellipse_01"}]}, "FX/ellipse_01_glow_01": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.3667, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7, "color": "ffffffff"}, {"time": 1.8, "color": "ffffff00"}], "attachment": [{"name": "ellipse_01_glow_01"}]}, "FX/ellipse_01_glow_02": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.3667, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7, "color": "ffffffff"}, {"time": 1.8, "color": "ffffff00"}], "attachment": [{"name": "ellipse_01_glow_02"}]}, "FX/ellipse_02": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff00"}], "attachment": [{"time": 2, "name": "ellipse_02"}]}, "FX/ellipse_03": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}], "attachment": [{"time": 2, "name": "ellipse_03"}]}, "FX/ellipse_04": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1667, "color": "ffffff00"}, {"time": 2.3333, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00"}], "attachment": [{"time": 2, "name": "ellipse_03_01"}]}, "FX/ellipse_05": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00"}, {"time": 2.5, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}], "attachment": [{"time": 2, "name": "ellipse_03_02"}]}, "FX/ellips_stroke": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00", "curve": [1.589, 1, 1.611, 1, 1.589, 1, 1.611, 1, 1.589, 1, 1.611, 1, 1.589, 0, 1.611, 1]}, {"time": 1.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7, "color": "ffffffff", "curve": [1.733, 1, 1.767, 1, 1.733, 1, 1.767, 1, 1.733, 1, 1.767, 1, 1.733, 1, 1.767, 0]}, {"time": 1.8, "color": "ffffff00"}], "attachment": [{"name": "ellips_stroke"}]}, "FX/ellips_stroke_glow": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.3667, "color": "ffffff00"}, {"time": 1.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7, "color": "ffffffff"}, {"time": 1.7667, "color": "ffffff00"}], "attachment": [{"name": "ellips_stroke_glow"}]}, "rays/glow_01": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4333, "color": "ffffff00", "curve": [0.456, 1, 0.478, 1, 0.456, 1, 0.478, 1, 0.456, 1, 0.478, 1, 0.467, 0, 0.467, 0.42]}, {"time": 0.5, "color": "ffffff6b", "curve": "stepped"}, {"time": 1.3, "color": "ffffff6b", "curve": [1.327, 1, 1.35, 1, 1.327, 1, 1.35, 1, 1.327, 1, 1.35, 1, 1.327, 0.42, 1.349, 0.01]}, {"time": 1.3667, "color": "ffffff00", "curve": [1.497, 1, 1.418, 1, 1.497, 1, 1.418, 1, 1.497, 1, 1.418, 1, 1.487, -0.08, 1.394, 0]}, {"time": 1.4667, "color": "ffffff00", "curve": [1.5, 1, 1.533, 1, 1.5, 1, 1.533, 1, 1.5, 1, 1.533, 1, 1.5, 0, 1.533, 1]}, {"time": 1.5667, "color": "ffffffff", "curve": [1.644, 1, 1.722, 1, 1.644, 1, 1.722, 1, 1.644, 1, 1.722, 1, 1.644, 1, 1.722, 0]}, {"time": 1.8, "color": "ffffff00"}]}, "rays/rey_025": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.4667, "color": "ffffff00", "curve": [1.533, 1, 1.467, 1, 1.533, 1, 1.467, 1, 1.533, 1, 1.467, 1, 1.493, 0, 1.507, 1]}, {"time": 1.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1, "color": "ffffffff", "curve": [2.178, 1, 2.256, 1, 2.178, 1, 2.256, 1, 2.178, 1, 2.256, 1, 2.14, 1, 2.294, 0]}, {"time": 2.3333, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_027": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.5333, "color": "ffffff00", "curve": [1.6, 1, 1.533, 1, 1.6, 1, 1.533, 1, 1.6, 1, 1.533, 1, 1.56, 0, 1.574, 1]}, {"time": 1.6, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1333, "color": "ffffffff", "curve": [2.211, 1, 2.289, 1, 2.211, 1, 2.289, 1, 2.211, 1, 2.289, 1, 2.173, 1, 2.328, 0]}, {"time": 2.3667, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_029": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.4333, "color": "ffffff00", "curve": [1.5, 1, 1.433, 1, 1.5, 1, 1.433, 1, 1.5, 1, 1.433, 1, 1.46, 0, 1.474, 1]}, {"time": 1.5, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff", "curve": [2.1, 1, 2.267, 1, 2.1, 1, 2.267, 1, 2.1, 1, 2.267, 1, 2.106, 1, 2.261, 0]}, {"time": 2.3, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_031": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00", "curve": [1.567, 1, 1.5, 1, 1.567, 1, 1.5, 1, 1.567, 1, 1.5, 1, 1.526, 0, 1.541, 1]}, {"time": 1.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff", "curve": [2.244, 1, 2.322, 1, 2.244, 1, 2.322, 1, 2.244, 1, 2.322, 1, 2.206, 1, 2.361, 0]}, {"time": 2.4, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_033": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00", "curve": [1.7, 1, 1.633, 1, 1.7, 1, 1.633, 1, 1.7, 1, 1.633, 1, 1.66, 0, 1.674, 1]}, {"time": 1.7, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff", "curve": [2.144, 1, 2.222, 1, 2.144, 1, 2.222, 1, 2.144, 1, 2.222, 1, 2.106, 1, 2.261, 0]}, {"time": 2.3, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_035": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.4667, "color": "ffffff00", "curve": [1.533, 1, 1.467, 1, 1.533, 1, 1.467, 1, 1.533, 1, 1.467, 1, 1.493, 0, 1.507, 1]}, {"time": 1.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff", "curve": [2.244, 1, 2.322, 1, 2.244, 1, 2.322, 1, 2.244, 1, 2.322, 1, 2.206, 1, 2.361, 0]}, {"time": 2.4, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_037": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00", "curve": [1.567, 1, 1.5, 1, 1.567, 1, 1.5, 1, 1.567, 1, 1.5, 1, 1.526, 0, 1.541, 1]}, {"time": 1.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff", "curve": [2.144, 1, 2.222, 1, 2.144, 1, 2.222, 1, 2.144, 1, 2.222, 1, 2.106, 1, 2.261, 0]}, {"time": 2.3, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_039": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.4, "color": "ffffff00", "curve": [1.467, 1, 1.4, 1, 1.467, 1, 1.4, 1, 1.467, 1, 1.4, 1, 1.426, 0, 1.441, 1]}, {"time": 1.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff", "curve": [2.244, 1, 2.322, 1, 2.244, 1, 2.322, 1, 2.244, 1, 2.322, 1, 2.206, 1, 2.361, 0]}, {"time": 2.4, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_041": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00", "curve": [1.633, 1, 1.567, 1, 1.633, 1, 1.567, 1, 1.633, 1, 1.567, 1, 1.593, 0, 1.607, 1]}, {"time": 1.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0333, "color": "ffffffff", "curve": [2.111, 1, 2.189, 1, 2.111, 1, 2.189, 1, 2.111, 1, 2.189, 1, 2.073, 1, 2.228, 0]}, {"time": 2.2667, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_043": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.4667, "color": "ffffff00", "curve": [1.533, 1, 1.467, 1, 1.533, 1, 1.467, 1, 1.533, 1, 1.467, 1, 1.493, 0, 1.507, 1]}, {"time": 1.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1333, "color": "ffffffff", "curve": [2.211, 1, 2.289, 1, 2.211, 1, 2.289, 1, 2.211, 1, 2.289, 1, 2.173, 1, 2.328, 0]}, {"time": 2.3667, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_045": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.7, "color": "ffffff00", "curve": [1.767, 1, 1.7, 1, 1.767, 1, 1.7, 1, 1.767, 1, 1.7, 1, 1.726, 0, 1.741, 1]}, {"time": 1.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff", "curve": [2.144, 1, 2.222, 1, 2.144, 1, 2.222, 1, 2.144, 1, 2.222, 1, 2.106, 1, 2.261, 0]}, {"time": 2.3, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_047": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00", "curve": [1.567, 1, 1.5, 1, 1.567, 1, 1.5, 1, 1.567, 1, 1.5, 1, 1.526, 0, 1.541, 1]}, {"time": 1.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff", "curve": [2.244, 1, 2.322, 1, 2.244, 1, 2.322, 1, 2.244, 1, 2.322, 1, 2.206, 1, 2.361, 0]}, {"time": 2.4, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "sine/rays": {"rgba": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3667, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffff00"}], "attachment": [{"name": "rays"}]}, "stars/star": {"attachment": [{"name": "star"}]}, "stars/star2": {"attachment": [{"name": "star"}]}, "stars/star3": {"attachment": [{"name": "star"}]}, "stars/star4": {"attachment": [{"name": "star"}]}, "stars/star5": {"attachment": [{"name": "star"}]}, "stars/star6": {"attachment": [{"name": "star"}]}}, "bones": {"box": {"rotate": [{"curve": [0.044, 0, 0.089, -2.99]}, {"time": 0.1333, "value": -2.99, "curve": [0.211, -2.99, 0.289, 3.62]}, {"time": 0.3667, "value": 3.62, "curve": [0.433, 3.62, 0.5, -1.16]}, {"time": 0.5667, "value": -1.16, "curve": [0.622, -1.16, 0.678, 0.92]}, {"time": 0.7333, "value": 0.92, "curve": [0.756, 0.92, 0.778, 0]}, {"time": 0.8, "curve": "stepped"}, {"time": 1.2333, "curve": "stepped"}, {"time": 1.3667, "curve": [1.4, 0, 1.433, -1]}, {"time": 1.4667, "value": -1}], "translatex": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8, "curve": "stepped"}, {"time": 1.2333, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 1.7}], "translatey": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.054, 0, 0.093, 707.78]}, {"time": 0.2667, "value": 707.78, "curve": [0.461, 707.78, 0.508, 0]}, {"time": 0.5333, "curve": [0.557, 0, 0.574, 117.39]}, {"time": 0.6667, "value": 117.39, "curve": [0.759, 117.39, 0.757, 0]}, {"time": 0.8, "curve": "stepped"}, {"time": 1.2333, "curve": "stepped"}, {"time": 1.3667, "curve": [1.387, 0, 1.526, 475.62]}, {"time": 1.7, "value": 475.62}], "scale": [{"x": 0.3, "y": 1.7, "curve": [0.044, 0.667, 0.122, 1.4, 0.044, 1.333, 0.122, 0.6]}, {"time": 0.1667, "x": 1.4, "y": 0.6, "curve": "stepped"}, {"time": 0.4667, "x": 1.4, "y": 0.6, "curve": [0.478, 1.4, 0.522, 0.6, 0.478, 0.6, 0.522, 1.4]}, {"time": 0.5333, "x": 0.6, "y": 1.4, "curve": [0.6, 0.6, 0.6, 1.059, 0.6, 1.4, 0.6, 0.941]}, {"time": 0.6667, "x": 1.059, "y": 0.941, "curve": [0.733, 1.059, 0.733, 1.02, 0.733, 0.941, 0.733, 0.98]}, {"time": 0.8, "curve": "stepped"}, {"time": 1.2333}, {"time": 1.3667, "x": 0.3, "y": 1.7, "curve": [1.411, 0.667, 1.489, 1.4, 1.411, 1.333, 1.489, 0.6]}, {"time": 1.5333, "x": 1.4, "y": 0.6, "curve": [1.567, 1.4, 1.667, 1.1, 1.567, 0.6, 1.667, 1.1]}, {"time": 1.7, "x": 1.1, "y": 1.1}]}, "box_down": {"scale": [{"curve": [0.134, 1, 0.133, 3.843, 0.134, 1, 0.133, 3.843]}, {"time": 0.2667, "x": 3.843, "y": 3.843, "curve": [0.401, 3.843, 0.399, 1.966, 0.401, 3.843, 0.399, 1.966]}, {"time": 0.5333, "x": 1.966, "y": 1.966, "curve": [0.6, 1.966, 0.6, 1, 0.6, 1.966, 0.6, 1]}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.3667, "curve": [1.456, 1, 1.611, 0.388, 1.456, 1, 1.611, 0.388]}, {"time": 1.7, "x": 0.388, "y": 0.388}]}, "box_up": {"scale": [{"curve": [0.134, 1, 0.133, 3.843, 0.134, 1, 0.133, 3.843]}, {"time": 0.2667, "x": 3.843, "y": 3.843, "curve": [0.401, 3.843, 0.399, 1.966, 0.401, 3.843, 0.399, 1.966]}, {"time": 0.5333, "x": 1.966, "y": 1.966, "curve": [0.6, 1.966, 0.6, 1, 0.6, 1.966, 0.6, 1]}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.3667, "curve": [1.456, 1, 1.611, 0.388, 1.456, 1, 1.611, 0.388]}, {"time": 1.7, "x": 0.388, "y": 0.388}]}, "ray_main": {"rotate": [{}, {"time": 1.6333, "value": -38.07}], "scale": [{"x": 0.429, "y": 0.429, "curve": [0.2, 0.429, 0.21, 0.714, 0.2, 0.429, 0.21, 0.714]}, {"time": 0.4333, "x": 0.714, "y": 0.714, "curve": [0.484, 0.714, 0.483, 1, 0.484, 0.714, 0.483, 1]}, {"time": 0.5333}]}, "glow": {"scale": [{"x": 0.4, "y": 0.4, "curve": "stepped"}, {"time": 0.4333, "x": 0.4, "y": 0.4, "curve": [0.511, 0.4, 0.499, 1, 0.511, 0.4, 0.499, 1]}, {"time": 0.6667, "curve": [0.834, 1, 0.733, 0.8, 0.834, 1, 0.733, 0.8]}, {"time": 0.9, "x": 0.8, "y": 0.8, "curve": [1.067, 0.8, 0.966, 1, 1.067, 0.8, 0.966, 1]}, {"time": 1.1333, "curve": [1.301, 1, 1.299, 0.8, 1.301, 1, 1.299, 0.8]}, {"time": 1.4667, "x": 0.8, "y": 0.8}]}, "ray_00": {"rotate": [{"curve": "stepped"}, {"time": 1.3667}, {"time": 2.6667, "value": -63.03}], "translate": [{"time": 2, "curve": "stepped"}, {"time": 2.0333, "y": 124.9}], "scale": [{"time": 1.3667, "curve": [1.911, 1, 2.122, 2.143, 1.911, 1, 2.122, 2.143]}, {"time": 2.6667, "x": 2.143, "y": 2.143}]}, "star": {"rotate": [{"curve": [0.201, 0, 0.399, -56.11]}, {"time": 0.6, "value": -56.11}], "translate": [{"curve": [0.2, 0, 0.4, 91.57, 0.2, 0, 0.4, 31.22]}, {"time": 0.6, "x": 91.57, "y": 31.22}], "scale": [{"x": 0.02, "y": 0.02, "curve": [0.038, 0.02, 0.149, 1, 0.038, 0.02, 0.149, 1]}, {"time": 0.3, "curve": [0.457, 1, 0.561, 0.02, 0.457, 1, 0.561, 0.02]}, {"time": 0.6, "x": 0.02, "y": 0.02}]}, "star2": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.234, 0, 0.432, -56.11]}, {"time": 0.6333, "value": -56.11}], "translate": [{"time": 0.0333, "curve": [0.233, 0, 0.433, -54.11, 0.233, 0, 0.433, 49.95]}, {"time": 0.6333, "x": -54.11, "y": 49.95}], "scale": [{"x": 0.02, "y": 0.02, "curve": "stepped"}, {"time": 0.0333, "x": 0.02, "y": 0.02, "curve": [0.071, 0.02, 0.182, 1, 0.071, 0.02, 0.182, 1]}, {"time": 0.3333, "curve": [0.49, 1, 0.594, 0.02, 0.49, 1, 0.594, 0.02]}, {"time": 0.6333, "x": 0.02, "y": 0.02}]}, "star3": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": [0.334, 0, 0.532, -56.11]}, {"time": 0.7333, "value": -56.11}], "translate": [{"time": 0.1333, "curve": [0.333, 0, 0.533, 61.67, 0.333, 0, 0.533, -21.59]}, {"time": 0.7333, "x": 61.67, "y": -21.59}], "scale": [{"x": 0.02, "y": 0.02, "curve": "stepped"}, {"time": 0.1333, "x": 0.02, "y": 0.02, "curve": [0.171, 0.02, 0.282, 1, 0.171, 0.02, 0.282, 1]}, {"time": 0.4333, "curve": [0.59, 1, 0.694, 0.02, 0.59, 1, 0.694, 0.02]}, {"time": 0.7333, "x": 0.02, "y": 0.02}]}, "star4": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": [0.368, 0, 0.566, -56.11]}, {"time": 0.7667, "value": -56.11}], "translate": [{"time": 0.1667, "curve": [0.367, 0, 0.567, 24.67, 0.367, 0, 0.567, -83.26]}, {"time": 0.7667, "x": 24.67, "y": -83.26}], "scale": [{"x": 0.02, "y": 0.02, "curve": "stepped"}, {"time": 0.1667, "x": 0.02, "y": 0.02, "curve": [0.205, 0.02, 0.316, 1, 0.205, 0.02, 0.316, 1]}, {"time": 0.4667, "curve": [0.624, 1, 0.728, 0.02, 0.624, 1, 0.728, 0.02]}, {"time": 0.7667, "x": 0.02, "y": 0.02}]}, "star5": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.434, 0, 0.632, -56.11]}, {"time": 0.8333, "value": -56.11}], "translate": [{"time": 0.2333, "curve": [0.433, 0, 0.633, 3.08, 0.433, 0, 0.633, -49.34]}, {"time": 0.8333, "x": 3.08, "y": -49.34}], "scale": [{"x": 0.02, "y": 0.02, "curve": "stepped"}, {"time": 0.2333, "x": 0.02, "y": 0.02, "curve": [0.271, 0.02, 0.382, 1, 0.271, 0.02, 0.382, 1]}, {"time": 0.5333, "curve": [0.69, 1, 0.794, 0.02, 0.69, 1, 0.794, 0.02]}, {"time": 0.8333, "x": 0.02, "y": 0.02}]}, "star6": {"rotate": [{"curve": "stepped"}, {"time": 0.3, "curve": [0.501, 0, 0.699, -56.11]}, {"time": 0.9, "value": -56.11}], "translate": [{"time": 0.3, "curve": [0.5, 0, 0.7, -58.59, 0.5, 0, 0.7, -74.01]}, {"time": 0.9, "x": -58.59, "y": -74.01}], "scale": [{"x": 0.02, "y": 0.02, "curve": "stepped"}, {"time": 0.3, "x": 0.02, "y": 0.02, "curve": [0.338, 0.02, 0.449, 1, 0.338, 0.02, 0.449, 1]}, {"time": 0.6, "curve": [0.757, 1, 0.861, 0.02, 0.757, 1, 0.861, 0.02]}, {"time": 0.9, "x": 0.02, "y": 0.02}]}, "b1_elipse": {"scale": [{"x": 0.1, "y": 0.1, "curve": "stepped"}, {"time": 1.3667, "x": 0.1, "y": 0.1, "curve": [1.651, 0.1, 1.549, 4, 1.651, 0.1, 1.549, 4]}, {"time": 1.8333, "x": 4, "y": 4}]}, "b2_stroke": {"scale": [{"x": 0.1, "y": 0.1, "curve": "stepped"}, {"time": 1.4333, "x": 0.1, "y": 0.1, "curve": [1.718, 0.1, 1.549, 4, 1.718, 0.1, 1.549, 4]}, {"time": 1.8333, "x": 4, "y": 4}]}, "b1_stroke_glow": {"scale": [{"x": 0.1, "y": 0.1, "curve": "stepped"}, {"time": 1.3667, "x": 0.1, "y": 0.1, "curve": [1.651, 0.1, 1.482, 4, 1.651, 0.1, 1.482, 4]}, {"time": 1.7667, "x": 4, "y": 4}]}, "boom": {"translate": [{"y": -581.86, "curve": "stepped"}, {"time": 1.3, "y": -581.86, "curve": [1.367, 0, 1.5, 0, 1.4, -581.86, 1.466, 0]}, {"time": 1.5667}]}, "r25": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.4667, "x": 0.24, "curve": [1.523, 0.713, 1.616, 0.933, 1.5, 1, 1.8, 1]}, {"time": 1.8333}]}, "r27": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.5333, "x": 0.24, "curve": [1.589, 0.713, 1.683, 0.933, 1.567, 1, 1.867, 1]}, {"time": 1.9}]}, "r29": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.4333, "x": 0.24, "curve": [1.489, 0.713, 1.583, 0.933, 1.467, 1, 1.767, 1]}, {"time": 1.8}]}, "r31": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.5, "x": 0.24, "curve": [1.556, 0.713, 1.65, 0.933, 1.533, 1, 1.833, 1]}, {"time": 1.8667}]}, "r33": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.6333, "x": 0.24, "curve": [1.689, 0.713, 1.783, 0.933, 1.667, 1, 1.967, 1]}, {"time": 2}]}, "r35": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.4667, "x": 0.24, "curve": [1.523, 0.713, 2.183, 0.933, 1.5, 1, 2.367, 1]}, {"time": 2.4}]}, "r37": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.5, "x": 0.24, "curve": [1.556, 0.713, 1.65, 0.933, 1.533, 1, 1.833, 1]}, {"time": 1.8667}]}, "r39": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.4, "x": 0.24, "curve": [1.456, 0.713, 1.55, 0.933, 1.433, 1, 1.733, 1]}, {"time": 1.7667}]}, "r41": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.5667, "x": 0.24, "curve": [1.623, 0.713, 1.716, 0.933, 1.6, 1, 1.9, 1]}, {"time": 1.9333}]}, "r43": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.4667, "x": 0.24, "curve": [1.523, 0.713, 1.616, 0.933, 1.5, 1, 1.8, 1]}, {"time": 1.8333}]}, "r45": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.7, "x": 0.24, "curve": [1.756, 0.713, 1.85, 0.933, 1.733, 1, 2.033, 1]}, {"time": 2.0667}]}, "r47": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.5, "x": 0.24, "curve": [1.556, 0.713, 1.65, 0.933, 1.533, 1, 1.833, 1]}, {"time": 1.8667}]}, "box_shadow": {"scale": [{"curve": [0.067, 0.868, 0.1, 0.608, 0.067, 0.868, 0.1, 0.608]}, {"time": 0.3333, "x": 0.6, "y": 0.6, "curve": [0.451, 0.596, 0.493, 0.868, 0.451, 0.596, 0.493, 0.868]}, {"time": 0.5333, "curve": [0.56, 0.967, 0.622, 0.9, 0.56, 0.967, 0.622, 0.9]}, {"time": 0.6667, "x": 0.9, "y": 0.9, "curve": [0.711, 0.9, 0.773, 0.967, 0.711, 0.9, 0.773, 0.967]}, {"time": 0.8, "curve": "stepped"}, {"time": 1.3667, "curve": [1.467, 1, 1.466, 0.7, 1.467, 1, 1.466, 0.7]}, {"time": 1.5667, "x": 0.7, "y": 0.7}]}, "ellipse": {"scale": [{"time": 2, "curve": [2.167, 1, 2.5, 1.5, 2.167, 1, 2.5, 1.5]}, {"time": 2.6667, "x": 1.5, "y": 1.5}]}}}, "action_uni": {"slots": {"white_screen": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00", "curve": [1.7, 1, 1.767, 1, 1.7, 1, 1.767, 1, 1.7, 1, 1.767, 1, 1.678, 0.71, 1.71, 0.95]}, {"time": 1.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": [2.1, 1, 2.4, 1, 2.1, 1, 2.4, 1, 2.1, 1, 2.4, 1, 2.1, 1, 2.4, 0]}, {"time": 2.5, "color": "ffffff00"}], "attachment": [{"name": "white_screen"}]}, "box/lucky_box": {"rgba": [{"color": "ffffff00", "curve": [0.011, 1, 0.022, 1, 0.011, 1, 0.022, 1, 0.011, 1, 0.022, 1, 0.011, 0, 0.005, 0.99]}, {"time": 0.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.7, "color": "ffffff00"}]}, "box/lucky_box2": {"rgba": [{"color": "ffffff00", "curve": [0.022, 1, 0.056, 1, 0.022, 1, 0.056, 1, 0.022, 1, 0.056, 1, 0.022, 0, 0.033, 1]}, {"time": 0.0667, "color": "ffffffff", "curve": [0.2, 1, 0.334, 1, 0.2, 1, 0.334, 1, 0.2, 1, 0.334, 1, 0.277, 1, 0.297, 0.17]}, {"time": 0.4667, "color": "ffffff00", "curve": [0.49, 1, 0.512, 1, 0.49, 1, 0.512, 1, 0.49, 1, 0.512, 1, 0.487, -0.02, 0.509, 0.53]}, {"time": 0.5333, "color": "ffffff88", "curve": [0.578, 1, 0.623, 1, 0.578, 1, 0.623, 1, 0.578, 1, 0.623, 1, 0.581, 0.53, 0.626, 0]}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00", "curve": [1.278, 1, 1.322, 1, 1.278, 1, 1.322, 1, 1.278, 1, 1.322, 1, 1.278, 0.07, 1.322, 0.05]}, {"time": 1.3667, "color": "ffffff34", "curve": [1.422, 1, 1.478, 1, 1.422, 1, 1.478, 1, 1.422, 1, 1.478, 1, 1.422, 0.39, 1.478, 1]}, {"time": 1.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff", "curve": [1.678, 1, 1.689, 1, 1.678, 1, 1.689, 1, 1.678, 1, 1.689, 1, 1.678, 1, 1.689, 0]}, {"time": 1.7, "color": "ffffff00"}]}, "box/shadow": {"rgba": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffff7e"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffffbb"}, {"time": 0.8, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3667, "color": "ffffffff", "curve": [1.433, 1, 1.5, 1, 1.433, 1, 1.5, 1, 1.433, 1, 1.5, 1, 1.467, 1, 1.466, 0]}, {"time": 1.5667, "color": "ffffff00"}]}, "FX/ellipse_01": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.3667, "color": "ffffff00"}, {"time": 1.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7, "color": "ffffffff"}, {"time": 1.8, "color": "ffffff04"}], "attachment": [{"name": "ellipse_01"}]}, "FX/ellipse_01_glow_01": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.3667, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7, "color": "ffffffff"}, {"time": 1.8, "color": "ffffff00"}], "attachment": [{"name": "ellipse_01_glow_01"}]}, "FX/ellipse_01_glow_02": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.3667, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7, "color": "ffffffff"}, {"time": 1.8, "color": "ffffff00"}], "attachment": [{"name": "ellipse_01_glow_02"}]}, "FX/ellipse_02": {"rgba": [{"color": "ffffffff"}]}, "FX/ellipse_03": {"rgba": [{"color": "ffffff00"}]}, "FX/ellipse_04": {"rgba": [{"color": "ffffff00"}]}, "FX/ellipse_05": {"rgba": [{"color": "ffffff00"}]}, "FX/ellips_stroke": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00", "curve": [1.589, 1, 1.611, 1, 1.589, 1, 1.611, 1, 1.589, 1, 1.611, 1, 1.589, 0, 1.611, 1]}, {"time": 1.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7, "color": "ffffffff", "curve": [1.733, 1, 1.767, 1, 1.733, 1, 1.767, 1, 1.733, 1, 1.767, 1, 1.733, 1, 1.767, 0]}, {"time": 1.8, "color": "ffffff00"}], "attachment": [{"name": "ellips_stroke"}]}, "FX/ellips_stroke_glow": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.3667, "color": "ffffff00"}, {"time": 1.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7, "color": "ffffffff"}, {"time": 1.7667, "color": "ffffff00"}], "attachment": [{"name": "ellips_stroke_glow"}]}, "rays/glow_01": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4333, "color": "ffffff00", "curve": [0.456, 1, 0.478, 1, 0.456, 1, 0.478, 1, 0.456, 1, 0.478, 1, 0.467, 0, 0.467, 0.42]}, {"time": 0.5, "color": "ffffff6b", "curve": "stepped"}, {"time": 1.3, "color": "ffffff6b", "curve": [1.327, 1, 1.35, 1, 1.327, 1, 1.35, 1, 1.327, 1, 1.35, 1, 1.327, 0.42, 1.349, 0.01]}, {"time": 1.3667, "color": "ffffff00", "curve": [1.497, 1, 1.418, 1, 1.497, 1, 1.418, 1, 1.497, 1, 1.418, 1, 1.487, -0.08, 1.394, 0]}, {"time": 1.4667, "color": "ffffff00", "curve": [1.5, 1, 1.533, 1, 1.5, 1, 1.533, 1, 1.5, 1, 1.533, 1, 1.5, 0, 1.533, 1]}, {"time": 1.5667, "color": "ffffffff", "curve": [1.644, 1, 1.722, 1, 1.644, 1, 1.722, 1, 1.644, 1, 1.722, 1, 1.644, 1, 1.722, 0]}, {"time": 1.8, "color": "ffffff00"}]}, "rays/rey_025": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.4667, "color": "ffffff00", "curve": [1.533, 1, 1.467, 1, 1.533, 1, 1.467, 1, 1.533, 1, 1.467, 1, 1.493, 0, 1.507, 1]}, {"time": 1.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1, "color": "ffffffff", "curve": [2.178, 1, 2.256, 1, 2.178, 1, 2.256, 1, 2.178, 1, 2.256, 1, 2.14, 1, 2.294, 0]}, {"time": 2.3333, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_027": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.5333, "color": "ffffff00", "curve": [1.6, 1, 1.533, 1, 1.6, 1, 1.533, 1, 1.6, 1, 1.533, 1, 1.56, 0, 1.574, 1]}, {"time": 1.6, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1333, "color": "ffffffff", "curve": [2.211, 1, 2.289, 1, 2.211, 1, 2.289, 1, 2.211, 1, 2.289, 1, 2.173, 1, 2.328, 0]}, {"time": 2.3667, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_029": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.4333, "color": "ffffff00", "curve": [1.5, 1, 1.433, 1, 1.5, 1, 1.433, 1, 1.5, 1, 1.433, 1, 1.46, 0, 1.474, 1]}, {"time": 1.5, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff", "curve": [2.1, 1, 2.267, 1, 2.1, 1, 2.267, 1, 2.1, 1, 2.267, 1, 2.106, 1, 2.261, 0]}, {"time": 2.3, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_031": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00", "curve": [1.567, 1, 1.5, 1, 1.567, 1, 1.5, 1, 1.567, 1, 1.5, 1, 1.526, 0, 1.541, 1]}, {"time": 1.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff", "curve": [2.244, 1, 2.322, 1, 2.244, 1, 2.322, 1, 2.244, 1, 2.322, 1, 2.206, 1, 2.361, 0]}, {"time": 2.4, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_033": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00", "curve": [1.7, 1, 1.633, 1, 1.7, 1, 1.633, 1, 1.7, 1, 1.633, 1, 1.66, 0, 1.674, 1]}, {"time": 1.7, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff", "curve": [2.144, 1, 2.222, 1, 2.144, 1, 2.222, 1, 2.144, 1, 2.222, 1, 2.106, 1, 2.261, 0]}, {"time": 2.3, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_035": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.4667, "color": "ffffff00", "curve": [1.533, 1, 1.467, 1, 1.533, 1, 1.467, 1, 1.533, 1, 1.467, 1, 1.493, 0, 1.507, 1]}, {"time": 1.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff", "curve": [2.244, 1, 2.322, 1, 2.244, 1, 2.322, 1, 2.244, 1, 2.322, 1, 2.206, 1, 2.361, 0]}, {"time": 2.4, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_037": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00", "curve": [1.567, 1, 1.5, 1, 1.567, 1, 1.5, 1, 1.567, 1, 1.5, 1, 1.526, 0, 1.541, 1]}, {"time": 1.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff", "curve": [2.144, 1, 2.222, 1, 2.144, 1, 2.222, 1, 2.144, 1, 2.222, 1, 2.106, 1, 2.261, 0]}, {"time": 2.3, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_039": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.4, "color": "ffffff00", "curve": [1.467, 1, 1.4, 1, 1.467, 1, 1.4, 1, 1.467, 1, 1.4, 1, 1.426, 0, 1.441, 1]}, {"time": 1.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff", "curve": [2.244, 1, 2.322, 1, 2.244, 1, 2.322, 1, 2.244, 1, 2.322, 1, 2.206, 1, 2.361, 0]}, {"time": 2.4, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_041": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00", "curve": [1.633, 1, 1.567, 1, 1.633, 1, 1.567, 1, 1.633, 1, 1.567, 1, 1.593, 0, 1.607, 1]}, {"time": 1.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0333, "color": "ffffffff", "curve": [2.111, 1, 2.189, 1, 2.111, 1, 2.189, 1, 2.111, 1, 2.189, 1, 2.073, 1, 2.228, 0]}, {"time": 2.2667, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_043": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.4667, "color": "ffffff00", "curve": [1.533, 1, 1.467, 1, 1.533, 1, 1.467, 1, 1.533, 1, 1.467, 1, 1.493, 0, 1.507, 1]}, {"time": 1.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1333, "color": "ffffffff", "curve": [2.211, 1, 2.289, 1, 2.211, 1, 2.289, 1, 2.211, 1, 2.289, 1, 2.173, 1, 2.328, 0]}, {"time": 2.3667, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_045": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.7, "color": "ffffff00", "curve": [1.767, 1, 1.7, 1, 1.767, 1, 1.7, 1, 1.767, 1, 1.7, 1, 1.726, 0, 1.741, 1]}, {"time": 1.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff", "curve": [2.144, 1, 2.222, 1, 2.144, 1, 2.222, 1, 2.144, 1, 2.222, 1, 2.106, 1, 2.261, 0]}, {"time": 2.3, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "rays/rey_047": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00", "curve": [1.567, 1, 1.5, 1, 1.567, 1, 1.5, 1, 1.567, 1, 1.5, 1, 1.526, 0, 1.541, 1]}, {"time": 1.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1667, "color": "ffffffff", "curve": [2.244, 1, 2.322, 1, 2.244, 1, 2.322, 1, 2.244, 1, 2.322, 1, 2.206, 1, 2.361, 0]}, {"time": 2.4, "color": "ffffff00"}], "attachment": [{"name": "rey_01"}]}, "sine/rays": {"rgba": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3667, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffff00"}], "attachment": [{"name": "rays"}]}, "stars/star": {"attachment": [{"name": "star"}]}, "stars/star2": {"attachment": [{"name": "star"}]}, "stars/star3": {"attachment": [{"name": "star"}]}, "stars/star4": {"attachment": [{"name": "star"}]}, "stars/star5": {"attachment": [{"name": "star"}]}, "stars/star6": {"attachment": [{"name": "star"}]}, "stars/star7": {"attachment": [{"time": 2.4333, "name": "star"}]}, "stars/star8": {"attachment": [{"time": 2.4667, "name": "star"}]}, "stars/star9": {"attachment": [{"time": 2.5667, "name": "star"}]}, "stars/star10": {"attachment": [{"time": 2.6, "name": "star"}]}, "stars/star11": {"attachment": [{"time": 2.6667, "name": "star"}]}, "stars/star12": {"attachment": [{"time": 2.7333, "name": "star"}]}, "uni/green_glow_01": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2.5, "color": "ffffff00", "curve": [2.511, 1, 2.522, 1, 2.511, 1, 2.522, 1, 2.511, 1, 2.522, 1, 2.517, 0, 2.517, 1]}, {"time": 2.5333, "color": "ffffffff", "curve": [2.689, 1, 2.844, 1, 2.689, 1, 2.844, 1, 2.689, 1, 2.844, 1, 2.768, 1, 2.766, 0]}, {"time": 3, "color": "ffffff00"}], "attachment": [{"name": "uni/green_glow_01"}]}, "uni/green_glow_02": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2.4, "color": "ffffff00", "curve": [2.411, 1, 2.522, 1, 2.411, 1, 2.522, 1, 2.411, 1, 2.522, 1, 2.453, 0, 2.481, 1]}, {"time": 2.5333, "color": "ffffffff", "curve": [2.689, 1, 2.711, 1, 2.689, 1, 2.711, 1, 2.689, 1, 2.711, 1, 2.666, 1, 2.736, 0]}, {"time": 2.8667, "color": "ffffff00"}], "attachment": [{"name": "uni/green_glow_02"}, {"time": 3.3333, "name": "uni/green_glow_02"}]}, "uni/green_ray": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2.3667, "color": "ffffff00"}, {"time": 2.4, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"name": "uni/green_ray"}, {"time": 3.3333, "name": "uni/green_ray"}]}, "uni/green_uni_01": {"rgba": [{"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5333, "color": "ffffffff"}, {"time": 2.6333, "color": "ffffff00"}], "attachment": [{}, {"time": 2, "name": "uni/green_uni_01"}]}, "uni/green_uni_02": {"rgba": [{"time": 2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.5333, "color": "ffffff00"}, {"time": 2.6333, "color": "ffffffff"}, {"time": 2.7333, "color": "ffffff00"}], "attachment": [{}, {"time": 2, "name": "uni/green_uni_02"}]}, "uni/green_uni_03": {"rgba": [{"time": 2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6333, "color": "ffffff00"}, {"time": 2.7333, "color": "ffffffff"}, {"time": 2.8333, "color": "ffffff00"}], "attachment": [{}, {"time": 2, "name": "uni/green_uni_03"}]}, "uni/green_uni_04": {"rgba": [{"time": 2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.7333, "color": "ffffff00"}, {"time": 2.8333, "color": "ffffffff"}, {"time": 2.9333, "color": "ffffff00"}], "attachment": [{}, {"time": 2, "name": "uni/green_uni_04"}]}, "uni/green_uni_05": {"rgba": [{"time": 2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8333, "color": "ffffff00"}, {"time": 2.9333, "color": "ffffffff"}, {"time": 3.0333, "color": "ffffff00"}], "attachment": [{}, {"time": 2, "name": "uni/green_uni_05"}]}}, "bones": {"box": {"rotate": [{"curve": [0.044, 0, 0.089, -2.99]}, {"time": 0.1333, "value": -2.99, "curve": [0.211, -2.99, 0.289, 3.62]}, {"time": 0.3667, "value": 3.62, "curve": [0.433, 3.62, 0.5, -1.16]}, {"time": 0.5667, "value": -1.16, "curve": [0.622, -1.16, 0.678, 0.92]}, {"time": 0.7333, "value": 0.92, "curve": [0.756, 0.92, 0.778, 0]}, {"time": 0.8, "curve": "stepped"}, {"time": 1.2333, "curve": "stepped"}, {"time": 1.3667, "curve": [1.4, 0, 1.433, -1]}, {"time": 1.4667, "value": -1}], "translatex": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8, "curve": "stepped"}, {"time": 1.2333, "curve": "stepped"}, {"time": 1.3667, "curve": "stepped"}, {"time": 1.7}], "translatey": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.054, 0, 0.093, 707.78]}, {"time": 0.2667, "value": 707.78, "curve": [0.461, 707.78, 0.508, 0]}, {"time": 0.5333, "curve": [0.557, 0, 0.574, 117.39]}, {"time": 0.6667, "value": 117.39, "curve": [0.759, 117.39, 0.757, 0]}, {"time": 0.8, "curve": "stepped"}, {"time": 1.2333, "curve": "stepped"}, {"time": 1.3667, "curve": [1.387, 0, 1.526, 475.62]}, {"time": 1.7, "value": 475.62}], "scale": [{"x": 0.3, "y": 1.7, "curve": [0.044, 0.667, 0.122, 1.4, 0.044, 1.333, 0.122, 0.6]}, {"time": 0.1667, "x": 1.4, "y": 0.6, "curve": "stepped"}, {"time": 0.4667, "x": 1.4, "y": 0.6, "curve": [0.478, 1.4, 0.522, 0.6, 0.478, 0.6, 0.522, 1.4]}, {"time": 0.5333, "x": 0.6, "y": 1.4, "curve": [0.6, 0.6, 0.6, 1.059, 0.6, 1.4, 0.6, 0.941]}, {"time": 0.6667, "x": 1.059, "y": 0.941, "curve": [0.733, 1.059, 0.733, 1.02, 0.733, 0.941, 0.733, 0.98]}, {"time": 0.8, "curve": "stepped"}, {"time": 1.2333}, {"time": 1.3667, "x": 0.3, "y": 1.7, "curve": [1.411, 0.667, 1.489, 1.4, 1.411, 1.333, 1.489, 0.6]}, {"time": 1.5333, "x": 1.4, "y": 0.6, "curve": [1.567, 1.4, 1.667, 1.1, 1.567, 0.6, 1.667, 1.1]}, {"time": 1.7, "x": 1.1, "y": 1.1}]}, "box_down": {"scale": [{"curve": [0.134, 1, 0.133, 3.843, 0.134, 1, 0.133, 3.843]}, {"time": 0.2667, "x": 3.843, "y": 3.843, "curve": [0.401, 3.843, 0.399, 1.966, 0.401, 3.843, 0.399, 1.966]}, {"time": 0.5333, "x": 1.966, "y": 1.966, "curve": [0.6, 1.966, 0.6, 1, 0.6, 1.966, 0.6, 1]}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.3667, "curve": [1.456, 1, 1.611, 0.388, 1.456, 1, 1.611, 0.388]}, {"time": 1.7, "x": 0.388, "y": 0.388}]}, "box_up": {"scale": [{"curve": [0.134, 1, 0.133, 3.843, 0.134, 1, 0.133, 3.843]}, {"time": 0.2667, "x": 3.843, "y": 3.843, "curve": [0.401, 3.843, 0.399, 1.966, 0.401, 3.843, 0.399, 1.966]}, {"time": 0.5333, "x": 1.966, "y": 1.966, "curve": [0.6, 1.966, 0.6, 1, 0.6, 1.966, 0.6, 1]}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.3667, "curve": [1.456, 1, 1.611, 0.388, 1.456, 1, 1.611, 0.388]}, {"time": 1.7, "x": 0.388, "y": 0.388}]}, "ray_main": {"rotate": [{}, {"time": 1.6333, "value": -38.07}], "scale": [{"x": 0.429, "y": 0.429, "curve": [0.2, 0.429, 0.21, 0.714, 0.2, 0.429, 0.21, 0.714]}, {"time": 0.4333, "x": 0.714, "y": 0.714, "curve": [0.484, 0.714, 0.483, 1, 0.484, 0.714, 0.483, 1]}, {"time": 0.5333}]}, "glow": {"scale": [{"x": 0.4, "y": 0.4, "curve": "stepped"}, {"time": 0.4333, "x": 0.4, "y": 0.4, "curve": [0.511, 0.4, 0.499, 1, 0.511, 0.4, 0.499, 1]}, {"time": 0.6667, "curve": [0.834, 1, 0.733, 0.8, 0.834, 1, 0.733, 0.8]}, {"time": 0.9, "x": 0.8, "y": 0.8, "curve": [1.067, 0.8, 0.966, 1, 1.067, 0.8, 0.966, 1]}, {"time": 1.1333, "curve": [1.301, 1, 1.299, 0.8, 1.301, 1, 1.299, 0.8]}, {"time": 1.4667, "x": 0.8, "y": 0.8}]}, "ray_00": {"rotate": [{"curve": "stepped"}, {"time": 1.3667}, {"time": 2.6667, "value": -63.03}], "translate": [{"time": 2, "curve": "stepped"}, {"time": 2.0333, "y": 124.9}], "scale": [{"time": 1.3667, "curve": [1.911, 1, 2.122, 2.143, 1.911, 1, 2.122, 2.143]}, {"time": 2.6667, "x": 2.143, "y": 2.143}]}, "star": {"rotate": [{"curve": [0.201, 0, 0.399, -56.11]}, {"time": 0.6, "value": -56.11}], "translate": [{"curve": [0.2, 0, 0.4, 91.57, 0.2, 0, 0.4, 31.22]}, {"time": 0.6, "x": 91.57, "y": 31.22}], "scale": [{"x": 0.02, "y": 0.02, "curve": [0.038, 0.02, 0.149, 1, 0.038, 0.02, 0.149, 1]}, {"time": 0.3, "curve": [0.457, 1, 0.561, 0.02, 0.457, 1, 0.561, 0.02]}, {"time": 0.6, "x": 0.02, "y": 0.02}]}, "star2": {"rotate": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.234, 0, 0.432, -56.11]}, {"time": 0.6333, "value": -56.11}], "translate": [{"time": 0.0333, "curve": [0.233, 0, 0.433, -54.11, 0.233, 0, 0.433, 49.95]}, {"time": 0.6333, "x": -54.11, "y": 49.95}], "scale": [{"x": 0.02, "y": 0.02, "curve": "stepped"}, {"time": 0.0333, "x": 0.02, "y": 0.02, "curve": [0.071, 0.02, 0.182, 1, 0.071, 0.02, 0.182, 1]}, {"time": 0.3333, "curve": [0.49, 1, 0.594, 0.02, 0.49, 1, 0.594, 0.02]}, {"time": 0.6333, "x": 0.02, "y": 0.02}]}, "star3": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": [0.334, 0, 0.532, -56.11]}, {"time": 0.7333, "value": -56.11}], "translate": [{"time": 0.1333, "curve": [0.333, 0, 0.533, 61.67, 0.333, 0, 0.533, -21.59]}, {"time": 0.7333, "x": 61.67, "y": -21.59}], "scale": [{"x": 0.02, "y": 0.02, "curve": "stepped"}, {"time": 0.1333, "x": 0.02, "y": 0.02, "curve": [0.171, 0.02, 0.282, 1, 0.171, 0.02, 0.282, 1]}, {"time": 0.4333, "curve": [0.59, 1, 0.694, 0.02, 0.59, 1, 0.694, 0.02]}, {"time": 0.7333, "x": 0.02, "y": 0.02}]}, "star4": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": [0.368, 0, 0.566, -56.11]}, {"time": 0.7667, "value": -56.11}], "translate": [{"time": 0.1667, "curve": [0.367, 0, 0.567, 24.67, 0.367, 0, 0.567, -83.26]}, {"time": 0.7667, "x": 24.67, "y": -83.26}], "scale": [{"x": 0.02, "y": 0.02, "curve": "stepped"}, {"time": 0.1667, "x": 0.02, "y": 0.02, "curve": [0.205, 0.02, 0.316, 1, 0.205, 0.02, 0.316, 1]}, {"time": 0.4667, "curve": [0.624, 1, 0.728, 0.02, 0.624, 1, 0.728, 0.02]}, {"time": 0.7667, "x": 0.02, "y": 0.02}]}, "star5": {"rotate": [{"curve": "stepped"}, {"time": 0.2333, "curve": [0.434, 0, 0.632, -56.11]}, {"time": 0.8333, "value": -56.11}], "translate": [{"time": 0.2333, "curve": [0.433, 0, 0.633, 3.08, 0.433, 0, 0.633, -49.34]}, {"time": 0.8333, "x": 3.08, "y": -49.34}], "scale": [{"x": 0.02, "y": 0.02, "curve": "stepped"}, {"time": 0.2333, "x": 0.02, "y": 0.02, "curve": [0.271, 0.02, 0.382, 1, 0.271, 0.02, 0.382, 1]}, {"time": 0.5333, "curve": [0.69, 1, 0.794, 0.02, 0.69, 1, 0.794, 0.02]}, {"time": 0.8333, "x": 0.02, "y": 0.02}]}, "star6": {"rotate": [{"curve": "stepped"}, {"time": 0.3, "curve": [0.501, 0, 0.699, -56.11]}, {"time": 0.9, "value": -56.11}], "translate": [{"time": 0.3, "curve": [0.5, 0, 0.7, -58.59, 0.5, 0, 0.7, -74.01]}, {"time": 0.9, "x": -58.59, "y": -74.01}], "scale": [{"x": 0.02, "y": 0.02, "curve": "stepped"}, {"time": 0.3, "x": 0.02, "y": 0.02, "curve": [0.338, 0.02, 0.449, 1, 0.338, 0.02, 0.449, 1]}, {"time": 0.6, "curve": [0.757, 1, 0.861, 0.02, 0.757, 1, 0.861, 0.02]}, {"time": 0.9, "x": 0.02, "y": 0.02}]}, "b1_elipse": {"scale": [{"x": 0.1, "y": 0.1, "curve": "stepped"}, {"time": 1.3667, "x": 0.1, "y": 0.1, "curve": [1.651, 0.1, 1.549, 4, 1.651, 0.1, 1.549, 4]}, {"time": 1.8333, "x": 4, "y": 4}]}, "b2_stroke": {"scale": [{"x": 0.1, "y": 0.1, "curve": "stepped"}, {"time": 1.4333, "x": 0.1, "y": 0.1, "curve": [1.718, 0.1, 1.549, 4, 1.718, 0.1, 1.549, 4]}, {"time": 1.8333, "x": 4, "y": 4}]}, "b1_stroke_glow": {"scale": [{"x": 0.1, "y": 0.1, "curve": "stepped"}, {"time": 1.3667, "x": 0.1, "y": 0.1, "curve": [1.651, 0.1, 1.482, 4, 1.651, 0.1, 1.482, 4]}, {"time": 1.7667, "x": 4, "y": 4}]}, "boom": {"translate": [{"y": -581.86, "curve": "stepped"}, {"time": 1.3, "y": -581.86, "curve": [1.367, 0, 1.5, 0, 1.4, -581.86, 1.466, 0]}, {"time": 1.5667}]}, "r25": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.4667, "x": 0.24, "curve": [1.523, 0.713, 1.616, 0.933, 1.5, 1, 1.8, 1]}, {"time": 1.8333}]}, "r27": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.5333, "x": 0.24, "curve": [1.589, 0.713, 1.683, 0.933, 1.567, 1, 1.867, 1]}, {"time": 1.9}]}, "r29": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.4333, "x": 0.24, "curve": [1.489, 0.713, 1.583, 0.933, 1.467, 1, 1.767, 1]}, {"time": 1.8}]}, "r31": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.5, "x": 0.24, "curve": [1.556, 0.713, 1.65, 0.933, 1.533, 1, 1.833, 1]}, {"time": 1.8667}]}, "r33": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.6333, "x": 0.24, "curve": [1.689, 0.713, 1.783, 0.933, 1.667, 1, 1.967, 1]}, {"time": 2}]}, "r35": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.4667, "x": 0.24, "curve": [1.523, 0.713, 2.183, 0.933, 1.5, 1, 2.367, 1]}, {"time": 2.4}]}, "r37": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.5, "x": 0.24, "curve": [1.556, 0.713, 1.65, 0.933, 1.533, 1, 1.833, 1]}, {"time": 1.8667}]}, "r39": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.4, "x": 0.24, "curve": [1.456, 0.713, 1.55, 0.933, 1.433, 1, 1.733, 1]}, {"time": 1.7667}]}, "r41": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.5667, "x": 0.24, "curve": [1.623, 0.713, 1.716, 0.933, 1.6, 1, 1.9, 1]}, {"time": 1.9333}]}, "r43": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.4667, "x": 0.24, "curve": [1.523, 0.713, 1.616, 0.933, 1.5, 1, 1.8, 1]}, {"time": 1.8333}]}, "r45": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.7, "x": 0.24, "curve": [1.756, 0.713, 1.85, 0.933, 1.733, 1, 2.033, 1]}, {"time": 2.0667}]}, "r47": {"scale": [{"x": 0.24, "curve": "stepped"}, {"time": 1.5, "x": 0.24, "curve": [1.556, 0.713, 1.65, 0.933, 1.533, 1, 1.833, 1]}, {"time": 1.8667}]}, "box_shadow": {"scale": [{"curve": [0.067, 0.868, 0.1, 0.608, 0.067, 0.868, 0.1, 0.608]}, {"time": 0.3333, "x": 0.6, "y": 0.6, "curve": [0.451, 0.596, 0.493, 0.868, 0.451, 0.596, 0.493, 0.868]}, {"time": 0.5333, "curve": [0.56, 0.967, 0.622, 0.9, 0.56, 0.967, 0.622, 0.9]}, {"time": 0.6667, "x": 0.9, "y": 0.9, "curve": [0.711, 0.9, 0.773, 0.967, 0.711, 0.9, 0.773, 0.967]}, {"time": 0.8, "curve": "stepped"}, {"time": 1.3667, "curve": [1.467, 1, 1.466, 0.7, 1.467, 1, 1.466, 0.7]}, {"time": 1.5667, "x": 0.7, "y": 0.7}]}, "ellipse": {"scale": [{"time": 2, "curve": [2.167, 1, 2.5, 1.5, 2.167, 1, 2.5, 1.5]}, {"time": 2.6667, "x": 1.5, "y": 1.5}]}, "uni_ch": {"translatex": [{"time": 2, "value": 76.62, "curve": [2.111, 76.62, 2.156, 53.09]}, {"time": 2.2667, "value": 36.06, "curve": [2.344, 24.14, 2.456, 12.02]}, {"time": 2.5333, "curve": [2.61, -11.92, 2.756, 0]}, {"time": 2.8667}], "translatey": [{"time": 2, "value": 714.14, "curve": [2.058, 959.43, 2.11, 1281.37]}, {"time": 2.2667, "value": 1281.37, "curve": [2.421, 1281.37, 2.475, 415.19]}, {"time": 2.5333, "curve": [2.554, 21.4, 2.613, 83.41]}, {"time": 2.7, "value": 83.1, "curve": [2.767, 82.86, 2.841, 23.29]}, {"time": 2.8667}], "scale": [{"time": 2, "x": 0.2, "y": -0.2, "curve": [2.111, 0.322, 2.156, 0.432, 2.111, -0.266, 2.156, -0.397]}, {"time": 2.2667, "x": 0.565, "y": -0.397, "curve": [2.378, 0.698, 2.422, 0.9, 2.378, -0.397, 2.419, 1.1]}, {"time": 2.5333, "x": 0.9, "y": 1.1, "curve": [2.617, 0.9, 2.616, 1.1, 2.617, 1.1, 2.616, 0.9]}, {"time": 2.7, "x": 1.1, "y": 0.9, "curve": [2.784, 1.1, 2.783, 1, 2.784, 0.9, 2.783, 1]}, {"time": 2.8667}]}, "UniRay": {"translate": [{"y": 680.31}, {"time": 2.3333, "x": 40.64, "y": 610.27}, {"time": 2.4, "x": 13.12, "y": 517.41, "curve": [2.411, 13.12, 2.587, 0, 2.411, 462.76, 2.543, -585.58]}, {"time": 2.6, "y": -585.58}], "scale": [{"x": 0.403, "curve": "stepped"}, {"time": 2.3333, "x": 0.403, "curve": [2.344, 0.403, 2.356, 0.886, 2.344, 1, 2.356, 1]}, {"time": 2.3667, "x": 0.801, "curve": [2.371, 0.771, 2.398, 0.595, 2.371, 1, 2.398, 1]}, {"time": 2.4333, "x": 0.479, "curve": [2.5, 0.262, 2.593, 0.061, 2.5, 1, 2.593, 1]}, {"time": 2.6, "x": 0.061}]}, "star7": {"rotate": [{"curve": "stepped"}, {"time": 2.4333, "curve": [2.633, 0, 2.833, -56.11]}, {"time": 3.0333, "value": -56.11}], "translate": [{"x": -161.4, "y": -281.51, "curve": "stepped"}, {"time": 2.4333, "x": -161.4, "y": -281.51, "curve": [2.545, -17.67, 2.681, 84.38, 2.545, -103.82, 2.681, 22.33]}, {"time": 3.0333, "x": 91.57, "y": 31.22}], "scale": [{"x": 0.02, "y": 0.02, "curve": "stepped"}, {"time": 2.4333, "x": 0.02, "y": 0.02, "curve": [2.456, 0.347, 2.478, 1, 2.456, 0.347, 2.478, 1]}, {"time": 2.5, "curve": [2.678, 1, 2.856, 0.347, 2.678, 1, 2.856, 0.347]}, {"time": 3.0333, "x": 0.02, "y": 0.02}]}, "star8": {"rotate": [{"curve": "stepped"}, {"time": 2.4667, "curve": [2.667, 0, 2.867, -56.11]}, {"time": 3.0667, "value": -56.11}], "translate": [{"x": 191.43, "y": -187.67, "curve": "stepped"}, {"time": 2.4667, "x": 191.43, "y": -187.67, "curve": [2.578, 51.92, 2.715, -47.13, 2.578, -52.66, 2.715, 43.2]}, {"time": 3.0667, "x": -54.11, "y": 49.95}], "scale": [{"x": 0.02, "y": 0.02, "curve": "stepped"}, {"time": 2.4667, "x": 0.02, "y": 0.02, "curve": [2.489, 0.347, 2.511, 1, 2.489, 0.347, 2.511, 1]}, {"time": 2.5333, "curve": [2.711, 1, 2.889, 0.347, 2.711, 1, 2.889, 0.347]}, {"time": 3.0667, "x": 0.02, "y": 0.02}]}, "star9": {"rotate": [{"curve": "stepped"}, {"time": 2.5667, "curve": [2.767, 0, 2.967, -56.11]}, {"time": 3.1667, "value": -56.11}], "translate": [{"x": -210.19, "y": -93.84, "curve": "stepped"}, {"time": 2.5667, "x": -210.19, "y": -93.84, "curve": [2.678, -55.72, 2.815, 53.95, 2.678, -52.78, 2.815, -23.64]}, {"time": 3.1667, "x": 61.67, "y": -21.59}], "scale": [{"x": 0.02, "y": 0.02, "curve": "stepped"}, {"time": 2.5667, "x": 0.02, "y": 0.02, "curve": [2.589, 0.347, 2.611, 1, 2.589, 0.347, 2.611, 1]}, {"time": 2.6333, "curve": [2.811, 1, 2.989, 0.347, 2.811, 1, 2.989, 0.347]}, {"time": 3.1667, "x": 0.02, "y": 0.02}]}, "star10": {"rotate": [{"curve": "stepped"}, {"time": 2.6, "curve": [2.8, 0, 3, -56.11]}, {"time": 3.2, "value": -56.11}], "translate": [{"x": -206.44, "y": -18.77, "curve": "stepped"}, {"time": 2.6, "x": -206.44, "y": -18.77, "curve": [2.711, -75.13, 2.848, 18.1, 2.711, -55.41, 2.848, -81.43]}, {"time": 3.2, "x": 24.67, "y": -83.26}], "scale": [{"x": 0.02, "y": 0.02, "curve": "stepped"}, {"time": 2.6, "x": 0.02, "y": 0.02, "curve": [2.622, 0.347, 2.644, 1, 2.622, 0.347, 2.644, 1]}, {"time": 2.6667, "curve": [2.844, 1, 3.022, 0.347, 2.844, 1, 3.022, 0.347]}, {"time": 3.2, "x": 0.02, "y": 0.02}]}, "star11": {"rotate": [{"curve": "stepped"}, {"time": 2.6667, "curve": [2.867, 0, 3.067, -56.11]}, {"time": 3.2667, "value": -56.11}], "translate": [{"x": -3.75, "y": 93.84, "curve": "stepped"}, {"time": 2.6667, "x": -3.75, "y": 93.84, "curve": [2.778, 0.13, 2.915, 2.89, 2.778, 12.49, 2.915, -45.27]}, {"time": 3.2667, "x": 3.08, "y": -49.34}], "scale": [{"x": 0.02, "y": 0.02, "curve": "stepped"}, {"time": 2.6667, "x": 0.02, "y": 0.02, "curve": [2.689, 0.347, 2.711, 1, 2.689, 0.347, 2.711, 1]}, {"time": 2.7333, "curve": [2.911, 1, 3.089, 0.347, 2.911, 1, 3.089, 0.347]}, {"time": 3.2667, "x": 0.02, "y": 0.02}]}, "star12": {"rotate": [{"curve": "stepped"}, {"time": 2.7333, "curve": [2.933, 0, 3.133, -56.11]}, {"time": 3.3333, "value": -56.11}], "translate": [{"x": 135.12, "y": -131.37, "curve": "stepped"}, {"time": 2.7333, "x": 135.12, "y": -131.37, "curve": [2.845, 25.06, 2.981, -53.09, 2.845, -98.78, 2.981, -75.64]}, {"time": 3.3333, "x": -58.59, "y": -74.01}], "scale": [{"x": 0.02, "y": 0.02, "curve": "stepped"}, {"time": 2.7333, "x": 0.02, "y": 0.02, "curve": [2.756, 0.347, 2.778, 1, 2.756, 0.347, 2.778, 1]}, {"time": 2.8, "curve": [2.978, 1, 3.156, 0.02, 2.978, 1, 3.156, 0.02]}, {"time": 3.3333, "x": 0.02, "y": 0.02}]}}}}}
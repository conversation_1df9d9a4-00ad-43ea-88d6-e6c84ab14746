{"skeleton": {"hash": "OYxAYEfxZS8", "spine": "4.2.40", "x": -181.38, "y": -6.73, "width": 360.49, "height": 132.53, "images": "./Images/Monster_Slippy/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "Low_Cntr", "parent": "root", "icon": "straightLine"}, {"name": "Main_Cntr", "parent": "Low_Cntr", "y": 59.96, "icon": "rotate"}, {"name": "Cntr", "parent": "Main_Cntr", "icon": "diamond"}, {"name": "Left", "parent": "Main_Cntr", "length": 79.06, "x": 35, "y": 0.22}, {"name": "Eye_L", "parent": "Left", "x": 71, "y": 13.62, "icon": "eye"}, {"name": "Eye_L_Pupil", "parent": "Eye_L", "length": 20, "x": 6, "y": -3}, {"name": "Eye_L_Eyelid_t", "parent": "Eye_L", "rotation": 4.43, "x": 0.61, "y": -2.46, "icon": "arrowUp"}, {"name": "Eye_<PERSON>_Eyelid_b", "parent": "Eye_L", "rotation": 4.43, "x": 0.99, "y": -7.25, "icon": "arrowDown"}, {"name": "Brow_L", "parent": "Left", "x": 71.5, "y": 56.62}, {"name": "Right", "parent": "Main_Cntr", "length": 79.06, "rotation": 180, "x": -35}, {"name": "Brow_R", "parent": "Right", "rotation": 180, "x": 71.5, "y": -56.84}, {"name": "Mouth", "parent": "Main_Cntr", "x": 5.53, "y": -11.1, "icon": "mouth"}, {"name": "Mouth_cntrl", "parent": "Mouth", "x": -0.55, "y": 4.98, "icon": "arrows"}, {"name": "Mouth_Bottom", "parent": "Mouth_cntrl", "y": -12.73, "icon": "arrowUpDown"}, {"name": "Saliva", "parent": "Mouth_cntrl", "x": -37.35, "y": -31.48}, {"name": "Eye_R", "parent": "Right", "rotation": 180, "x": 71.69, "y": -13.84, "icon": "eye"}, {"name": "Eye_R_Pupil", "parent": "Eye_R", "length": 20, "x": 6, "y": -3}, {"name": "Eye_R_Eyelid_t", "parent": "Eye_R", "rotation": 4.43, "x": 0.61, "y": -2.46, "icon": "arrowUp"}, {"name": "Eye_<PERSON>_Eye<PERSON>_b", "parent": "Eye_R", "rotation": 4.43, "x": 0.99, "y": -7.25, "icon": "arrowDown"}, {"name": "Slippy_leg_R", "parent": "Right", "length": 28.19, "rotation": 89.76, "x": 103.73, "y": 2.08}, {"name": "Slippy_leg_R2", "parent": "Slippy_leg_R", "length": 28.19, "x": 28.19}, {"name": "Slippy_leg_L", "parent": "Left", "length": 28.19, "rotation": -90.24, "x": 103.18, "y": -3.45, "scaleY": -1}, {"name": "Slippy_leg_L2", "parent": "Slippy_leg_L", "length": 28.19, "x": 28.19}, {"name": "Leg_L_IK", "parent": "root", "x": 138, "y": 0.2, "color": "ff3f00ff", "icon": "ik"}, {"name": "Leg_R_IK", "parent": "root", "x": -138.92, "y": 0.2, "color": "ff3f00ff", "icon": "ik"}, {"name": "blot", "parent": "root", "rotation": -0.04, "x": 0.04, "y": 53.57, "scaleX": 1.5, "icon": "flower"}, {"name": "blot_drops_control", "parent": "blot", "y": -10.68, "icon": "arrowDown"}, {"name": "Drops", "parent": "root", "y": 53.57, "scaleX": 1.5}, {"name": "blot_drop2", "parent": "Drops"}, {"name": "blot_drop_s1", "parent": "Drops"}, {"name": "blot_drop3", "parent": "Drops", "scaleX": 0.8698, "scaleY": 0.8698}, {"name": "blot_drop4", "parent": "Drops", "scaleX": 1.2553, "scaleY": 1.2553}, {"name": "blot_drop_s2", "parent": "Drops"}, {"name": "blot_drop5", "parent": "Drops"}, {"name": "blot_drop_s3", "parent": "Drops"}, {"name": "blot_drop6", "parent": "Drops", "scaleX": 0.8698, "scaleY": 0.8698}, {"name": "blot_drop_s4", "parent": "Drops"}], "slots": [{"name": "Body_outline", "bone": "Cntr", "attachment": "Body_outline"}, {"name": "Leg_R_outline", "bone": "Slippy_leg_R", "attachment": "Leg_l_outline"}, {"name": "Leg_L_outline", "bone": "Slippy_leg_L", "attachment": "Leg_l_outline"}, {"name": "Slippy_body", "bone": "Cntr", "attachment": "Slippy_body"}, {"name": "Slippy_leg_R", "bone": "Slippy_leg_R", "attachment": "Slippy_leg_l"}, {"name": "Slippy_leg_L", "bone": "Slippy_leg_L", "attachment": "Slippy_leg_l"}, {"name": "Eye_L", "bone": "Eye_L", "attachment": "Eye_r"}, {"name": "Eye_L2", "bone": "Eye_R", "attachment": "Eye_r"}, {"name": "Eye_red_L", "bone": "Eye_L", "color": "ffffff00", "attachment": "Eye_red_r"}, {"name": "Eye_red_L2", "bone": "Eye_R", "color": "ffffff00", "attachment": "Eye_red_r"}, {"name": "Eye_Pupil_r", "bone": "Eye_L_Pupil", "attachment": "Eye_Pupil_r"}, {"name": "Eye_Pupil_r2", "bone": "Eye_R_Pupil", "attachment": "Eye_Pupil_r"}, {"name": "Eyelid_l_r", "bone": "Eye_L", "attachment": "Eyelid_l_r"}, {"name": "Eyelid_l_r2", "bone": "Eye_R", "attachment": "Eyelid_l_r"}, {"name": "Eyelid_u_r", "bone": "Eye_L", "attachment": "Eyelid_u_r"}, {"name": "Eyelid_u_r2", "bone": "Eye_R", "attachment": "Eyelid_u_r"}, {"name": "Brow_r", "bone": "Brow_R", "attachment": "Brow_r"}, {"name": "Brow_l", "bone": "Brow_L", "attachment": "Brow_l"}, {"name": "Mouth_base", "bone": "Mouth", "attachment": "Mouth_base"}, {"name": "Mouth_open", "bone": "Mouth", "attachment": "Mouth_open"}, {"name": "<PERSON><PERSON>", "bone": "Mouth", "attachment": "<PERSON><PERSON>"}, {"name": "Saliva", "bone": "Mouth", "attachment": "Saliva"}, {"name": "blot", "bone": "blot"}, {"name": "blot_drop2", "bone": "blot_drop2"}, {"name": "blot_drop_s1", "bone": "blot_drop_s1"}, {"name": "blot_drop3", "bone": "blot_drop3"}, {"name": "blot_drop4", "bone": "blot_drop4"}, {"name": "blot_drop5", "bone": "blot_drop_s2"}, {"name": "blot_drop6", "bone": "blot_drop5"}, {"name": "blot_drop_s2", "bone": "blot_drop_s3"}, {"name": "blot_drop7", "bone": "blot_drop6"}, {"name": "blot_drop8", "bone": "blot_drop_s4"}], "ik": [{"name": "Leg_L_IK", "bones": ["Slippy_leg_L", "Slippy_leg_L2"], "target": "Leg_L_IK", "bendPositive": false, "stretch": true}, {"name": "Leg_R_IK", "order": 1, "bones": ["Slippy_leg_R", "Slippy_leg_R2"], "target": "Leg_R_IK", "stretch": true}], "skins": [{"name": "default", "attachments": {"blot": {"blot": {"type": "mesh", "color": "fdd84dff", "uvs": [0.16853, 0.04132, 0.20427, 0.04133, 0.235, 0.05776, 0.2567, 0.08503, 0.27019, 0.11893, 0.28083, 0.15284, 0.29008, 0.16859, 0.40306, 0.14175, 0.52511, 0.14243, 0.63958, 0.17341, 0.65099, 0.15069, 0.66785, 0.11583, 0.69938, 0.10078, 0.74656, 0.0982, 0.78663, 0.11447, 0.81478, 0.14412, 0.82679, 0.17844, 0.8274, 0.22268, 0.80551, 0.24979, 0.78146, 0.26764, 0.77045, 0.28539, 0.79007, 0.31899, 0.82203, 0.31695, 0.86186, 0.3347, 0.88752, 0.35974, 0.90407, 0.39632, 0.9038, 0.43154, 0.89703, 0.47361, 0.87282, 0.49811, 0.84367, 0.51769, 0.83976, 0.57695, 0.82721, 0.62819, 0.85091, 0.63399, 0.88661, 0.64029, 0.9085, 0.64943, 0.92589, 0.66799, 0.92962, 0.68768, 0.92352, 0.71185, 0.90345, 0.72913, 0.88078, 0.7345, 0.85869, 0.73204, 0.84233, 0.71683, 0.82827, 0.69353, 0.80387, 0.68283, 0.78923, 0.70054, 0.77272, 0.72456, 0.75456, 0.74543, 0.74252, 0.76337, 0.75174, 0.78241, 0.7707, 0.79541, 0.78966, 0.80098, 0.80915, 0.81654, 0.82604, 0.83018, 0.83576, 0.85319, 0.83587, 0.87721, 0.83318, 0.90725, 0.81778, 0.92725, 0.79106, 0.94395, 0.74837, 0.94704, 0.71572, 0.94051, 0.69219, 0.91664, 0.67776, 0.88946, 0.6729, 0.86403, 0.66254, 0.84197, 0.64012, 0.83666, 0.62051, 0.84647, 0.61585, 0.88202, 0.60876, 0.90761, 0.58596, 0.93801, 0.55205, 0.95614, 0.51472, 0.9587, 0.47446, 0.95391, 0.44301, 0.93005, 0.42458, 0.90092, 0.4098, 0.87756, 0.3531, 0.86126, 0.31071, 0.8462, 0.27287, 0.82849, 0.23944, 0.82863, 0.21336, 0.82462, 0.19205, 0.80713, 0.1763, 0.78103, 0.17544, 0.7546, 0.15225, 0.73275, 0.12704, 0.75153, 0.09896, 0.78407, 0.07158, 0.81002, 0.02474, 0.81038, 0.00898, 0.79541, 0.00014, 0.76864, 0.0126, 0.74387, 0.02727, 0.723, 0.06373, 0.71112, 0.1024, 0.70314, 0.11577, 0.6871, 0.11344, 0.66789, 0.10013, 0.64319, 0.07288, 0.63049, 0.0481, 0.60493, 0.0395, 0.57932, 0.04017, 0.53824, 0.05265, 0.51498, 0.04012, 0.49584, 0.02776, 0.45571, 0.02776, 0.39855, 0.042, 0.36049, 0.07071, 0.32161, 0.10651, 0.29848, 0.14993, 0.28593, 0.17204, 0.25511, 0.157, 0.23649, 0.12839, 0.21904, 0.09915, 0.19856, 0.07437, 0.1592, 0.07434, 0.10411, 0.09293, 0.06764, 0.12711, 0.04525, 0.7637, 0.87399, 0.51839, 0.89337, 0.06159, 0.75434, 0.88018, 0.68481, 0.82945, 0.40232, 0.45472, 0.47982, 0.74434, 0.1825], "triangles": [123, 12, 13, 0, 112, 113, 111, 0, 4, 3, 4, 0, 119, 89, 90, 119, 88, 89, 85, 119, 84, 116, 113, 114, 123, 13, 14, 117, 53, 54, 55, 117, 54, 40, 41, 120, 56, 117, 55, 59, 60, 117, 56, 57, 117, 57, 58, 117, 69, 70, 118, 86, 87, 119, 86, 119, 85, 70, 71, 118, 71, 72, 118, 69, 118, 68, 117, 58, 59, 120, 39, 40, 38, 39, 120, 38, 120, 37, 37, 120, 36, 120, 35, 36, 120, 34, 35, 117, 51, 52, 117, 52, 53, 117, 50, 51, 2, 0, 1, 3, 0, 2, 115, 116, 114, 116, 0, 113, 87, 88, 119, 97, 98, 99, 96, 97, 99, 122, 79, 80, 79, 122, 78, 77, 78, 122, 122, 96, 101, 111, 112, 0, 123, 14, 15, 11, 12, 123, 95, 96, 122, 93, 94, 83, 84, 93, 83, 119, 91, 92, 90, 91, 119, 119, 93, 84, 93, 119, 92, 63, 64, 47, 48, 63, 47, 62, 63, 48, 117, 48, 49, 117, 49, 50, 62, 48, 117, 65, 118, 74, 61, 62, 117, 66, 118, 65, 73, 74, 118, 67, 118, 66, 60, 61, 117, 72, 73, 118, 68, 118, 67, 42, 31, 32, 122, 83, 95, 46, 122, 45, 82, 83, 122, 47, 122, 46, 81, 82, 122, 122, 80, 81, 77, 122, 76, 47, 64, 122, 76, 122, 75, 64, 65, 122, 122, 74, 75, 65, 74, 122, 101, 99, 100, 31, 44, 122, 123, 15, 16, 123, 16, 17, 4, 110, 111, 110, 4, 5, 110, 5, 6, 18, 123, 17, 109, 110, 6, 19, 123, 18, 20, 123, 19, 121, 22, 23, 121, 23, 24, 121, 24, 25, 26, 121, 25, 27, 121, 26, 122, 7, 8, 122, 8, 9, 6, 7, 122, 109, 6, 122, 108, 109, 122, 10, 11, 123, 9, 10, 123, 20, 122, 9, 20, 9, 123, 105, 103, 104, 101, 102, 103, 28, 121, 27, 105, 107, 103, 101, 103, 108, 106, 107, 105, 108, 103, 107, 101, 108, 122, 29, 121, 28, 121, 122, 21, 21, 22, 121, 122, 121, 29, 122, 20, 21, 29, 30, 122, 31, 122, 30, 101, 96, 99, 45, 122, 44, 120, 32, 33, 120, 33, 34, 83, 94, 95, 42, 32, 120, 43, 31, 42, 43, 44, 31, 41, 42, 120], "vertices": [2, 26, -90.46, 143.76, 0.02155, 27, -90.46, 154.44, 0.97845, 2, 26, -78.96, 143.56, 0.00946, 27, -78.96, 154.24, 0.99054, 2, 26, -69.06, 140.41, 0.153, 27, -69.06, 151.08, 0.847, 2, 26, -62.08, 135.28, 0.39795, 27, -62.08, 145.96, 0.60205, 2, 26, -57.73, 128.96, 0.70584, 27, -57.73, 139.64, 0.29416, 2, 26, -54.31, 121.28, 0.93146, 27, -54.31, 131.95, 0.06854, 2, 26, -51.33, 116.34, 0.95362, 27, -51.33, 127.01, 0.04638, 2, 26, -14.95, 122.1, 0.7559, 27, -14.95, 132.78, 0.2441, 2, 26, 24.35, 121.97, 0.76179, 27, 24.35, 132.65, 0.23821, 2, 26, 61.21, 111.18, 0.74063, 27, 61.21, 121.86, 0.25937, 2, 26, 64.89, 117.25, 0.64513, 27, 64.89, 127.93, 0.35487, 2, 26, 70.31, 124.39, 0.36729, 27, 70.31, 135.07, 0.63271, 2, 26, 80.47, 126.67, 0.19886, 27, 80.47, 137.34, 0.80114, 2, 26, 95.66, 125.92, 0.10171, 27, 95.66, 136.6, 0.89829, 2, 26, 108.56, 122.02, 0.19647, 27, 108.56, 132.69, 0.80353, 2, 26, 117.62, 114.78, 0.36288, 27, 117.62, 125.46, 0.63712, 2, 26, 121.49, 104.86, 0.46208, 27, 121.49, 115.54, 0.53792, 2, 26, 121.69, 92.1, 0.59174, 27, 121.69, 102.78, 0.40826, 2, 26, 114.64, 85.44, 0.74078, 27, 114.64, 96.12, 0.25922, 2, 26, 106.9, 81.54, 0.86799, 27, 106.9, 92.21, 0.13201, 1, 26, 103.35, 77.75, 1, 1, 26, 109.67, 66.42, 1, 2, 26, 119.96, 66.84, 0.98349, 27, 119.96, 77.51, 0.01651, 2, 26, 132.78, 58.11, 0.81787, 27, 132.78, 68.78, 0.18213, 2, 26, 141.05, 47.77, 0.70315, 27, 141.05, 58.44, 0.29685, 2, 26, 146.38, 34.2, 0.62876, 27, 146.38, 44.88, 0.37124, 2, 26, 146.29, 22, 0.60882, 27, 146.29, 32.68, 0.39118, 2, 26, 144.11, 8.19, 0.63067, 27, 144.11, 18.86, 0.36933, 2, 26, 136.31, 1.81, 0.74414, 27, 136.31, 12.49, 0.25586, 2, 26, 126.93, -2.41, 0.88743, 27, 126.93, 8.27, 0.11257, 1, 26, 125.67, -20.51, 1, 2, 26, 121.63, -39.03, 0.92415, 27, 121.63, -28.36, 0.07585, 2, 26, 129.26, -42.12, 0.85626, 27, 129.26, -31.44, 0.14374, 2, 26, 140.76, -47.64, 0.65141, 27, 140.76, -36.96, 0.34859, 2, 26, 147.81, -54, 0.45337, 27, 147.81, -43.33, 0.54663, 2, 26, 153.4, -64.06, 0.22414, 27, 153.4, -53.39, 0.77586, 2, 26, 154.61, -72.28, 0.12863, 27, 154.61, -61.61, 0.87137, 2, 26, 152.64, -81.49, 0.06435, 27, 152.64, -70.82, 0.93565, 2, 26, 146.18, -87.13, 0.0759, 27, 146.18, -76.45, 0.9241, 2, 26, 138.88, -88.32, 0.11319, 27, 138.88, -77.64, 0.88681, 2, 26, 131.77, -85.67, 0.22246, 27, 131.77, -75, 0.77754, 2, 26, 126.5, -77.3, 0.41853, 27, 126.5, -66.62, 0.58147, 2, 26, 121.97, -65.4, 0.66246, 27, 121.97, -54.72, 0.33754, 2, 26, 114.11, -56.28, 0.99426, 27, 114.11, -45.61, 0.00574, 2, 26, 109.4, -62.28, 0.99314, 27, 109.4, -51.6, 0.00686, 2, 26, 104.08, -70.39, 0.99213, 27, 104.08, -59.71, 0.00787, 2, 26, 98.24, -77.33, 0.99754, 27, 98.24, -66.65, 0.00246, 2, 26, 94.36, -89.81, 0.60983, 27, 94.36, -79.13, 0.39017, 2, 26, 97.33, -95.07, 0.67939, 27, 97.33, -84.39, 0.32061, 2, 26, 103.43, -101.07, 0.58167, 27, 103.43, -90.39, 0.41833, 2, 26, 109.54, -105.3, 0.43988, 27, 109.54, -94.63, 0.56012, 2, 26, 115.81, -112.43, 0.3264, 27, 115.81, -101.75, 0.6736, 2, 26, 121.25, -119, 0.20757, 27, 121.25, -108.32, 0.79243, 2, 26, 124.38, -126.45, 0.2256, 27, 124.38, -115.78, 0.7744, 2, 26, 124.42, -132.77, 0.33257, 27, 124.42, -122.1, 0.66743, 2, 26, 123.55, -143.88, 0.27292, 27, 123.55, -133.21, 0.72708, 2, 26, 118.59, -152.98, 0.13068, 27, 118.59, -142.31, 0.86932, 1, 27, 109.99, -150.11, 1, 1, 27, 96.24, -151.15, 1, 2, 26, 85.73, -158.03, 0.09602, 27, 85.73, -147.35, 0.90398, 2, 26, 78.15, -145.04, 0.39368, 27, 78.15, -134.37, 0.60632, 2, 26, 73.51, -130.78, 0.70124, 27, 73.51, -120.11, 0.29876, 2, 26, 71.94, -119.42, 0.86966, 27, 71.94, -108.74, 0.13034, 2, 26, 68.6, -110.64, 0.95079, 27, 68.6, -99.96, 0.04921, 2, 26, 61.39, -108.45, 0.97473, 27, 61.39, -97.77, 0.02527, 2, 26, 55.07, -112.16, 0.95012, 27, 55.07, -101.49, 0.04988, 2, 26, 53.57, -125.98, 0.83978, 27, 53.57, -115.3, 0.16022, 2, 26, 51.29, -136.12, 0.7483, 27, 51.29, -125.44, 0.2517, 2, 26, 43.94, -150.35, 0.50798, 27, 43.94, -139.67, 0.49202, 2, 26, 33.03, -161.53, 0.20256, 27, 33.03, -150.85, 0.79744, 2, 26, 21.01, -165.66, 0.00552, 27, 21.01, -154.98, 0.99448, 1, 27, 8.04, -153.46, 1, 2, 26, -2.08, -151.12, 0.29991, 27, -2.08, -140.44, 0.70009, 2, 26, -8.02, -136, 0.61942, 27, -8.02, -125.33, 0.38058, 2, 26, -12.78, -126.03, 0.74609, 27, -12.78, -115.35, 0.25391, 2, 26, -31.03, -119.37, 0.81636, 27, -31.03, -108.69, 0.18364, 2, 26, -44.69, -117.31, 0.63451, 27, -44.69, -106.63, 0.36549, 2, 26, -56.87, -112.04, 0.59263, 27, -56.87, -101.36, 0.40737, 2, 26, -67.63, -113.63, 0.49938, 27, -67.63, -102.96, 0.50062, 2, 26, -76.03, -113.1, 0.44983, 27, -76.03, -102.43, 0.55017, 2, 26, -82.89, -105.42, 0.5575, 27, -82.89, -94.75, 0.4425, 2, 26, -87.96, -92.4, 0.81245, 27, -87.96, -81.72, 0.18755, 2, 26, -88.24, -80.61, 0.9861, 27, -88.24, -69.93, 0.0139, 2, 26, -95.71, -73.18, 0.99035, 27, -95.71, -62.5, 0.00965, 2, 26, -103.83, -87.19, 0.52724, 27, -103.83, -76.51, 0.47276, 1, 27, -112.87, -96.22, 1, 1, 27, -121.68, -104.97, 1, 1, 27, -136.77, -105.09, 1, 2, 26, -141.84, -109.31, 0.08531, 27, -141.84, -98.63, 0.91469, 2, 26, -144.69, -96.14, 0.33535, 27, -144.69, -85.46, 0.66465, 2, 26, -140.67, -85.7, 0.46148, 27, -140.67, -75.02, 0.53852, 2, 26, -135.95, -77.08, 0.55661, 27, -135.95, -66.41, 0.44339, 2, 26, -124.21, -70.98, 0.68332, 27, -124.21, -60.3, 0.31668, 2, 26, -111.76, -66.77, 0.77501, 27, -111.76, -56.09, 0.22499, 2, 26, -107.46, -60.29, 0.83963, 27, -107.46, -49.62, 0.16037, 2, 26, -108.21, -51.16, 0.29143, 27, -108.21, -40.48, 0.70857, 2, 26, -112.49, -52.79, 0.39981, 27, -112.49, -42.12, 0.60019, 2, 26, -121.27, -49.57, 0.33627, 27, -121.27, -38.89, 0.66373, 2, 26, -129.25, -38.22, 0.50107, 27, -129.25, -27.54, 0.49893, 2, 26, -132.01, -27.67, 0.61634, 27, -132.01, -17, 0.38366, 2, 26, -131.8, -13.11, 0.65964, 27, -131.8, -2.44, 0.34036, 2, 26, -127.78, -5.36, 0.65438, 27, -127.78, 5.31, 0.34562, 2, 26, -131.82, 1.43, 0.6746, 27, -131.82, 12.1, 0.3254, 2, 26, -135.8, 18.83, 0.90838, 27, -135.8, 29.5, 0.09162, 2, 26, -135.79, 36.43, 0.80863, 27, -135.79, 47.11, 0.19137, 2, 26, -131.21, 47.68, 0.71293, 27, -131.21, 58.35, 0.28707, 2, 26, -121.97, 60.35, 0.68699, 27, -121.97, 71.02, 0.31301, 2, 26, -110.44, 69.7, 0.78083, 27, -110.44, 80.37, 0.21917, 2, 26, -96.46, 76.64, 0.94418, 27, -96.46, 87.31, 0.05582, 2, 26, -89.34, 82.55, 0.67426, 27, -89.34, 93.22, 0.32574, 2, 26, -94.18, 88.9, 0.67915, 27, -94.18, 99.58, 0.32085, 2, 26, -103.39, 92.35, 0.53234, 27, -103.39, 103.02, 0.46766, 2, 26, -112.81, 99.57, 0.55156, 27, -112.81, 110.24, 0.44844, 2, 26, -120.79, 108.47, 0.28889, 27, -120.79, 119.15, 0.71111, 2, 26, -120.8, 122.84, 0.03582, 27, -120.8, 133.52, 0.96418, 1, 27, -114.81, 145.21, 1, 1, 27, -103.8, 152.76, 1, 2, 26, 101.18, -125.37, 0.71328, 27, 101.18, -114.69, 0.28672, 2, 26, 22.19, -131.76, 0.72168, 27, 22.19, -121.09, 0.27832, 2, 26, -124.9, -88.81, 0.48636, 27, -124.9, -78.13, 0.51364, 2, 26, 138.69, -65.46, 0.48188, 27, 138.69, -54.78, 0.51812, 2, 26, 122.35, 36.82, 0.90859, 27, 122.35, 47.5, 0.09141, 2, 26, 1.69, 0.84, 0.31422, 27, 1.69, 11.52, 0.68578, 2, 26, 94.94, 109.37, 0.81578, 27, 94.94, 120.04, 0.18422], "hull": 117, "edges": [10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 34, 36, 58, 60, 60, 62, 72, 74, 152, 154, 172, 174, 174, 176, 176, 178, 210, 212, 218, 220, 224, 226, 226, 228, 228, 230, 230, 232, 166, 168, 168, 170, 170, 172, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 200, 202, 202, 204, 196, 198, 198, 200, 158, 160, 160, 162, 154, 156, 156, 158, 162, 164, 164, 166, 146, 148, 142, 144, 144, 146, 140, 142, 134, 136, 132, 134, 136, 138, 138, 140, 128, 130, 130, 132, 126, 128, 122, 124, 124, 126, 118, 120, 120, 122, 114, 116, 116, 118, 110, 112, 112, 114, 108, 110, 178, 180, 180, 182, 102, 104, 100, 102, 94, 96, 104, 106, 106, 108, 96, 98, 98, 100, 90, 92, 92, 94, 86, 88, 88, 90, 82, 84, 84, 86, 74, 76, 76, 78, 78, 80, 80, 82, 68, 70, 70, 72, 62, 64, 64, 66, 66, 68, 54, 56, 56, 58, 50, 52, 52, 54, 46, 48, 48, 50, 44, 46, 244, 60, 40, 42, 42, 44, 36, 38, 38, 40, 30, 32, 32, 34, 26, 28, 28, 30, 18, 20, 20, 22, 40, 244, 148, 150, 150, 152, 156, 244, 160, 244, 162, 244, 158, 244, 6, 8, 8, 10, 2, 4, 4, 6, 2, 0, 0, 232, 220, 222, 222, 224, 216, 218, 212, 214, 214, 216, 208, 210, 206, 208, 204, 206, 244, 202], "width": 322, "height": 337}}, "blot_drop2": {"blot_drop2": {"color": "fdd84dff", "width": 63, "height": 52}}, "blot_drop3": {"blot_drop2": {"color": "fdd84dff", "width": 63, "height": 52}}, "blot_drop4": {"blot_drop2": {"color": "fdd84dff", "width": 63, "height": 52}}, "blot_drop5": {"blot_drop1": {"color": "fdd84dff", "rotation": -0.04, "width": 30, "height": 29}}, "blot_drop6": {"blot_drop2": {"color": "fdd84dff", "width": 63, "height": 52}}, "blot_drop7": {"blot_drop2": {"color": "fdd84dff", "width": 63, "height": 52}}, "blot_drop8": {"blot_drop1": {"color": "fdd84dff", "rotation": -0.04, "width": 30, "height": 29}}, "blot_drop_s1": {"blot_drop1": {"color": "fdd84dff", "rotation": -0.04, "width": 30, "height": 29}}, "blot_drop_s2": {"blot_drop1": {"color": "fdd84dff", "rotation": -0.04, "width": 30, "height": 29}}, "Body_outline": {"Body_outline": {"type": "mesh", "uvs": [0.82656, 0.01813, 0.87127, 0.11663, 0.89621, 0.01718, 0.95453, 0.01779, 0.99483, 0.16476, 0.99481, 0.36433, 0.94545, 0.38653, 0.95221, 0.49192, 0.94249, 0.61909, 0.91324, 0.71351, 0.873, 0.79908, 0.82713, 0.85007, 0.77521, 0.89398, 0.72816, 0.92302, 0.67627, 0.95025, 0.62712, 0.97218, 0.57782, 0.98388, 0.52343, 0.98917, 0.46904, 0.99445, 0.41465, 0.98335, 0.36536, 0.9742, 0.31606, 0.94867, 0.26677, 0.92314, 0.21068, 0.88414, 0.16506, 0.84277, 0.11787, 0.78961, 0.08263, 0.71244, 0.05222, 0.6001, 0.05182, 0.38864, 0.00554, 0.35722, 0.00446, 0.1962, 0.02473, 0.08983, 0.05189, 0.0173, 0.10993, 0.01746, 0.12314, 0.12245, 0.15325, 0.05663, 0.18789, 0.01063, 0.23643, 0.0189, 0.28028, 0.072, 0.33202, 0.06687, 0.38376, 0.06175, 0.43429, 0.06049, 0.48482, 0.05923, 0.53534, 0.05796, 0.58587, 0.0567, 0.64076, 0.06801, 0.69564, 0.07933, 0.72444, 0.07785, 0.76511, 0.016], "triangles": [4, 6, 3, 3, 1, 2, 5, 6, 4, 6, 1, 3, 6, 9, 1, 7, 9, 6, 7, 8, 9, 10, 1, 9, 1, 48, 0, 11, 1, 10, 48, 11, 47, 1, 11, 48, 12, 47, 11, 13, 47, 12, 46, 47, 13, 14, 45, 46, 14, 46, 13, 15, 44, 45, 15, 45, 14, 16, 43, 44, 43, 17, 42, 44, 15, 16, 43, 16, 17, 28, 29, 30, 34, 28, 31, 32, 34, 31, 34, 32, 33, 28, 30, 31, 26, 27, 28, 34, 26, 28, 35, 36, 37, 35, 37, 38, 24, 34, 35, 25, 26, 34, 38, 24, 35, 25, 34, 24, 23, 24, 38, 22, 23, 38, 39, 22, 38, 21, 22, 39, 40, 21, 39, 20, 21, 40, 19, 40, 41, 20, 40, 19, 19, 42, 18, 19, 41, 42, 17, 18, 42], "vertices": [2, 3, 117.87, 49.79, 0.0017, 4, 82.87, 49.57, 0.9983, 2, 3, 134.14, 38.66, 0.00017, 4, 99.14, 38.44, 0.99983, 2, 3, 143.22, 49.9, 1e-05, 4, 108.22, 49.68, 0.99999, 1, 4, 129.45, 49.61, 1, 1, 4, 144.12, 33, 1, 1, 4, 144.11, 10.45, 1, 1, 4, 126.14, 7.94, 1, 1, 4, 128.6, -3.97, 1, 2, 3, 160.07, -18.12, 2e-05, 4, 125.07, -18.34, 0.99998, 2, 3, 149.42, -28.78, 0.00026, 4, 114.42, -29.01, 0.99974, 3, 10, -169.77, 38.45, 1e-05, 3, 134.77, -38.45, 0.00184, 4, 99.77, -38.68, 0.99815, 3, 10, -153.07, 44.22, 0.00017, 3, 118.07, -44.22, 0.00808, 4, 83.07, -44.44, 0.99175, 3, 10, -134.18, 49.18, 0.0014, 3, 99.18, -49.18, 0.02573, 4, 64.18, -49.4, 0.97287, 3, 10, -117.05, 52.46, 0.00694, 3, 82.05, -52.46, 0.06379, 4, 47.05, -52.68, 0.92926, 3, 10, -98.16, 55.54, 0.02464, 3, 63.16, -55.54, 0.12768, 4, 28.16, -55.76, 0.84768, 3, 10, -80.27, 58.01, 0.06766, 3, 45.27, -58.01, 0.21071, 4, 10.27, -58.24, 0.72163, 3, 10, -62.33, 59.34, 0.15018, 3, 27.33, -59.34, 0.29014, 4, -7.67, -59.56, 0.55968, 3, 10, -42.53, 59.93, 0.27841, 3, 7.53, -59.93, 0.3355, 4, -27.47, -60.16, 0.38609, 3, 10, -22.73, 60.53, 0.44196, 3, -12.27, -60.53, 0.32659, 4, -47.27, -60.75, 0.23145, 3, 10, -2.93, 59.28, 0.61494, 3, -32.07, -59.28, 0.26747, 4, -67.07, -59.5, 0.11759, 3, 10, 15.01, 58.24, 0.7672, 3, -50.01, -58.24, 0.18357, 4, -85.01, -58.47, 0.04923, 3, 10, 32.95, 55.36, 0.87892, 3, -67.95, -55.36, 0.10474, 4, -102.95, -55.58, 0.01634, 3, 10, 50.89, 52.47, 0.94692, 3, -85.89, -52.47, 0.04901, 4, -120.89, -52.7, 0.00407, 3, 10, 71.31, 48.07, 0.98098, 3, -106.31, -48.07, 0.01834, 4, -141.31, -48.29, 0.00068, 3, 10, 87.92, 43.39, 0.99469, 3, -122.92, -43.39, 0.00525, 4, -157.92, -43.61, 6e-05, 2, 10, 105.1, 37.38, 0.99896, 3, -140.1, -37.38, 0.00104, 2, 10, 117.92, 28.66, 0.99989, 3, -152.92, -28.66, 0.00011, 2, 10, 128.99, 15.97, 1, 3, -163.99, -15.97, 0, 1, 10, 129.14, -7.93, 1, 1, 10, 145.98, -11.48, 1, 1, 10, 146.38, -29.67, 1, 1, 10, 139, -41.69, 1, 1, 10, 129.11, -49.89, 1, 1, 10, 107.99, -49.87, 1, 2, 10, 103.18, -38.01, 0.99998, 3, -138.18, 38.01, 2e-05, 2, 10, 92.22, -45.44, 0.99956, 3, -127.22, 45.44, 0.00044, 3, 10, 79.61, -50.64, 0.99648, 3, -114.61, 50.64, 0.00352, 4, -149.61, 50.42, 1e-05, 3, 10, 61.94, -49.71, 0.98362, 3, -96.94, 49.71, 0.01602, 4, -131.94, 49.48, 0.00036, 3, 10, 45.98, -43.71, 0.94662, 3, -80.98, 43.71, 0.05027, 4, -115.98, 43.48, 0.00311, 3, 10, 27.14, -44.29, 0.86714, 3, -62.14, 44.29, 0.11772, 4, -97.14, 44.06, 0.01513, 3, 10, 8.31, -44.86, 0.73435, 3, -43.31, 44.86, 0.21507, 4, -78.31, 44.64, 0.05058, 3, 10, -10.08, -45.01, 0.55784, 3, -24.92, 45.01, 0.31424, 4, -59.92, 44.78, 0.12793, 3, 10, -28.47, -45.15, 0.36911, 3, -6.53, 45.15, 0.3724, 4, -41.53, 44.93, 0.2585, 3, 10, -46.86, -45.29, 0.20656, 3, 11.86, 45.29, 0.35976, 4, -23.14, 45.07, 0.43368, 3, 10, -65.26, -45.43, 0.09455, 3, 30.26, 45.43, 0.28286, 4, -4.74, 45.21, 0.62259, 3, 10, -85.24, -44.16, 0.03389, 3, 50.24, 44.16, 0.17919, 4, 15.24, 43.93, 0.78691, 3, 10, -105.21, -42.88, 0.00889, 3, 70.21, 42.88, 0.08975, 4, 35.21, 42.66, 0.90136, 3, 10, -115.7, -43.04, 0.00151, 3, 80.7, 43.04, 0.03434, 4, 45.7, 42.82, 0.96415, 3, 10, -130.5, -50.03, 0.00012, 3, 95.5, 50.03, 0.00948, 4, 60.5, 49.81, 0.9904], "hull": 49, "edges": [0, 96, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 92, 94, 94, 96, 24, 26, 20, 22, 22, 24, 16, 18, 18, 20, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 26, 28, 28, 30, 88, 90, 90, 92, 76, 78, 78, 80, 84, 86, 86, 88, 80, 82, 82, 84], "width": 364, "height": 113}}, "Brow_l": {"Brow_l": {"width": 43, "height": 18}}, "Brow_r": {"Brow_r": {"width": 43, "height": 18}}, "Eyelid_l_r": {"Eyelid_l_r": {"type": "mesh", "uvs": [0.68019, 0.04469, 0.88515, 0.04515, 0.97782, 0.13544, 0.9775, 0.40906, 0.87482, 0.61541, 0.77214, 0.82176, 0.63431, 0.95584, 0.31823, 0.95573, 0.18126, 0.81817, 0, 0.45616, 0, 0.4535, 0.17363, 0.22012, 0.32444, 0.13218, 0.47524, 0.04424], "triangles": [0, 6, 13, 12, 13, 7, 8, 11, 12, 4, 0, 1, 9, 10, 11, 7, 8, 12, 3, 1, 2, 6, 7, 13, 5, 0, 4, 0, 5, 6, 4, 1, 3, 9, 11, 8], "vertices": [2, 5, 8.93, -3.98, 0.0977, 8, 8.17, 2.65, 0.9023, 2, 5, 17.95, -3.99, 0.37002, 8, 17.16, 1.94, 0.62998, 2, 5, 22.02, -5.98, 0.65589, 8, 21.07, -0.35, 0.34411, 2, 5, 22.01, -12, 0.86565, 8, 20.6, -6.36, 0.13435, 2, 5, 17.49, -16.54, 0.77551, 8, 15.74, -10.53, 0.22449, 2, 5, 12.97, -21.08, 0.81853, 8, 10.89, -14.71, 0.18147, 2, 5, 6.91, -24.03, 0.85856, 8, 4.61, -17.18, 0.14144, 2, 5, -7, -24.03, 0.84764, 8, -9.25, -16.11, 0.15236, 2, 5, -13.02, -21, 0.78498, 8, -15.03, -12.63, 0.21502, 2, 5, -21, -13.04, 0.72475, 8, -22.37, -4.07, 0.27525, 2, 5, -21, -12.98, 0.7221, 8, -22.36, -4.01, 0.2779, 2, 5, -13.36, -7.84, 0.26576, 8, -14.35, 0.52, 0.73424, 2, 5, -6.72, -5.91, 0.08948, 8, -7.58, 1.94, 0.91052, 2, 5, -0.09, -3.97, 0.00172, 8, -0.82, 3.35, 0.99828], "hull": 14, "edges": [2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 6, 8, 8, 10, 22, 24, 24, 26, 2, 0, 0, 26], "width": 44, "height": 22}}, "Eyelid_l_r2": {"Eyelid_l_r": {"type": "mesh", "uvs": [0.68019, 0.04469, 0.88515, 0.04515, 0.97782, 0.13544, 0.9775, 0.40906, 0.87482, 0.61541, 0.77214, 0.82176, 0.63431, 0.95584, 0.31823, 0.95573, 0.18126, 0.81817, 0, 0.45616, 0, 0.4535, 0.17363, 0.22012, 0.32444, 0.13218, 0.47524, 0.04424], "triangles": [0, 6, 13, 12, 13, 7, 8, 11, 12, 4, 0, 1, 9, 10, 11, 7, 8, 12, 3, 1, 2, 6, 7, 13, 5, 0, 4, 0, 5, 6, 4, 1, 3, 9, 11, 8], "vertices": [2, 16, 8.93, -3.98, 0.0977, 19, 8.17, 2.65, 0.9023, 2, 16, 17.95, -3.99, 0.37002, 19, 17.16, 1.94, 0.62998, 2, 16, 22.02, -5.98, 0.65589, 19, 21.07, -0.35, 0.34411, 2, 16, 22.01, -12, 0.86565, 19, 20.6, -6.36, 0.13435, 2, 16, 17.49, -16.54, 0.77551, 19, 15.74, -10.53, 0.22449, 2, 16, 12.97, -21.08, 0.81853, 19, 10.89, -14.71, 0.18147, 2, 16, 6.91, -24.03, 0.85856, 19, 4.61, -17.18, 0.14144, 2, 16, -7, -24.03, 0.84764, 19, -9.25, -16.11, 0.15236, 2, 16, -13.02, -21, 0.78498, 19, -15.03, -12.63, 0.21502, 2, 16, -21, -13.04, 0.72475, 19, -22.37, -4.07, 0.27525, 2, 16, -21, -12.98, 0.7221, 19, -22.36, -4.01, 0.2779, 2, 16, -13.36, -7.84, 0.26576, 19, -14.35, 0.52, 0.73424, 2, 16, -6.72, -5.91, 0.08948, 19, -7.58, 1.94, 0.91052, 2, 16, -0.09, -3.97, 0.00172, 19, -0.82, 3.35, 0.99828], "hull": 14, "edges": [2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 6, 8, 8, 10, 22, 24, 24, 26, 2, 0, 0, 26], "width": 44, "height": 22}}, "Eyelid_u_r": {"Eyelid_u_r": {"type": "mesh", "uvs": [0.64586, 0.0199, 0.814, 0.10256, 0.89599, 0.18361, 0.9593, 0.30699, 0.97949, 0.3641, 0.97947, 0.51396, 0.97946, 0.66381, 0.87236, 0.61897, 0.76314, 0.6032, 0.6534, 0.60509, 0.54577, 0.61737, 0.43391, 0.63484, 0.32628, 0.66061, 0.22449, 0.70197, 0.13117, 0.75579, 0.07286, 0.76282, 0.02054, 0.63505, 0.0204, 0.49764, 0.02027, 0.36023, 0.05971, 0.25946, 0.13984, 0.13938, 0.26678, 0.05697, 0.35278, 0.02007], "triangles": [17, 13, 16, 12, 13, 17, 5, 8, 4, 2, 8, 9, 10, 0, 9, 7, 8, 5, 22, 10, 11, 18, 11, 12, 6, 7, 5, 14, 16, 13, 15, 16, 14, 4, 8, 3, 17, 18, 12, 1, 9, 0, 2, 9, 1, 10, 22, 0, 11, 21, 22, 11, 20, 21, 11, 19, 20, 19, 11, 18, 8, 2, 3], "vertices": [2, 5, 7, 23.03, 0.99795, 7, 8.33, 24.91, 0.00205, 2, 5, 15.07, 18.97, 0.97196, 7, 16.07, 20.25, 0.02804, 2, 5, 19.01, 15, 0.9437, 7, 19.69, 15.99, 0.0563, 2, 5, 22.05, 8.96, 0.80868, 7, 22.25, 9.73, 0.19132, 2, 5, 23.02, 6.16, 0.73513, 7, 23, 6.86, 0.26487, 2, 5, 23.01, -1.18, 0.48056, 7, 22.43, -0.46, 0.51944, 2, 5, 23.01, -8.53, 0.26841, 7, 21.86, -7.78, 0.73159, 2, 5, 17.87, -6.33, 0.21256, 7, 16.91, -5.19, 0.78744, 2, 5, 12.63, -5.56, 0.12445, 7, 11.74, -4.02, 0.87555, 2, 5, 7.36, -5.65, 0.07059, 7, 6.48, -3.7, 0.92941, 2, 5, 2.2, -6.25, 0.05727, 7, 1.29, -3.9, 0.94273, 2, 5, -3.17, -7.11, 0.07037, 7, -4.13, -4.34, 0.92963, 2, 5, -8.34, -8.37, 0.07944, 7, -9.38, -5.2, 0.92056, 2, 5, -13.22, -10.4, 0.09926, 7, -14.41, -6.85, 0.90074, 2, 5, -17.7, -13.03, 0.17406, 7, -19.08, -9.13, 0.82594, 2, 5, -20.5, -13.38, 0.29634, 7, -21.9, -9.26, 0.70366, 2, 5, -23.01, -7.12, 0.58226, 7, -23.92, -2.82, 0.41774, 2, 5, -23.02, -0.38, 0.72188, 7, -23.4, 3.89, 0.27812, 2, 5, -23.03, 6.35, 0.88192, 7, -22.89, 10.61, 0.11808, 2, 5, -21.13, 11.29, 0.94872, 7, -20.62, 15.38, 0.05128, 2, 5, -17.29, 17.17, 0.99246, 7, -16.33, 20.95, 0.00754, 2, 5, -11.19, 21.21, 0.99929, 7, -9.95, 24.51, 0.00071, 1, 5, -7.07, 23.02, 1], "hull": 23, "edges": [0, 44, 0, 2, 2, 4, 4, 6, 6, 8, 30, 32, 36, 38, 38, 40, 40, 42, 42, 44, 28, 30, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 12, 14, 14, 16, 8, 10, 10, 12, 32, 34, 34, 36], "width": 48, "height": 49}}, "Eyelid_u_r2": {"Eyelid_u_r": {"type": "mesh", "uvs": [0.64586, 0.0199, 0.814, 0.10256, 0.89599, 0.18361, 0.9593, 0.30699, 0.97949, 0.3641, 0.97947, 0.51396, 0.97946, 0.66381, 0.87236, 0.61897, 0.76314, 0.6032, 0.6534, 0.60509, 0.54577, 0.61737, 0.43391, 0.63484, 0.32628, 0.66061, 0.22449, 0.70197, 0.13117, 0.75579, 0.07286, 0.76282, 0.02054, 0.63505, 0.0204, 0.49764, 0.02027, 0.36023, 0.05971, 0.25946, 0.13984, 0.13938, 0.26678, 0.05697, 0.35278, 0.02007], "triangles": [17, 13, 16, 12, 13, 17, 5, 8, 4, 2, 8, 9, 10, 0, 9, 7, 8, 5, 22, 10, 11, 18, 11, 12, 6, 7, 5, 14, 16, 13, 15, 16, 14, 4, 8, 3, 17, 18, 12, 1, 9, 0, 2, 9, 1, 10, 22, 0, 11, 21, 22, 11, 20, 21, 11, 19, 20, 19, 11, 18, 8, 2, 3], "vertices": [2, 16, 7, 23.03, 0.99795, 18, 8.33, 24.91, 0.00205, 2, 16, 15.07, 18.97, 0.97196, 18, 16.07, 20.25, 0.02804, 2, 16, 19.01, 15, 0.9437, 18, 19.69, 15.99, 0.0563, 2, 16, 22.05, 8.96, 0.80868, 18, 22.25, 9.73, 0.19132, 2, 16, 23.02, 6.16, 0.73513, 18, 23, 6.86, 0.26487, 2, 16, 23.01, -1.18, 0.48056, 18, 22.43, -0.46, 0.51944, 2, 16, 23.01, -8.53, 0.26841, 18, 21.86, -7.78, 0.73159, 2, 16, 17.87, -6.33, 0.21256, 18, 16.91, -5.19, 0.78744, 2, 16, 12.63, -5.56, 0.12445, 18, 11.74, -4.02, 0.87555, 2, 16, 7.36, -5.65, 0.07059, 18, 6.48, -3.7, 0.92941, 2, 16, 2.2, -6.25, 0.05727, 18, 1.29, -3.9, 0.94273, 2, 16, -3.17, -7.11, 0.07037, 18, -4.13, -4.34, 0.92963, 2, 16, -8.34, -8.37, 0.07944, 18, -9.38, -5.2, 0.92056, 2, 16, -13.22, -10.4, 0.09926, 18, -14.41, -6.85, 0.90074, 2, 16, -17.7, -13.03, 0.17406, 18, -19.08, -9.13, 0.82594, 2, 16, -20.5, -13.38, 0.29634, 18, -21.9, -9.26, 0.70366, 2, 16, -23.01, -7.12, 0.58226, 18, -23.92, -2.82, 0.41774, 2, 16, -23.02, -0.38, 0.72188, 18, -23.4, 3.89, 0.27812, 2, 16, -23.03, 6.35, 0.88192, 18, -22.89, 10.61, 0.11808, 2, 16, -21.13, 11.29, 0.94872, 18, -20.62, 15.38, 0.05128, 2, 16, -17.29, 17.17, 0.99246, 18, -16.33, 20.95, 0.00754, 2, 16, -11.19, 21.21, 0.99929, 18, -9.95, 24.51, 0.00071, 1, 16, -7.07, 23.02, 1], "hull": 23, "edges": [0, 44, 0, 2, 2, 4, 4, 6, 6, 8, 30, 32, 36, 38, 38, 40, 40, 42, 42, 44, 28, 30, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 12, 14, 14, 16, 8, 10, 10, 12, 32, 34, 34, 36], "width": 48, "height": 49}}, "Eye_L": {"Eye_r": {"x": 0.5, "y": -2.5, "width": 61, "height": 61}}, "Eye_L2": {"Eye_r": {"x": 0.5, "y": -2.5, "width": 61, "height": 61}}, "Eye_Pupil_r": {"Eye_cross_L": {"width": 29, "height": 28}, "Eye_Pupil_r": {"width": 14, "height": 16}}, "Eye_Pupil_r2": {"Eye_cross_R": {"width": 25, "height": 28}, "Eye_Pupil_r": {"width": 14, "height": 16}}, "Eye_red_L": {"Eye_red_r": {"width": 48, "height": 48}}, "Eye_red_L2": {"Eye_red_r": {"width": 48, "height": 48}}, "Leg_L_outline": {"Leg_l_outline": {"type": "mesh", "uvs": [0.60238, 0.0075, 0.79129, 0.05659, 0.93364, 0.18182, 1, 0.3317, 1, 0.4841, 0.93275, 0.65191, 0.82749, 0.82937, 0.60956, 0.97806, 0.30512, 0.97722, 0.16151, 0.85373, 0.05072, 0.69378, 0.01606, 0.52126, 0.0177, 0.34009, 0.01589, 0.22737, 0.16044, 0.08795, 0.34472, 0.01072], "triangles": [6, 7, 9, 7, 8, 9, 9, 10, 6, 6, 10, 5, 5, 10, 4, 4, 12, 1, 0, 12, 14, 14, 15, 0, 4, 10, 11, 12, 4, 11, 3, 4, 1, 12, 0, 1, 1, 2, 3, 12, 13, 14], "vertices": [2, 22, -21.29, 6.17, 0.99989, 23, -49.49, 6.17, 0.00011, 2, 22, -17.07, 16.39, 0.99492, 23, -45.26, 16.39, 0.00508, 2, 22, -6.2, 24.12, 0.96554, 23, -34.4, 24.12, 0.03446, 2, 22, 6.82, 27.76, 0.86503, 23, -21.37, 27.76, 0.13497, 2, 22, 20.08, 27.82, 0.66686, 23, -8.11, 27.82, 0.33314, 2, 22, 34.7, 24.25, 0.40641, 23, 6.5, 24.25, 0.59359, 2, 22, 50.16, 18.63, 0.17745, 23, 21.96, 18.63, 0.82255, 2, 22, 63.14, 6.91, 0.04837, 23, 34.95, 6.91, 0.95163, 2, 22, 63.14, -9.53, 0.02851, 23, 34.94, -9.53, 0.97149, 2, 22, 52.43, -17.33, 0.1214, 23, 24.23, -17.33, 0.8786, 2, 22, 38.54, -23.37, 0.32196, 23, 10.34, -23.37, 0.67804, 2, 22, 23.53, -25.3, 0.58815, 23, -4.66, -25.3, 0.41185, 2, 22, 7.77, -25.28, 0.81947, 23, -20.42, -25.28, 0.18053, 2, 22, -2.03, -25.42, 0.9507, 23, -30.23, -25.42, 0.0493, 2, 22, -14.2, -17.66, 0.9942, 23, -42.39, -17.66, 0.0058, 2, 22, -20.96, -7.74, 0.99958, 23, -49.15, -7.74, 0.00042], "hull": 16, "edges": [0, 30, 0, 2, 6, 8, 12, 14, 14, 16, 24, 26, 20, 22, 22, 24, 16, 18, 18, 20, 26, 28, 28, 30, 2, 4, 4, 6, 8, 10, 10, 12], "width": 54, "height": 87}}, "Leg_R_outline": {"Leg_l_outline": {"type": "mesh", "uvs": [0.60238, 0.0075, 0.79129, 0.05659, 0.93364, 0.18182, 1, 0.3317, 1, 0.4841, 0.93275, 0.65191, 0.82749, 0.82937, 0.60956, 0.97806, 0.30512, 0.97722, 0.16151, 0.85373, 0.05072, 0.69378, 0.01606, 0.52126, 0.0177, 0.34009, 0.01589, 0.22737, 0.16044, 0.08795, 0.34472, 0.01072], "triangles": [6, 7, 9, 7, 8, 9, 9, 10, 6, 6, 10, 5, 5, 10, 4, 4, 12, 1, 0, 12, 14, 14, 15, 0, 4, 10, 11, 12, 4, 11, 3, 4, 1, 12, 0, 1, 1, 2, 3, 12, 13, 14], "vertices": [2, 20, -21.29, 6.17, 0.99989, 21, -49.49, 6.17, 0.00011, 2, 20, -17.07, 16.39, 0.99492, 21, -45.26, 16.39, 0.00508, 2, 20, -6.2, 24.12, 0.96554, 21, -34.4, 24.12, 0.03446, 2, 20, 6.82, 27.76, 0.86503, 21, -21.37, 27.76, 0.13497, 2, 20, 20.08, 27.82, 0.66686, 21, -8.11, 27.82, 0.33314, 2, 20, 34.7, 24.25, 0.40641, 21, 6.5, 24.25, 0.59359, 2, 20, 50.16, 18.63, 0.17745, 21, 21.96, 18.63, 0.82255, 2, 20, 63.14, 6.91, 0.04837, 21, 34.95, 6.91, 0.95163, 2, 20, 63.14, -9.53, 0.02851, 21, 34.94, -9.53, 0.97149, 2, 20, 52.43, -17.33, 0.1214, 21, 24.23, -17.33, 0.8786, 2, 20, 38.54, -23.37, 0.32196, 21, 10.34, -23.37, 0.67804, 2, 20, 23.53, -25.3, 0.58815, 21, -4.66, -25.3, 0.41185, 2, 20, 7.77, -25.28, 0.81947, 21, -20.42, -25.28, 0.18053, 2, 20, -2.03, -25.42, 0.9507, 21, -30.23, -25.42, 0.0493, 2, 20, -14.2, -17.66, 0.9942, 21, -42.39, -17.66, 0.0058, 2, 20, -20.96, -7.74, 0.99958, 21, -49.15, -7.74, 0.00042], "hull": 16, "edges": [0, 30, 0, 2, 6, 8, 12, 14, 14, 16, 24, 26, 20, 22, 22, 24, 16, 18, 18, 20, 26, 28, 28, 30, 2, 4, 4, 6, 8, 10, 10, 12], "width": 54, "height": 87}}, "Mouth_base": {"Mouth_base": {"type": "mesh", "uvs": [0.1812, 0, 0.28256, 0.08604, 0.472, 0.1782, 0.55343, 0.1487, 0.73818, 0.05023, 0.90202, 0.05101, 0.95893, 0.14145, 0.99262, 0.33224, 0.99263, 0.64126, 0.95064, 0.85393, 0.87943, 0.97522, 0.73603, 0.97496, 0.63938, 0.91514, 0.54272, 0.85532, 0.4716, 0.84669, 0.35414, 0.91068, 0.23668, 0.97467, 0.09297, 0.97022, 0.03468, 0.84644, 0.00725, 0.70395, 0.00723, 0.35938, 0.04824, 0.12678, 0.12848, 0], "triangles": [14, 2, 13, 13, 3, 12, 11, 12, 4, 5, 8, 9, 7, 5, 6, 8, 5, 7, 0, 16, 17, 18, 19, 20, 20, 17, 18, 2, 15, 1, 13, 2, 3, 12, 3, 4, 22, 20, 21, 22, 17, 20, 17, 22, 0, 1, 16, 0, 5, 10, 4, 14, 15, 2, 16, 1, 15, 10, 11, 4, 9, 10, 5], "vertices": [1, 13, -43.88, 13.96, 1, 1, 13, -30.4, 10.6, 1, 1, 13, -5.2, 7.01, 1, 1, 13, 5.63, 8.16, 1, 1, 13, 30.2, 12, 1, 1, 13, 51.99, 11.97, 1, 1, 13, 59.56, 8.44, 1, 1, 13, 64.04, 1, 1, 1, 13, 64.04, -11.05, 1, 1, 13, 58.46, -19.34, 1, 2, 13, 48.99, -24.07, 0.96118, 14, 48.99, -11.34, 0.03882, 2, 13, 29.91, -24.06, 0.72475, 14, 29.91, -11.33, 0.27525, 2, 13, 17.06, -21.73, 0.52404, 14, 17.06, -9, 0.47596, 2, 13, 4.2, -19.4, 0.39542, 14, 4.2, -6.67, 0.60458, 2, 13, -5.26, -19.06, 0.35983, 14, -5.26, -6.33, 0.64017, 2, 13, -20.88, -21.56, 0.48684, 14, -20.88, -8.83, 0.51316, 2, 13, -36.5, -24.05, 0.70048, 14, -36.5, -11.32, 0.29952, 2, 13, -55.61, -23.88, 0.98352, 14, -55.61, -11.15, 0.01648, 1, 13, -63.37, -19.05, 1, 1, 13, -67.01, -13.5, 1, 1, 13, -67.02, -0.06, 1, 1, 13, -61.56, 9.01, 1, 1, 13, -50.89, 13.96, 1], "hull": 23, "edges": [0, 44, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 14, 16, 18, 20, 20, 22, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 42, 44, 28, 30, 30, 32, 22, 24, 24, 26, 12, 14, 16, 18, 40, 42], "width": 133, "height": 39}}, "Mouth_open": {"Mouth_open": {"type": "mesh", "uvs": [0.07588, 0.04725, 0.26277, 0.15687, 0.45626, 0.21431, 0.56991, 0.20347, 0.74636, 0.08401, 0.94491, 0.08551, 0.99129, 0.30523, 0.99095, 0.73779, 0.93693, 0.87101, 0.80067, 0.87286, 0.70289, 0.79, 0.60512, 0.70714, 0.46803, 0.71378, 0.29541, 0.85689, 0.12279, 1, 0.07544, 0.97876, 0.02892, 0.84683, 0.00504, 0.48966, 0.01967, 0.28331], "triangles": [11, 3, 4, 12, 2, 3, 12, 3, 11, 6, 8, 5, 10, 11, 4, 18, 15, 17, 16, 17, 15, 13, 1, 2, 13, 2, 12, 5, 9, 4, 7, 8, 6, 9, 10, 4, 5, 8, 9, 18, 14, 15, 0, 14, 18, 1, 14, 0, 13, 14, 1], "vertices": [1, 13, -46.71, 3.87, 1, 1, 13, -26.34, 1.35, 1, 1, 13, -5.25, 0.03, 1, 1, 13, 7.14, 0.28, 1, 1, 13, 26.37, 3.03, 1, 1, 13, 48.02, 2.99, 1, 1, 13, 53.07, -2.06, 1, 2, 13, 53.04, -12.01, 0.98537, 14, 53.04, 0.72, 0.01463, 2, 13, 47.15, -15.07, 0.91101, 14, 47.15, -2.34, 0.08899, 2, 13, 32.29, -15.12, 0.66094, 14, 32.29, -2.39, 0.33906, 2, 13, 21.64, -13.21, 0.47124, 14, 21.64, -0.48, 0.52876, 2, 13, 10.98, -11.31, 0.31577, 14, 10.98, 1.43, 0.68423, 2, 13, -3.96, -11.46, 0.30072, 14, -3.96, 1.27, 0.69928, 2, 13, -22.78, -14.75, 0.43383, 14, -22.78, -2.02, 0.56617, 2, 13, -41.59, -18.04, 0.80584, 14, -41.59, -5.31, 0.19416, 2, 13, -46.76, -17.55, 0.88448, 14, -46.76, -4.82, 0.11552, 1, 13, -51.83, -14.52, 1, 1, 13, -54.43, -6.3, 1, 1, 13, -52.83, -1.56, 1], "hull": 19, "edges": [0, 36, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 28, 30, 30, 32, 34, 36, 32, 34, 18, 20, 20, 22, 24, 26, 26, 28, 0, 2, 2, 4], "width": 109, "height": 23}}, "Saliva": {"Saliva": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.16466, 1, 0], "triangles": [0, 1, 2, 0, 2, 3], "vertices": [1, 15, 10.37, -2.56, 1, 1, 15, -7.63, -2.56, 1, 2, 13, -44.98, -15.66, 0.82, 14, -44.98, -2.93, 0.18, 2, 13, -26.98, -12.04, 0.43429, 14, -26.98, 0.69, 0.56571], "hull": 4, "edges": [0, 2, 0, 6, 2, 4, 4, 6], "width": 18, "height": 22}}, "Slippy_body": {"Slippy_body": {"type": "mesh", "uvs": [0.82461, 0.00617, 0.85945, 0.06614, 0.88606, 0.13907, 0.90371, 0.12297, 0.91614, 0.00977, 0.96556, 0.00981, 0.99438, 0.1484, 0.99442, 0.28915, 0.95746, 0.30473, 0.9453, 0.34463, 0.94941, 0.47484, 0.94351, 0.59892, 0.90697, 0.72843, 0.8585, 0.80571, 0.80812, 0.86017, 0.74237, 0.90643, 0.69009, 0.94101, 0.63781, 0.97559, 0.578, 0.98182, 0.52152, 0.9877, 0.46505, 0.99358, 0.41668, 0.98558, 0.36831, 0.97758, 0.31289, 0.94242, 0.24978, 0.90238, 0.19672, 0.86695, 0.14926, 0.81449, 0.1066, 0.74665, 0.07922, 0.68018, 0.05446, 0.59371, 0.05249, 0.42906, 0.05568, 0.35222, 0.04219, 0.30165, 0.00573, 0.29211, 0.00562, 0.12822, 0.03981, 0.01024, 0.08256, 0.00626, 0.09087, 0.07402, 0.09768, 0.13968, 0.11564, 0.13524, 0.1443, 0.06762, 0.17297, 0, 0.21326, 0, 0.27353, 0.07965, 0.32157, 0.07151, 0.37018, 0.06326, 0.41879, 0.05502, 0.47276, 0.05641, 0.52674, 0.05781, 0.58072, 0.05921, 0.63251, 0.06522, 0.68146, 0.07017, 0.73556, 0.07389, 0.78636, 0], "triangles": [5, 3, 4, 8, 5, 6, 8, 6, 7, 8, 3, 5, 9, 3, 8, 2, 9, 10, 9, 2, 3, 11, 2, 10, 12, 2, 11, 0, 13, 53, 13, 1, 2, 13, 2, 12, 1, 13, 0, 13, 52, 53, 14, 52, 13, 15, 52, 14, 15, 16, 51, 15, 51, 52, 16, 17, 50, 16, 50, 51, 18, 48, 49, 18, 49, 50, 18, 50, 17, 48, 18, 19, 48, 19, 47, 32, 33, 34, 37, 32, 35, 37, 35, 36, 32, 37, 38, 32, 34, 35, 31, 32, 38, 30, 39, 29, 31, 39, 30, 38, 39, 31, 28, 29, 39, 27, 28, 39, 26, 40, 41, 41, 42, 26, 26, 27, 39, 39, 40, 26, 42, 43, 26, 25, 26, 43, 24, 25, 43, 23, 43, 44, 24, 43, 23, 45, 23, 44, 22, 23, 45, 21, 45, 46, 22, 45, 21, 21, 46, 47, 21, 47, 20, 19, 20, 47], "vertices": [3, 10, -148.26, -44.23, 2e-05, 3, 113.26, 44.23, 0.00309, 4, 78.26, 44, 0.99689, 2, 3, 125.53, 38.23, 0.00048, 4, 90.53, 38.01, 0.99952, 2, 3, 134.89, 30.94, 3e-05, 4, 99.89, 30.71, 0.99997, 1, 4, 106.11, 32.32, 1, 1, 4, 110.48, 43.64, 1, 1, 4, 127.88, 43.64, 1, 1, 4, 138.02, 29.78, 1, 1, 4, 138.03, 15.7, 1, 1, 4, 125.03, 14.15, 1, 1, 4, 120.75, 10.16, 1, 2, 3, 157.19, -2.64, 1e-05, 4, 122.19, -2.86, 0.99999, 2, 3, 155.12, -15.05, 0.00013, 4, 120.12, -15.27, 0.99987, 3, 10, -177.25, 28, 1e-05, 3, 142.25, -28, 0.00126, 4, 107.25, -28.22, 0.99873, 3, 10, -160.19, 35.73, 0.00016, 3, 125.19, -35.73, 0.00596, 4, 90.19, -35.95, 0.99388, 3, 10, -142.46, 41.17, 0.00183, 3, 107.46, -41.17, 0.02345, 4, 72.46, -41.4, 0.97472, 3, 10, -119.31, 45.8, 0.00851, 3, 84.31, -45.8, 0.05959, 4, 49.31, -46.02, 0.93189, 3, 10, -100.91, 49.26, 0.02842, 3, 65.91, -49.26, 0.12009, 4, 30.91, -49.48, 0.85149, 3, 10, -82.51, 52.72, 0.07396, 3, 47.51, -52.72, 0.19911, 4, 12.51, -52.94, 0.72693, 3, 10, -61.45, 53.34, 0.15719, 3, 26.45, -53.34, 0.27589, 4, -8.55, -53.56, 0.56692, 3, 10, -41.58, 53.93, 0.28213, 3, 6.58, -53.93, 0.32239, 4, -28.42, -54.15, 0.39548, 3, 10, -21.7, 54.52, 0.43868, 3, -13.3, -54.52, 0.31929, 4, -48.3, -54.74, 0.24203, 3, 10, -4.67, 53.72, 0.6042, 3, -30.33, -53.72, 0.26834, 4, -65.33, -53.94, 0.12746, 3, 10, 12.35, 52.92, 0.75257, 3, -47.35, -52.92, 0.19082, 4, -82.35, -53.14, 0.05661, 3, 10, 31.86, 49.4, 0.86538, 3, -66.86, -49.4, 0.11395, 4, -101.86, -49.62, 0.02067, 3, 10, 54.08, 45.4, 0.93772, 3, -89.08, -45.4, 0.05628, 4, -124.08, -45.62, 0.00601, 3, 10, 72.75, 41.85, 0.97656, 3, -107.75, -41.85, 0.02213, 4, -142.75, -42.08, 0.00131, 3, 10, 89.46, 36.61, 0.99421, 3, -124.46, -36.61, 0.00567, 4, -159.46, -36.83, 0.00012, 3, 10, 104.48, 29.82, 0.99882, 3, -139.48, -29.82, 0.00117, 4, -174.48, -30.04, 1e-05, 2, 10, 114.12, 23.18, 0.99986, 3, -149.12, -23.18, 0.00014, 2, 10, 122.83, 14.53, 1, 3, -157.83, -14.53, 0, 1, 10, 123.52, -1.94, 1, 1, 10, 122.4, -9.62, 1, 1, 10, 127.15, -14.68, 1, 1, 10, 139.98, -15.63, 1, 1, 10, 140.02, -32.02, 1, 1, 10, 127.99, -43.82, 1, 1, 10, 112.94, -44.22, 1, 1, 10, 110.01, -37.44, 1, 1, 10, 107.62, -30.87, 1, 2, 10, 101.3, -31.32, 0.99998, 3, -136.3, 31.32, 2e-05, 2, 10, 91.2, -38.08, 0.99954, 3, -126.2, 38.08, 0.00046, 3, 10, 81.11, -44.84, 0.99701, 3, -116.11, 44.84, 0.00298, 4, -151.11, 44.62, 1e-05, 3, 10, 66.93, -44.84, 0.98771, 3, -101.93, 44.84, 0.01201, 4, -136.93, 44.62, 0.00027, 3, 10, 45.72, -36.88, 0.95045, 3, -80.72, 36.88, 0.04665, 4, -115.72, 36.65, 0.0029, 3, 10, 28.81, -37.69, 0.87801, 3, -63.81, 37.69, 0.10827, 4, -98.81, 37.47, 0.01372, 3, 10, 11.7, -38.52, 0.75798, 3, -46.7, 38.52, 0.19661, 4, -81.7, 38.29, 0.04542, 3, 10, -5.41, -39.34, 0.59499, 3, -29.59, 39.34, 0.29015, 4, -64.59, 39.12, 0.11486, 3, 10, -24.41, -39.2, 0.41364, 3, -10.59, 39.2, 0.35252, 4, -45.59, 38.98, 0.23384, 3, 10, -43.41, -39.06, 0.24811, 3, 8.41, 39.06, 0.35446, 4, -26.59, 38.84, 0.39744, 3, 10, -62.41, -38.92, 0.12483, 3, 27.41, 38.92, 0.29483, 4, -7.59, 38.7, 0.58034, 3, 10, -80.64, -38.32, 0.05087, 3, 45.64, 38.32, 0.20161, 4, 10.64, 38.1, 0.74752, 3, 10, -97.87, -37.82, 0.01598, 3, 62.87, 37.82, 0.11176, 4, 27.87, 37.6, 0.87226, 3, 10, -116.92, -37.45, 0.00356, 3, 81.92, 37.45, 0.0483, 4, 46.92, 37.23, 0.94814, 3, 10, -134.8, -44.84, 0.00036, 3, 99.8, 44.84, 0.01244, 4, 64.8, 44.62, 0.9872], "hull": 54, "edges": [0, 106, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 66, 68, 68, 70, 70, 72, 76, 78, 82, 84, 84, 86, 104, 106, 16, 18, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 72, 74, 74, 76, 0, 2, 2, 4, 78, 80, 80, 82, 102, 104, 64, 66, 14, 16, 106, 26, 0, 26, 84, 52, 82, 52, 58, 78, 22, 4], "width": 352, "height": 100}}, "Slippy_leg_L": {"Slippy_leg_l": {"type": "mesh", "uvs": [0.8671, 0.11045, 0.97098, 0.22598, 0.97617, 0.42427, 0.97052, 0.59862, 0.8285, 0.79248, 0.62576, 0.98633, 0.3556, 0.98972, 0.20125, 0.87275, 0.09461, 0.72554, 0.04614, 0.5543, 0.04538, 0.36919, 0.0469, 0.19975, 0.27548, 0.01178, 0.69968, 0.0136], "triangles": [8, 9, 2, 3, 8, 2, 4, 8, 3, 7, 8, 4, 5, 7, 4, 6, 7, 5, 12, 9, 10, 2, 9, 1, 1, 9, 0, 11, 12, 10, 12, 0, 9, 13, 0, 12], "vertices": [2, 22, -7.81, 15.99, 0.98546, 23, -36.01, 15.99, 0.01454, 2, 22, 0.72, 20.49, 0.91013, 23, -27.48, 20.49, 0.08987, 2, 22, 15.39, 20.77, 0.72614, 23, -12.8, 20.77, 0.27386, 2, 22, 28.29, 20.58, 0.4593, 23, 0.1, 20.58, 0.5407, 2, 22, 42.66, 14.54, 0.20334, 23, 14.47, 14.54, 0.79666, 2, 22, 57.04, 5.88, 0.0542, 23, 28.85, 5.88, 0.9458, 2, 22, 57.34, -5.74, 0.01045, 23, 29.15, -5.74, 0.98955, 2, 22, 48.72, -12.41, 0.07291, 23, 20.52, -12.41, 0.92709, 2, 22, 37.84, -17.04, 0.23829, 23, 9.65, -17.04, 0.76171, 2, 22, 25.18, -19.18, 0.49743, 23, -3.02, -19.18, 0.50257, 2, 22, 11.48, -19.27, 0.75548, 23, -16.71, -19.27, 0.24452, 2, 22, -1.06, -19.25, 0.92289, 23, -29.25, -19.25, 0.07711, 2, 22, -15.01, -9.48, 0.98868, 23, -43.2, -9.48, 0.01132, 2, 22, -14.95, 8.76, 0.99925, 23, -43.14, 8.76, 0.00075], "hull": 14, "edges": [10, 12, 22, 24, 24, 26, 6, 8, 8, 10, 2, 4, 4, 6, 20, 22, 16, 18, 18, 20, 12, 14, 14, 16, 2, 0, 0, 26], "width": 43, "height": 74}}, "Slippy_leg_R": {"Slippy_leg_l": {"type": "mesh", "uvs": [0.8671, 0.11045, 0.97098, 0.22598, 0.97617, 0.42427, 0.97052, 0.59862, 0.8285, 0.79248, 0.62576, 0.98633, 0.3556, 0.98972, 0.20125, 0.87275, 0.09461, 0.72554, 0.04614, 0.5543, 0.04538, 0.36919, 0.0469, 0.19975, 0.27548, 0.01178, 0.69968, 0.0136], "triangles": [8, 9, 2, 3, 8, 2, 4, 8, 3, 7, 8, 4, 5, 7, 4, 6, 7, 5, 12, 9, 10, 2, 9, 1, 1, 9, 0, 11, 12, 10, 12, 0, 9, 13, 0, 12], "vertices": [2, 20, -7.81, 15.99, 0.98546, 21, -36.01, 15.99, 0.01454, 2, 20, 0.72, 20.49, 0.91013, 21, -27.48, 20.49, 0.08987, 2, 20, 15.39, 20.77, 0.72614, 21, -12.8, 20.77, 0.27386, 2, 20, 28.29, 20.58, 0.4593, 21, 0.1, 20.58, 0.5407, 2, 20, 42.66, 14.54, 0.20334, 21, 14.47, 14.54, 0.79666, 2, 20, 57.04, 5.88, 0.0542, 21, 28.85, 5.88, 0.9458, 2, 20, 57.34, -5.74, 0.01045, 21, 29.15, -5.74, 0.98955, 2, 20, 48.72, -12.41, 0.07291, 21, 20.52, -12.41, 0.92709, 2, 20, 37.84, -17.04, 0.23829, 21, 9.65, -17.04, 0.76171, 2, 20, 25.18, -19.18, 0.49743, 21, -3.02, -19.18, 0.50257, 2, 20, 11.48, -19.27, 0.75548, 21, -16.71, -19.27, 0.24452, 2, 20, -1.06, -19.25, 0.92289, 21, -29.25, -19.25, 0.07711, 2, 20, -15.01, -9.48, 0.98868, 21, -43.2, -9.48, 0.01132, 2, 20, -14.95, 8.76, 0.99925, 21, -43.14, 8.76, 0.00075], "hull": 14, "edges": [10, 12, 22, 24, 24, 26, 6, 8, 8, 10, 2, 4, 4, 6, 20, 22, 16, 18, 18, 20, 12, 14, 14, 16, 2, 0, 0, 26], "width": 43, "height": 74}}, "Teeth": {"Teeth": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.12379, 0.70856, 0.12504, 1, 0], "triangles": [1, 2, 3, 3, 4, 0, 1, 3, 0], "vertices": [1, 12, 31.47, -18.06, 1, 1, 12, -24.53, -18.06, 1, 2, 12, -24.53, 5.6, 0.12379, 13, -23.98, 0.62, 0.87621, 2, 12, 15.15, 5.56, 0.12504, 13, 15.7, 0.58, 0.87496, 1, 13, 32.02, 3.96, 1], "hull": 5, "edges": [0, 2, 0, 8, 2, 4, 6, 4, 6, 8], "width": 56, "height": 27}}}}], "animations": {"t0_000000": {"slots": {"Body_outline": {"rgba": [{"color": "0000007d"}]}, "Leg_L_outline": {"rgba": [{"color": "0000007d"}]}, "Leg_R_outline": {"rgba": [{"color": "0000007d"}]}}}, "t0_405c80": {"slots": {"Body_outline": {"rgba": [{"color": "405c80ff"}]}, "Leg_L_outline": {"rgba": [{"color": "405c80ff"}]}, "Leg_R_outline": {"rgba": [{"color": "405c80ff"}]}}}, "t1_Death": {"slots": {"blot": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff00"}], "attachment": [{}, {"time": 0.1667, "name": "blot"}]}, "blot_drop2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.1667, "name": "blot_drop2"}, {"time": 0.4}]}, "blot_drop3": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.1667, "name": "blot_drop2"}, {"time": 0.5}]}, "blot_drop4": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.1667, "name": "blot_drop2"}, {"time": 0.4667}]}, "blot_drop5": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.1667, "name": "blot_drop1"}, {"time": 0.4}]}, "blot_drop6": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.1667, "name": "blot_drop2"}, {"time": 0.4667}]}, "blot_drop7": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.1667, "name": "blot_drop2"}, {"time": 0.5333}]}, "blot_drop8": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2, "name": "blot_drop1"}, {"time": 0.5667}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.1667, "name": "blot_drop1"}, {"time": 0.3667}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.1667, "name": "blot_drop1"}, {"time": 0.3333}]}, "Body_outline": {"attachment": [{"name": "Body_outline"}, {"time": 0.3}]}, "Brow_l": {"attachment": [{"name": "Brow_l"}, {"time": 0.3}]}, "Brow_r": {"attachment": [{"name": "Brow_r"}, {"time": 0.3}]}, "Eyelid_l_r": {"attachment": [{"name": "Eyelid_l_r"}, {"time": 0.3}]}, "Eyelid_l_r2": {"attachment": [{"name": "Eyelid_l_r"}, {"time": 0.3}]}, "Eyelid_u_r": {"attachment": [{"name": "Eyelid_u_r"}, {"time": 0.3}]}, "Eyelid_u_r2": {"attachment": [{"name": "Eyelid_u_r"}, {"time": 0.3}]}, "Eye_L": {"attachment": [{"name": "Eye_r"}, {"time": 0.3}]}, "Eye_L2": {"attachment": [{"name": "Eye_r"}, {"time": 0.3}]}, "Eye_Pupil_r": {"attachment": [{"name": "Eye_Pupil_r"}, {"time": 0.0333, "name": "Eye_cross_L"}, {"time": 0.3}]}, "Eye_Pupil_r2": {"attachment": [{"name": "Eye_Pupil_r"}, {"time": 0.0333, "name": "Eye_cross_R"}, {"time": 0.3}]}, "Eye_red_L": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "Eye_red_r"}, {"time": 0.3}]}, "Eye_red_L2": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "Eye_red_r"}, {"time": 0.3}]}, "Leg_L_outline": {"attachment": [{"name": "Leg_l_outline"}, {"time": 0.3}]}, "Leg_R_outline": {"attachment": [{"name": "Leg_l_outline"}, {"time": 0.3}]}, "Mouth_base": {"attachment": [{"name": "Mouth_base"}, {"time": 0.3}]}, "Mouth_open": {"attachment": [{"name": "Mouth_open"}, {"time": 0.3}]}, "Saliva": {"attachment": [{"name": "Saliva"}, {"time": 0.3}]}, "Slippy_body": {"attachment": [{"name": "Slippy_body"}, {"time": 0.3}]}, "Slippy_leg_L": {"attachment": [{"name": "Slippy_leg_l"}, {"time": 0.3}]}, "Slippy_leg_R": {"attachment": [{"name": "Slippy_leg_l"}, {"time": 0.3}]}, "Teeth": {"attachment": [{"name": "<PERSON><PERSON>"}, {"time": 0.3}]}}, "bones": {"blot": {"translate": [{"x": 0.54, "y": 15.4, "curve": "stepped"}, {"time": 0.2, "x": 0.54, "y": 15.4, "curve": [0.322, 0.54, 0.444, 0.53, 0.276, 7.46, 0.444, 1.44]}, {"time": 0.5667, "x": 0.53, "y": 1.44}], "scale": [{"x": 0.7, "y": 0.7, "curve": "stepped"}, {"time": 0.1667, "x": 0.449, "y": 0.449, "curve": [0.211, 1.137, 0.367, 1.2, 0.211, 1.137, 0.367, 1.2]}, {"time": 0.4667, "x": 1.2, "y": 1.2}]}, "blot_drops_control": {"translate": [{"curve": "stepped"}, {"time": 0.2333, "x": -0.54, "y": -0.54, "curve": [0.456, -0.54, 0.678, 0, 0.379, -0.67, 0.678, -16.59]}, {"time": 0.9, "y": -16.59}]}, "blot_drop2": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": [0.233, 0, 0.3, -67.84]}, {"time": 0.3667, "value": -67.84}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "x": 41.44, "y": 7.91, "curve": [0.231, 77.65, 0.322, 273.93, 0.227, -38.99, 0.335, -211.23]}, {"time": 0.4, "x": 273.93, "y": -398.86}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": [0.378, 1, 0.422, 0.4, 0.378, 1, 0.422, 0.4]}, {"time": 0.4667, "x": 0.4, "y": 0.4}]}, "blot_drop3": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "value": -35.08, "curve": [0.229, 32.76, 0.389, 77.81]}, {"time": 0.5, "value": 77.81}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "x": -74.68, "y": 24.95, "curve": [0.252, -115.07, 0.389, -322.02, 0.263, 261.05, 0.407, -337.68]}, {"time": 0.5, "x": -322.02, "y": -605.72}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3667, "curve": [0.411, 1, 0.456, 0.4, 0.411, 1, 0.456, 0.4]}, {"time": 0.5, "x": 0.4, "y": 0.4}]}, "blot_drop4": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "value": 16.41, "curve": [0.263, 68.37, 0.367, 77.81]}, {"time": 0.4667, "value": 77.81}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "x": -48.78, "y": -9.58, "curve": [0.221, -164.38, 0.367, -211.51, 0.244, -53.5, 0.395, -476.89]}, {"time": 0.4667, "x": -211.51, "y": -781.84}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": [0.378, 1, 0.422, 0.4, 0.378, 1, 0.422, 0.4]}, {"time": 0.4667, "x": 0.4, "y": 0.4}]}, "blot_drop_s1": {"rotate": [{"curve": "stepped"}, {"time": 0.1667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "x": 54.1, "y": 60.11, "curve": [0.234, 209.62, 0.3, 276.96, 0.233, -14.18, 0.29, -99.2]}, {"time": 0.3667, "x": 276.96, "y": -195.12}]}, "blot_drop_s2": {"rotate": [{"curve": "stepped"}, {"time": 0.1667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "x": -53.64, "y": 83.69, "curve": [0.214, -164.57, 0.309, -315.66, 0.257, 159.13, 0.326, 84.28]}, {"time": 0.4, "x": -370.66, "y": -36.32}]}, "blot_drop5": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "value": 103.14, "curve": [0.256, 103.14, 0.419, 97.56]}, {"time": 0.4333, "value": 97.56}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "x": 35.48, "y": -53.58, "curve": [0.255, 58.13, 0.367, 37.99, 0.244, -113.89, 0.383, -373.65]}, {"time": 0.4667, "x": 37.99, "y": -614.89}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": [0.378, 1, 0.422, 0.4, 0.378, 1, 0.422, 0.4]}, {"time": 0.4667, "x": 0.4, "y": 0.4}]}, "blot_drop_s3": {"rotate": [{"curve": "stepped"}, {"time": 0.1667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "x": 10.55, "y": -19.73, "curve": [0.223, -6.01, 0.27, -21.01, 0.214, -79.55, 0.261, -201.42]}, {"time": 0.3333, "x": -31.65, "y": -390.13}]}, "blot_drop6": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "value": -75.4, "curve": [0.209, -120.98, 0.293, -263.98]}, {"time": 0.4, "value": -261.68}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "x": 9.31, "y": 91.31, "curve": [0.258, 118.46, 0.411, 297.6, 0.222, 320.53, 0.381, 364.92]}, {"time": 0.5333, "x": 297.6, "y": -1347.33}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4, "curve": [0.444, 1, 0.489, 0.4, 0.444, 1, 0.489, 0.4]}, {"time": 0.5333, "x": 0.4, "y": 0.4}]}, "blot_drop_s4": {"rotate": [{"curve": "stepped"}, {"time": 0.2}], "translate": [{"curve": "stepped"}, {"time": 0.2, "x": -53.64, "y": 83.69, "curve": [0.288, -170.51, 0.44, -244.31, 0.36, -107.5, 0.476, -316.02]}, {"time": 0.5667, "x": -277.78, "y": -598.88}]}, "Eye_R_Eyelid_t": {"translate": [{"x": -2.12, "y": 27.44}]}, "Eye_L_Eyelid_t": {"translate": [{"x": -2.12, "y": 27.44}]}, "Eye_L": {"translate": [{}], "scale": [{"curve": [0.033, 1, 0.067, 1.547, 0.033, 1, 0.067, 1.547]}, {"time": 0.1, "x": 1.547, "y": 1.547}]}, "Eye_R": {"translate": [{}], "scale": [{"curve": [0.033, 1, 0.067, 1.547, 0.033, 1, 0.067, 1.547]}, {"time": 0.1, "x": 1.547, "y": 1.547}]}, "Main_Cntr": {"rotate": [{}], "translate": [{"y": -9.16, "curve": [0.029, 0, 0.038, 0, 0.018, -0.68, 0.038, 0.1]}, {"time": 0.0667, "y": 0.1, "curve": [0.089, 0, 0.111, 0, 0.089, 0.1, 0.113, -0.63]}, {"time": 0.1333, "y": -9.16}], "scale": [{"curve": "stepped"}, {"time": 0.1333}, {"time": 0.2333, "x": 0.62, "y": 0.62}]}, "Low_Cntr": {"scale": [{"x": 1.05, "y": 0.95, "curve": [0.016, 1.05, 0.018, 0.8, 0.016, 0.95, 0.018, 1.3]}, {"time": 0.0333, "x": 0.8, "y": 1.3, "curve": [0.078, 0.8, 0.121, 1, 0.078, 1.3, 0.121, 1]}, {"time": 0.1667, "curve": [0.178, 1, 0.189, 1.1, 0.178, 1, 0.189, 0.9]}, {"time": 0.2, "x": 1.1, "y": 0.9, "curve": [0.244, 1.1, 0.289, 1.05, 0.244, 0.9, 0.289, 0.95]}, {"time": 0.3333, "x": 1.05, "y": 0.95}], "shear": [{}]}, "Eye_R_Pupil": {"translate": [{"x": -3.97, "y": -5.53}, {"time": 0.1, "x": -6.66, "y": 1.99}], "scale": [{"x": 0.694, "y": 0.694}, {"time": 0.1, "x": 0.98, "y": 0.98}]}, "Eye_L_Pupil": {"translate": [{"x": -3.97, "y": -5.53}, {"time": 0.1, "x": -4.46, "y": 1.99}], "scale": [{"x": 0.694, "y": 0.694}, {"time": 0.1, "x": 0.861, "y": 0.861}]}, "Mouth": {"translate": [{}]}, "Mouth_Bottom": {"translate": [{"curve": [0.022, 0, 0.044, 0, 0.022, 0, 0.044, 6.43]}, {"time": 0.0667, "y": 6.43, "curve": [0.111, 0, 0.156, 0, 0.111, 6.43, 0.156, 0]}, {"time": 0.2}]}, "Eye_L_Eyelid_b": {"translate": [{"x": 0.54, "y": -8.19, "curve": [0.033, 0.85, 0.067, 1.48, 0.033, -12.24, 0.067, -20.35]}, {"time": 0.1, "x": 1.48, "y": -20.35, "curve": "stepped"}, {"time": 0.2, "x": 1.48, "y": -20.35, "curve": [0.222, 1.48, 0.244, 0.85, 0.222, -20.35, 0.244, -12.24]}, {"time": 0.2667, "x": 0.54, "y": -8.19}]}, "Mouth_cntrl": {"scale": [{"x": 1.118, "y": 0.632, "curve": [0.022, 1.118, 0.044, 0.876, 0.022, 0.632, 0.044, 0.913]}, {"time": 0.0667, "x": 0.876, "y": 0.913, "curve": [0.111, 0.876, 0.156, 1.118, 0.111, 0.913, 0.156, 0.632]}, {"time": 0.2, "x": 1.118, "y": 0.632}]}, "Saliva": {"translate": [{"y": -27.68}]}, "Cntr": {"translate": [{}], "scale": [{}]}, "Brow_L": {"rotate": [{"value": -18.62, "curve": [0.022, -18.62, 0.044, -24.12]}, {"time": 0.0667, "value": -24.12, "curve": [0.111, -24.12, 0.156, -18.62]}, {"time": 0.2, "value": -18.62}], "translate": [{"x": 6.91, "y": -0.85, "curve": [0.022, 6.91, 0.044, 8.92, 0.022, -0.85, 0.044, 5.45]}, {"time": 0.0667, "x": 8.92, "y": 5.45, "curve": [0.111, 8.92, 0.156, 6.91, 0.111, 5.45, 0.156, -0.85]}, {"time": 0.2, "x": 6.91, "y": -0.85}]}, "Brow_R": {"rotate": [{"value": 24.11, "curve": [0.022, 24.11, 0.044, 29.68]}, {"time": 0.0667, "value": 29.68, "curve": [0.111, 29.68, 0.156, 24.11]}, {"time": 0.2, "value": 24.11}], "translate": [{"x": 8.64, "y": 1.27, "curve": [0.022, 8.64, 0.044, 12.37, 0.022, 1.27, 0.044, -5.25]}, {"time": 0.0667, "x": 12.37, "y": -5.25, "curve": [0.111, 12.37, 0.156, 8.64, 0.111, -5.25, 0.156, 1.27]}, {"time": 0.2, "x": 8.64, "y": 1.27}]}, "Leg_L_IK": {"translate": [{}]}, "Leg_R_IK": {"translate": [{}]}, "Eye_R_Eyelid_b": {"translate": [{"x": 0.54, "y": -8.19, "curve": [0.033, 0.86, 0.067, 1.5, 0.033, -12.34, 0.067, -20.65]}, {"time": 0.1, "x": 1.5, "y": -20.65, "curve": "stepped"}, {"time": 0.2, "x": 1.5, "y": -20.65, "curve": [0.222, 1.5, 0.244, 0.86, 0.222, -20.65, 0.244, -12.34]}, {"time": 0.2667, "x": 0.54, "y": -8.19}]}, "Right": {"rotate": [{}]}, "Left": {"rotate": [{}]}}}, "t1_Death2": {"slots": {"blot": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot"}]}, "blot_drop2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5}]}, "blot_drop3": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.6}]}, "blot_drop4": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5667}]}, "blot_drop5": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.5}]}, "blot_drop6": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5667}]}, "blot_drop7": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.6333}]}, "blot_drop8": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.3, "name": "blot_drop1"}, {"time": 0.6667}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.4667}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.4333}]}, "Body_outline": {"attachment": [{"name": "Body_outline"}, {"time": 0.4333}]}, "Brow_l": {"attachment": [{"name": "Brow_l"}, {"time": 0.4333}]}, "Brow_r": {"attachment": [{"name": "Brow_r"}, {"time": 0.4333}]}, "Eyelid_l_r": {"attachment": [{"name": "Eyelid_l_r"}, {"time": 0.4333}]}, "Eyelid_l_r2": {"attachment": [{"name": "Eyelid_l_r"}, {"time": 0.4333}]}, "Eyelid_u_r": {"attachment": [{"name": "Eyelid_u_r"}, {"time": 0.4333}]}, "Eyelid_u_r2": {"attachment": [{"name": "Eyelid_u_r"}, {"time": 0.4333}]}, "Eye_L": {"attachment": [{"name": "Eye_r"}, {"time": 0.4333}]}, "Eye_L2": {"attachment": [{"name": "Eye_r"}, {"time": 0.4333}]}, "Eye_Pupil_r": {"attachment": [{"name": "Eye_Pupil_r"}, {"time": 0.0333, "name": "Eye_cross_L"}, {"time": 0.4333}]}, "Eye_Pupil_r2": {"attachment": [{"name": "Eye_Pupil_r"}, {"time": 0.0333, "name": "Eye_cross_R"}, {"time": 0.4333}]}, "Eye_red_L": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.1, "color": "ffffffff"}], "attachment": [{"name": "Eye_red_r"}, {"time": 0.4333}]}, "Eye_red_L2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.1, "color": "ffffffff"}], "attachment": [{"name": "Eye_red_r"}, {"time": 0.4333}]}, "Leg_L_outline": {"attachment": [{"name": "Leg_l_outline"}, {"time": 0.4333}]}, "Leg_R_outline": {"attachment": [{"name": "Leg_l_outline"}, {"time": 0.4333}]}, "Mouth_base": {"attachment": [{"name": "Mouth_base"}, {"time": 0.4333}]}, "Mouth_open": {"attachment": [{"name": "Mouth_open"}, {"time": 0.4333}]}, "Saliva": {"attachment": [{"name": "Saliva"}, {"time": 0.4333}]}, "Slippy_body": {"attachment": [{"name": "Slippy_body"}, {"time": 0.4333}]}, "Slippy_leg_L": {"attachment": [{"name": "Slippy_leg_l"}, {"time": 0.4333}]}, "Slippy_leg_R": {"attachment": [{"name": "Slippy_leg_l"}, {"time": 0.4333}]}, "Teeth": {"attachment": [{"name": "<PERSON><PERSON>"}, {"time": 0.4333}]}}, "bones": {"blot": {"translate": [{"x": -12.21, "y": 15.4, "curve": [0.35, -12.21, -0.05, 0.54, 0.35, 15.4, -0.05, 52.19]}, {"time": 0.3, "x": 0.54, "y": 15.4, "curve": [0.422, 0.54, 0.544, 0.53, 0.376, 7.46, 0.544, 1.44]}, {"time": 0.6667, "x": 0.53, "y": 1.44}], "scale": [{"x": 0.7, "y": 0.7, "curve": [0.35, 0.7, 0.258, 0.313, 0.35, 0.7, 0.258, 0.313]}, {"time": 0.2667, "x": 0.449, "y": 0.449, "curve": [0.311, 1.137, 0.467, 1.2, 0.311, 1.137, 0.467, 1.2]}, {"time": 0.5667, "x": 1.2, "y": 1.2}]}, "blot_drops_control": {"translate": [{"curve": [0.296, 0, 0.037, -0.54, 0.296, 0, 0.067, -0.3]}, {"time": 0.3333, "x": -0.54, "y": -0.54, "curve": [0.556, -0.54, 0.778, 0, 0.479, -0.67, 0.778, -16.59]}, {"time": 1, "y": -16.59}]}, "blot_drop2": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": [0.333, 0, 0.4, -67.84]}, {"time": 0.4667, "value": -67.84}], "translate": [{"curve": [0.385, 0, 0.222, 16.67, 0.385, 0, 0.261, 12.65]}, {"time": 0.2667, "x": 41.44, "y": 7.91, "curve": [0.331, 77.65, 0.422, 273.93, 0.327, -38.99, 0.435, -211.23]}, {"time": 0.5, "x": 273.93, "y": -398.86}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop3": {"rotate": [{"curve": [0.336, 0, 0.25, -53.36]}, {"time": 0.2667, "value": -35.08, "curve": [0.329, 32.76, 0.489, 77.81]}, {"time": 0.6, "value": 77.81}], "translate": [{"curve": [0.336, 0, 0.184, -35.77, 0.336, 0, 0.261, 11.95]}, {"time": 0.2667, "x": -74.68, "y": 24.95, "curve": [0.352, -115.07, 0.489, -322.02, 0.363, 261.05, 0.507, -337.68]}, {"time": 0.6, "x": -322.02, "y": -605.72}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": [0.511, 1, 0.556, 0.4, 0.511, 1, 0.556, 0.4]}, {"time": 0.6, "x": 0.4, "y": 0.4}]}, "blot_drop4": {"rotate": [{"curve": [0.35, 0, 0.25, 7.49]}, {"time": 0.2667, "value": 16.41, "curve": [0.363, 68.37, 0.467, 77.81]}, {"time": 0.5667, "value": 77.81}], "translate": [{"curve": [0.35, 0, 0.254, -22.26, 0.35, 0, 0.257, -4.37]}, {"time": 0.2667, "x": -48.78, "y": -9.58, "curve": [0.321, -164.38, 0.467, -211.51, 0.344, -53.5, 0.495, -476.89]}, {"time": 0.5667, "x": -211.51, "y": -781.84}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop_s1": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": [0.406, 0, 0.252, 19.99, 0.406, 0, 0.233, 98.01]}, {"time": 0.2667, "x": 54.1, "y": 60.11, "curve": [0.334, 209.62, 0.4, 276.96, 0.333, -14.18, 0.39, -99.2]}, {"time": 0.4667, "x": 276.96, "y": -195.12}]}, "blot_drop_s2": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": [0.385, 0, 0.253, -21.58, 0.385, 0, 0.207, 33.67]}, {"time": 0.2667, "x": -53.64, "y": 83.69, "curve": [0.314, -164.57, 0.409, -315.66, 0.357, 159.13, 0.426, 84.28]}, {"time": 0.5, "x": -370.66, "y": -36.32}]}, "blot_drop5": {"rotate": [{"curve": [0.367, 0, -0.1, 103.14]}, {"time": 0.2667, "value": 103.14, "curve": [0.356, 103.14, 0.519, 97.56]}, {"time": 0.5333, "value": 97.56}], "translate": [{"curve": [0.35, 0, 0.191, 16.19, 0.35, 0, 0.229, -24.45]}, {"time": 0.2667, "x": 35.48, "y": -53.58, "curve": [0.355, 58.13, 0.467, 37.99, 0.344, -113.89, 0.483, -373.65]}, {"time": 0.5667, "x": 37.99, "y": -614.89}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop_s3": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": [0.431, 0, 0.243, 17.6, 0.431, 0, 0.256, -6.54]}, {"time": 0.2667, "x": 10.55, "y": -19.73, "curve": [0.323, -6.01, 0.37, -21.01, 0.314, -79.55, 0.361, -201.42]}, {"time": 0.4333, "x": -31.65, "y": -390.13}]}, "blot_drop6": {"rotate": [{"curve": [0.385, 0, 0.225, -30.33]}, {"time": 0.2667, "value": -75.4, "curve": [0.309, -120.98, 0.393, -263.98]}, {"time": 0.5, "value": -261.68}], "translate": [{"curve": [0.323, 0, 0.263, 4.64, 0.323, 0, 0.256, 45.57]}, {"time": 0.2667, "x": 9.31, "y": 91.31, "curve": [0.358, 118.46, 0.511, 297.6, 0.322, 320.53, 0.481, 364.92]}, {"time": 0.6333, "x": 297.6, "y": -1347.33}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": [0.544, 1, 0.589, 0.4, 0.544, 1, 0.589, 0.4]}, {"time": 0.6333, "x": 0.4, "y": 0.4}]}, "blot_drop_s4": {"rotate": [{"curve": "stepped"}, {"time": 0.3}], "translate": [{"curve": [0.35, 0, 0.278, -25, 0.35, 0, 0.263, 128.37]}, {"time": 0.3, "x": -53.64, "y": 83.69, "curve": [0.388, -170.51, 0.54, -244.31, 0.46, -107.5, 0.576, -316.02]}, {"time": 0.6667, "x": -277.78, "y": -598.88}]}, "Eye_R_Eyelid_t": {"translate": [{"x": -2.12, "y": 27.44}]}, "Eye_L_Eyelid_t": {"translate": [{"x": -2.12, "y": 27.44}]}, "Eye_L": {"translate": [{"curve": [0.033, 0, 0.089, 11.04, 0.033, 0, 0.089, 23.82]}, {"time": 0.1, "x": 11.04, "y": 23.82, "curve": "stepped"}, {"time": 0.1333, "x": 11.04, "y": 23.82, "curve": [0.2, 11.04, 0.322, 3.19, 0.2, 23.82, 0.322, -11.88]}, {"time": 0.3333, "x": 3.19, "y": -11.88}], "scale": [{"curve": [0.033, 1, 0.067, 1.33, 0.033, 1, 0.067, 1.33]}, {"time": 0.1, "x": 1.33, "y": 1.33, "curve": [0.144, 1.33, 0.189, 1, 0.144, 1.33, 0.189, 1]}, {"time": 0.2333}]}, "Eye_R": {"translate": [{"curve": [0.033, 0, 0.067, 12.89, 0.033, 0, 0.067, -24.03]}, {"time": 0.1, "x": 12.89, "y": -24.03, "curve": "stepped"}, {"time": 0.1333, "x": 12.89, "y": -24.03, "curve": [0.144, 12.89, 0.322, 7.81, 0.144, -24.03, 0.322, 12.4]}, {"time": 0.3333, "x": 7.81, "y": 12.4}], "scale": [{"curve": [0.033, 1, 0.067, 1.33, 0.033, 1, 0.067, 1.33]}, {"time": 0.1, "x": 1.33, "y": 1.33, "curve": [0.144, 1.33, 0.189, 1, 0.144, 1.33, 0.189, 1]}, {"time": 0.2333}]}, "Main_Cntr": {"rotate": [{}], "translate": [{"y": -9.16, "curve": "stepped"}, {"time": 0.0333, "y": -9.16, "curve": [0.062, 0, 0.071, 0, 0.052, -0.68, 0.073, 30.72]}, {"time": 0.1, "y": 30.72, "curve": [0.144, 0, 0.189, 0, 0.144, 30.72, 0.189, -15.44]}, {"time": 0.2333, "y": -15.44}], "scale": [{}]}, "Low_Cntr": {"scale": [{"x": 1.05, "y": 0.95, "curve": [0.016, 1.05, 0.018, 0.9, 0.016, 0.95, 0.018, 1.2]}, {"time": 0.0333, "x": 0.9, "y": 1.2, "curve": [0.078, 0.9, 0.122, 1, 0.078, 1.2, 0.122, 1]}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.2333, "curve": [0.244, 1, 0.256, 1.1, 0.244, 1, 0.256, 0.9]}, {"time": 0.2667, "x": 1.1, "y": 0.9, "curve": [0.311, 1.1, 0.356, 1.05, 0.311, 0.9, 0.356, 0.95]}, {"time": 0.4, "x": 1.05, "y": 0.95}], "shear": [{}]}, "Eye_R_Pupil": {"translate": [{"x": -3.97, "y": -5.53, "curve": [0.033, -3.97, 0.067, -11.15, 0.033, -5.53, 0.067, 4.29]}, {"time": 0.1, "x": -11.15, "y": 4.29}], "scale": [{"x": 0.694, "y": 0.694}, {"time": 0.1, "x": 0.968, "y": 0.968}]}, "Eye_L_Pupil": {"translate": [{"x": -3.97, "y": -5.53, "curve": [0.033, -3.97, 0.067, -0.29, 0.033, -5.53, 0.067, 7.16]}, {"time": 0.1, "x": -0.29, "y": 7.16}], "scale": [{"x": 0.694, "y": 0.694}, {"time": 0.1, "x": 0.968, "y": 0.968}]}, "Mouth": {"translate": [{"curve": [0.033, 0, 0.067, 0, 0.033, 0, 0.067, 28.32]}, {"time": 0.1, "y": 28.32, "curve": "stepped"}, {"time": 0.1333, "y": 28.32, "curve": [0.144, 0, 0.289, 0, 0.144, 28.32, 0.289, -17.28]}, {"time": 0.3667, "y": -17.28}]}, "Mouth_Bottom": {"translate": [{"curve": [0.022, 0, 0.044, 0, 0.022, 0, 0.044, 6.43]}, {"time": 0.0667, "y": 6.43, "curve": [0.111, 0, 0.156, 0, 0.111, 6.43, 0.156, 0]}, {"time": 0.2}]}, "Eye_L_Eyelid_b": {"translate": [{"x": 0.54, "y": -8.19, "curve": [0.033, 0.85, 0.067, 1.48, 0.033, -12.24, 0.067, -20.35]}, {"time": 0.1, "x": 1.48, "y": -20.35, "curve": "stepped"}, {"time": 0.2, "x": 1.48, "y": -20.35, "curve": [0.222, 1.48, 0.244, 0.85, 0.222, -20.35, 0.244, -12.24]}, {"time": 0.2667, "x": 0.54, "y": -8.19}]}, "Mouth_cntrl": {"scale": [{"x": 1.118, "y": 0.632, "curve": [0.022, 1.118, 0.044, 0.876, 0.022, 0.632, 0.044, 0.913]}, {"time": 0.0667, "x": 0.876, "y": 0.913, "curve": [0.111, 0.876, 0.156, 1.118, 0.111, 0.913, 0.156, 0.632]}, {"time": 0.2, "x": 1.118, "y": 0.632}]}, "Saliva": {"translate": [{"y": -27.68}]}, "Cntr": {"translate": [{"curve": "stepped"}, {"time": 0.2, "curve": [0.267, 0, 0.333, 0, 0.267, 0, 0.333, -55.07]}, {"time": 0.4, "y": -55.07}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": [0.244, 1, 0.322, 1, 0.244, 1, 0.322, -0.567]}, {"time": 0.4, "y": -0.567}]}, "Brow_L": {"rotate": [{"value": -18.62, "curve": [0.022, -18.62, 0.044, -29.95]}, {"time": 0.0667, "value": -29.95, "curve": "stepped"}, {"time": 0.1, "value": -29.95, "curve": [0.167, -29.95, 0.233, -29.95]}, {"time": 0.3, "value": -29.95}], "translate": [{"x": 6.91, "y": -0.85, "curve": [0.022, 6.91, 0.044, 31.75, 0.022, -0.85, 0.044, 23.15]}, {"time": 0.0667, "x": 31.75, "y": 23.15, "curve": "stepped"}, {"time": 0.1, "x": 31.75, "y": 23.15, "curve": [0.167, 31.75, 0.233, 12.97, 0.167, 23.15, 0.233, -14.5]}, {"time": 0.3, "x": 12.97, "y": -14.5}]}, "Brow_R": {"rotate": [{"value": 24.11, "curve": [0.022, 24.11, 0.044, 29.68]}, {"time": 0.0667, "value": 29.68, "curve": "stepped"}, {"time": 0.1, "value": 29.68, "curve": [0.167, 29.68, 0.233, 29.7]}, {"time": 0.3, "value": 29.7}], "translate": [{"x": 8.64, "y": 1.27, "curve": [0.022, 8.64, 0.044, 39.82, 0.022, 1.27, 0.044, -20.27]}, {"time": 0.0667, "x": 39.82, "y": -20.27, "curve": "stepped"}, {"time": 0.1, "x": 39.82, "y": -20.27, "curve": [0.167, 39.82, 0.233, 16.2, 0.167, -20.27, 0.233, 11.35]}, {"time": 0.3, "x": 16.2, "y": 11.35}]}, "Leg_L_IK": {"translate": [{}]}, "Leg_R_IK": {"translate": [{}]}, "Eye_R_Eyelid_b": {"translate": [{"x": 0.54, "y": -8.19, "curve": [0.033, 0.86, 0.067, 1.5, 0.033, -12.34, 0.067, -20.65]}, {"time": 0.1, "x": 1.5, "y": -20.65, "curve": "stepped"}, {"time": 0.2, "x": 1.5, "y": -20.65, "curve": [0.222, 1.5, 0.244, 0.86, 0.222, -20.65, 0.244, -12.34]}, {"time": 0.2667, "x": 0.54, "y": -8.19}]}, "Right": {"rotate": [{"curve": [0.033, 0, 0.067, 15.43]}, {"time": 0.1, "value": 15.43, "curve": [0.133, 15.43, 0.167, -4.35]}, {"time": 0.2, "value": -4.35}]}, "Left": {"rotate": [{"curve": [0.033, 0, 0.067, -17.66]}, {"time": 0.1, "value": -17.66, "curve": [0.133, -17.66, 0.167, 5.09]}, {"time": 0.2, "value": 5.09}]}}}, "t1_Hit": {"slots": {"blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "Body_outline": {"attachment": [{"name": "Body_outline"}]}, "Brow_l": {"attachment": [{"name": "Brow_l"}]}, "Brow_r": {"attachment": [{"name": "Brow_r"}]}, "Eyelid_l_r": {"attachment": [{"name": "Eyelid_l_r"}]}, "Eyelid_l_r2": {"attachment": [{"name": "Eyelid_l_r"}]}, "Eyelid_u_r": {"attachment": [{"name": "Eyelid_u_r"}]}, "Eyelid_u_r2": {"attachment": [{"name": "Eyelid_u_r"}]}, "Eye_L": {"attachment": [{"name": "Eye_r"}]}, "Eye_L2": {"attachment": [{"name": "Eye_r"}]}, "Eye_Pupil_r": {"attachment": [{"name": "Eye_Pupil_r"}]}, "Eye_Pupil_r2": {"attachment": [{"name": "Eye_Pupil_r"}]}, "Eye_red_L": {"rgba": [{"color": "ffffff00"}, {"time": 0.1, "color": "ffffffff"}], "attachment": [{"name": "Eye_red_r"}]}, "Eye_red_L2": {"rgba": [{"color": "ffffff00"}, {"time": 0.1, "color": "ffffffff"}], "attachment": [{"name": "Eye_red_r"}]}, "Leg_L_outline": {"attachment": [{"name": "Leg_l_outline"}]}, "Leg_R_outline": {"attachment": [{"name": "Leg_l_outline"}]}, "Mouth_base": {"attachment": [{"name": "Mouth_base"}]}, "Mouth_open": {"attachment": [{"name": "Mouth_open"}]}, "Saliva": {"attachment": [{"name": "Saliva"}]}, "Slippy_body": {"attachment": [{"name": "Slippy_body"}]}, "Slippy_leg_L": {"attachment": [{"name": "Slippy_leg_l"}]}, "Slippy_leg_R": {"attachment": [{"name": "Slippy_leg_l"}]}, "Teeth": {"attachment": [{"name": "<PERSON><PERSON>"}]}}, "bones": {"Eye_R_Eyelid_t": {"translate": [{"x": -2.12, "y": 27.44}]}, "Eye_L_Eyelid_t": {"translate": [{"x": -2.12, "y": 27.44}]}, "Eye_L": {"translate": [{"curve": [0.033, 0, 0.067, 0, 0.033, 0, 0.067, 18.28]}, {"time": 0.1, "y": 18.28, "curve": "stepped"}, {"time": 0.1333, "y": 18.28, "curve": [0.2, 0, 0.267, 0, 0.2, 18.28, 0.267, 0]}, {"time": 0.3333}], "scale": [{"curve": [0.033, 1, 0.067, 1.33, 0.033, 1, 0.067, 1.33]}, {"time": 0.1, "x": 1.33, "y": 1.33, "curve": [0.144, 1.33, 0.189, 1, 0.144, 1.33, 0.189, 1]}, {"time": 0.2333}]}, "Eye_R": {"translate": [{"curve": [0.033, 0, 0.067, 0, 0.033, 0, 0.067, -18.28]}, {"time": 0.1, "y": -18.28, "curve": "stepped"}, {"time": 0.1333, "y": -18.28, "curve": [0.2, 0, 0.267, 0, 0.2, -18.28, 0.267, 0]}, {"time": 0.3333}], "scale": [{"curve": [0.033, 1, 0.067, 1.33, 0.033, 1, 0.067, 1.33]}, {"time": 0.1, "x": 1.33, "y": 1.33, "curve": [0.144, 1.33, 0.189, 1, 0.144, 1.33, 0.189, 1]}, {"time": 0.2333}]}, "Main_Cntr": {"rotate": [{}], "translate": [{"y": -9.16, "curve": "stepped"}, {"time": 0.0333, "y": -9.16, "curve": [0.062, 0, 0.071, 0, 0.052, -0.68, 0.071, 18.88]}, {"time": 0.1, "y": 18.88, "curve": [0.144, 0, 0.189, 0, 0.144, 18.88, 0.192, 7.9]}, {"time": 0.2333, "y": -9.16}], "scale": [{}]}, "Low_Cntr": {"scale": [{"x": 1.05, "y": 0.95, "curve": [0.016, 1.05, 0.018, 0.9, 0.016, 0.95, 0.018, 1.2]}, {"time": 0.0333, "x": 0.9, "y": 1.2, "curve": [0.078, 0.9, 0.122, 1, 0.078, 1.2, 0.122, 1]}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.2333, "curve": [0.244, 1, 0.256, 1.1, 0.244, 1, 0.256, 0.9]}, {"time": 0.2667, "x": 1.1, "y": 0.9, "curve": [0.311, 1.1, 0.356, 1.05, 0.311, 0.9, 0.356, 0.95]}, {"time": 0.4, "x": 1.05, "y": 0.95}], "shear": [{}]}, "Eye_R_Pupil": {"translate": [{"x": -3.97, "y": -5.53, "curve": [0.033, -3.97, 0.067, -18.23, 0.033, -5.53, 0.067, 8.39]}, {"time": 0.1, "x": -18.23, "y": 8.39, "curve": "stepped"}, {"time": 0.1667, "x": -18.23, "y": 8.39, "curve": [0.222, -18.23, 0.278, -3.97, 0.222, 8.39, 0.278, -5.53]}, {"time": 0.3333, "x": -3.97, "y": -5.53}], "scale": [{"x": 0.694, "y": 0.694}]}, "Eye_L_Pupil": {"translate": [{"x": -3.97, "y": -5.53, "curve": [0.033, -3.97, 0.067, 5.88, 0.033, -5.53, 0.067, 9.62]}, {"time": 0.1, "x": 5.88, "y": 9.62, "curve": "stepped"}, {"time": 0.1667, "x": 5.88, "y": 9.62, "curve": [0.222, 5.88, 0.278, -3.97, 0.222, 9.62, 0.278, -5.53]}, {"time": 0.3333, "x": -3.97, "y": -5.53}], "scale": [{"x": 0.694, "y": 0.694}]}, "Mouth": {"translate": [{"curve": [0.033, 0, 0.067, 0, 0.033, 0, 0.067, 17.96]}, {"time": 0.1, "y": 17.96, "curve": "stepped"}, {"time": 0.1333, "y": 17.96, "curve": [0.211, 0, 0.289, 0, 0.211, 17.96, 0.289, 0]}, {"time": 0.3667}]}, "Eye_L_Eyelid_b": {"translate": [{"x": 0.54, "y": -8.19, "curve": [0.033, 0.85, 0.067, 1.48, 0.033, -12.24, 0.067, -20.35]}, {"time": 0.1, "x": 1.48, "y": -20.35, "curve": "stepped"}, {"time": 0.2, "x": 1.48, "y": -20.35, "curve": [0.222, 1.48, 0.244, 0.85, 0.222, -20.35, 0.244, -12.24]}, {"time": 0.2667, "x": 0.54, "y": -8.19}]}, "Eye_R_Eyelid_b": {"translate": [{"x": 0.54, "y": -8.19, "curve": [0.033, 0.86, 0.067, 1.5, 0.033, -12.34, 0.067, -20.65]}, {"time": 0.1, "x": 1.5, "y": -20.65, "curve": "stepped"}, {"time": 0.2, "x": 1.5, "y": -20.65, "curve": [0.222, 1.5, 0.244, 0.86, 0.222, -20.65, 0.244, -12.34]}, {"time": 0.2667, "x": 0.54, "y": -8.19}]}, "Mouth_cntrl": {"scale": [{"x": 1.118, "y": 0.632, "curve": [0.022, 1.118, 0.044, 0.876, 0.022, 0.632, 0.044, 0.913]}, {"time": 0.0667, "x": 0.876, "y": 0.913, "curve": [0.111, 0.876, 0.156, 1.118, 0.111, 0.913, 0.156, 0.632]}, {"time": 0.2, "x": 1.118, "y": 0.632}]}, "Saliva": {"translate": [{"y": -27.68}]}, "Cntr": {"translate": [{}], "scale": [{}]}, "Brow_L": {"rotate": [{"value": -18.62, "curve": [0.022, -18.62, 0.044, -24.12]}, {"time": 0.0667, "value": -24.12, "curve": "stepped"}, {"time": 0.1, "value": -24.12, "curve": [0.167, -24.12, 0.233, -18.62]}, {"time": 0.3, "value": -18.62}], "translate": [{"x": 6.91, "y": -0.85, "curve": [0.022, 6.91, 0.044, 15.28, 0.022, -0.85, 0.044, 14.36]}, {"time": 0.0667, "x": 15.28, "y": 14.36, "curve": "stepped"}, {"time": 0.1, "x": 15.28, "y": 14.36, "curve": [0.167, 15.28, 0.233, 6.91, 0.167, 14.36, 0.233, -0.85]}, {"time": 0.3, "x": 6.91, "y": -0.85}]}, "Brow_R": {"rotate": [{"value": 24.11, "curve": [0.022, 24.11, 0.044, 29.68]}, {"time": 0.0667, "value": 29.68, "curve": "stepped"}, {"time": 0.1, "value": 29.68, "curve": [0.167, 29.68, 0.233, 24.11]}, {"time": 0.3, "value": 24.11}], "translate": [{"x": 8.64, "y": 1.27, "curve": [0.022, 8.64, 0.044, 22.31, 0.022, 1.27, 0.044, -11.93]}, {"time": 0.0667, "x": 22.31, "y": -11.93, "curve": "stepped"}, {"time": 0.1, "x": 22.31, "y": -11.93, "curve": [0.167, 22.31, 0.233, 8.64, 0.167, -11.93, 0.233, 1.27]}, {"time": 0.3, "x": 8.64, "y": 1.27}]}, "Leg_L_IK": {"translate": [{}]}, "Leg_R_IK": {"translate": [{}]}, "Mouth_Bottom": {"translate": [{"curve": [0.022, 0, 0.044, 0, 0.022, 0, 0.044, 6.43]}, {"time": 0.0667, "y": 6.43, "curve": [0.111, 0, 0.156, 0, 0.111, 6.43, 0.156, 0]}, {"time": 0.2}]}, "blot": {"translate": [{"x": -12.21, "y": 15.4}], "scale": [{"x": 0.7, "y": 0.7}]}, "blot_drops_control": {"translate": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}, "Right": {"rotate": [{}]}, "Left": {"rotate": [{}]}}}, "t1_Hit2": {"slots": {"blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "Body_outline": {"attachment": [{"name": "Body_outline"}]}, "Brow_l": {"attachment": [{"name": "Brow_l"}]}, "Brow_r": {"attachment": [{"name": "Brow_r"}]}, "Eyelid_l_r": {"attachment": [{"name": "Eyelid_l_r"}]}, "Eyelid_l_r2": {"attachment": [{"name": "Eyelid_l_r"}]}, "Eyelid_u_r": {"attachment": [{"name": "Eyelid_u_r"}]}, "Eyelid_u_r2": {"attachment": [{"name": "Eyelid_u_r"}]}, "Eye_L": {"attachment": [{"name": "Eye_r"}]}, "Eye_L2": {"attachment": [{"name": "Eye_r"}]}, "Eye_Pupil_r": {"attachment": [{"name": "Eye_Pupil_r"}]}, "Eye_Pupil_r2": {"attachment": [{"name": "Eye_Pupil_r"}]}, "Eye_red_L": {"rgba": [{"color": "ffffff00"}, {"time": 0.1, "color": "ffffffff"}], "attachment": [{"name": "Eye_red_r"}]}, "Eye_red_L2": {"rgba": [{"color": "ffffff00"}, {"time": 0.1, "color": "ffffffff"}], "attachment": [{"name": "Eye_red_r"}]}, "Leg_L_outline": {"attachment": [{"name": "Leg_l_outline"}]}, "Leg_R_outline": {"attachment": [{"name": "Leg_l_outline"}]}, "Mouth_base": {"attachment": [{"name": "Mouth_base"}]}, "Mouth_open": {"attachment": [{"name": "Mouth_open"}]}, "Saliva": {"attachment": [{"name": "Saliva"}]}, "Slippy_body": {"attachment": [{"name": "Slippy_body"}]}, "Slippy_leg_L": {"attachment": [{"name": "Slippy_leg_l"}]}, "Slippy_leg_R": {"attachment": [{"name": "Slippy_leg_l"}]}, "Teeth": {"attachment": [{"name": "<PERSON><PERSON>"}]}}, "bones": {"Eye_R_Eyelid_t": {"translate": [{"x": -2.12, "y": 27.44}]}, "Eye_L_Eyelid_t": {"translate": [{"x": -2.12, "y": 27.44}]}, "Eye_L": {"translate": [{"curve": [0.033, 0, 0.089, 11.04, 0.033, 0, 0.089, 23.82]}, {"time": 0.1, "x": 11.04, "y": 23.82, "curve": "stepped"}, {"time": 0.1333, "x": 11.04, "y": 23.82, "curve": [0.2, 11.04, 0.267, 0, 0.2, 23.82, 0.267, 0]}, {"time": 0.3333}], "scale": [{"curve": [0.033, 1, 0.067, 1.33, 0.033, 1, 0.067, 1.33]}, {"time": 0.1, "x": 1.33, "y": 1.33, "curve": [0.144, 1.33, 0.189, 1, 0.144, 1.33, 0.189, 1]}, {"time": 0.2333}]}, "Eye_R": {"translate": [{"curve": [0.033, 0, 0.067, 12.89, 0.033, 0, 0.067, -24.03]}, {"time": 0.1, "x": 12.89, "y": -24.03, "curve": "stepped"}, {"time": 0.1333, "x": 12.89, "y": -24.03, "curve": [0.144, 12.89, 0.267, 0, 0.144, -24.03, 0.267, 0]}, {"time": 0.3333}], "scale": [{"curve": [0.033, 1, 0.067, 1.33, 0.033, 1, 0.067, 1.33]}, {"time": 0.1, "x": 1.33, "y": 1.33, "curve": [0.144, 1.33, 0.189, 1, 0.144, 1.33, 0.189, 1]}, {"time": 0.2333}]}, "Main_Cntr": {"rotate": [{}], "translate": [{"y": -9.16, "curve": "stepped"}, {"time": 0.0333, "y": -9.16, "curve": [0.062, 0, 0.071, 0, 0.052, -0.68, 0.073, 30.72]}, {"time": 0.1, "y": 30.72, "curve": [0.144, 0, 0.189, 0, 0.144, 30.72, 0.189, -15.44]}, {"time": 0.2333, "y": -15.44, "curve": [0.289, 0, 0.344, 0, 0.289, -15.44, 0.344, -9.16]}, {"time": 0.4, "y": -9.16}], "scale": [{}]}, "Low_Cntr": {"scale": [{"x": 1.05, "y": 0.95, "curve": [0.016, 1.05, 0.018, 0.9, 0.016, 0.95, 0.018, 1.2]}, {"time": 0.0333, "x": 0.9, "y": 1.2, "curve": [0.078, 0.9, 0.122, 1, 0.078, 1.2, 0.122, 1]}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.2333, "curve": [0.244, 1, 0.256, 1.1, 0.244, 1, 0.256, 0.9]}, {"time": 0.2667, "x": 1.1, "y": 0.9, "curve": [0.311, 1.1, 0.356, 1.05, 0.311, 0.9, 0.356, 0.95]}, {"time": 0.4, "x": 1.05, "y": 0.95}], "shear": [{}]}, "Eye_R_Pupil": {"translate": [{"x": -3.97, "y": -5.53, "curve": [0.033, -3.97, 0.067, -18.23, 0.033, -5.53, 0.067, 8.39]}, {"time": 0.1, "x": -18.23, "y": 8.39, "curve": "stepped"}, {"time": 0.1667, "x": -18.23, "y": 8.39, "curve": [0.222, -18.23, 0.278, -3.97, 0.222, 8.39, 0.278, -5.53]}, {"time": 0.3333, "x": -3.97, "y": -5.53}], "scale": [{"x": 0.694, "y": 0.694}]}, "Eye_L_Pupil": {"translate": [{"x": -3.97, "y": -5.53, "curve": [0.033, -3.97, 0.067, 5.88, 0.033, -5.53, 0.067, 9.62]}, {"time": 0.1, "x": 5.88, "y": 9.62, "curve": "stepped"}, {"time": 0.1667, "x": 5.88, "y": 9.62, "curve": [0.222, 5.88, 0.278, -3.97, 0.222, 9.62, 0.278, -5.53]}, {"time": 0.3333, "x": -3.97, "y": -5.53}], "scale": [{"x": 0.694, "y": 0.694}]}, "Mouth": {"translate": [{"curve": [0.033, 0, 0.067, 0, 0.033, 0, 0.067, 28.32]}, {"time": 0.1, "y": 28.32, "curve": "stepped"}, {"time": 0.1333, "y": 28.32, "curve": [0.144, 0, 0.289, 0, 0.144, 28.32, 0.289, 0]}, {"time": 0.3667}]}, "Eye_L_Eyelid_b": {"translate": [{"x": 0.54, "y": -8.19, "curve": [0.033, 0.85, 0.067, 1.48, 0.033, -12.24, 0.067, -20.35]}, {"time": 0.1, "x": 1.48, "y": -20.35, "curve": "stepped"}, {"time": 0.2, "x": 1.48, "y": -20.35, "curve": [0.222, 1.48, 0.244, 0.85, 0.222, -20.35, 0.244, -12.24]}, {"time": 0.2667, "x": 0.54, "y": -8.19}]}, "Eye_R_Eyelid_b": {"translate": [{"x": 0.54, "y": -8.19, "curve": [0.033, 0.86, 0.067, 1.5, 0.033, -12.34, 0.067, -20.65]}, {"time": 0.1, "x": 1.5, "y": -20.65, "curve": "stepped"}, {"time": 0.2, "x": 1.5, "y": -20.65, "curve": [0.222, 1.5, 0.244, 0.86, 0.222, -20.65, 0.244, -12.34]}, {"time": 0.2667, "x": 0.54, "y": -8.19}]}, "Mouth_cntrl": {"scale": [{"x": 1.118, "y": 0.632, "curve": [0.022, 1.118, 0.044, 0.876, 0.022, 0.632, 0.044, 0.913]}, {"time": 0.0667, "x": 0.876, "y": 0.913, "curve": [0.111, 0.876, 0.156, 1.118, 0.111, 0.913, 0.156, 0.632]}, {"time": 0.2, "x": 1.118, "y": 0.632}]}, "Saliva": {"translate": [{"y": -27.68}]}, "Cntr": {"translate": [{}], "scale": [{}]}, "Brow_L": {"rotate": [{"value": -18.62, "curve": [0.022, -18.62, 0.044, -29.95]}, {"time": 0.0667, "value": -29.95, "curve": "stepped"}, {"time": 0.1, "value": -29.95, "curve": [0.167, -29.95, 0.233, -18.62]}, {"time": 0.3, "value": -18.62}], "translate": [{"x": 6.91, "y": -0.85, "curve": [0.022, 6.91, 0.044, 31.75, 0.022, -0.85, 0.044, 23.15]}, {"time": 0.0667, "x": 31.75, "y": 23.15, "curve": "stepped"}, {"time": 0.1, "x": 31.75, "y": 23.15, "curve": [0.167, 31.75, 0.233, 6.91, 0.167, 23.15, 0.233, -0.85]}, {"time": 0.3, "x": 6.91, "y": -0.85}]}, "Brow_R": {"rotate": [{"value": 24.11, "curve": [0.022, 24.11, 0.044, 29.68]}, {"time": 0.0667, "value": 29.68, "curve": "stepped"}, {"time": 0.1, "value": 29.68, "curve": [0.167, 29.68, 0.233, 24.11]}, {"time": 0.3, "value": 24.11}], "translate": [{"x": 8.64, "y": 1.27, "curve": [0.022, 8.64, 0.044, 39.82, 0.022, 1.27, 0.044, -20.27]}, {"time": 0.0667, "x": 39.82, "y": -20.27, "curve": "stepped"}, {"time": 0.1, "x": 39.82, "y": -20.27, "curve": [0.167, 39.82, 0.233, 8.64, 0.167, -20.27, 0.233, 1.27]}, {"time": 0.3, "x": 8.64, "y": 1.27}]}, "Leg_L_IK": {"translate": [{}]}, "Leg_R_IK": {"translate": [{}]}, "Mouth_Bottom": {"translate": [{"curve": [0.022, 0, 0.044, 0, 0.022, 0, 0.044, 6.43]}, {"time": 0.0667, "y": 6.43, "curve": [0.111, 0, 0.156, 0, 0.111, 6.43, 0.156, 0]}, {"time": 0.2}]}, "blot": {"translate": [{"x": -12.21, "y": 15.4}], "scale": [{"x": 0.7, "y": 0.7}]}, "blot_drops_control": {"translate": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}, "Left": {"rotate": [{"curve": [0.033, 0, 0.067, -17.66]}, {"time": 0.1, "value": -17.66, "curve": [0.144, -17.66, 0.189, 5.09]}, {"time": 0.2333, "value": 5.09, "curve": [0.289, 5.09, 0.344, 0]}, {"time": 0.4}]}, "Right": {"rotate": [{"curve": [0.033, 0, 0.067, 15.43]}, {"time": 0.1, "value": 15.43, "curve": [0.144, 15.43, 0.189, -4.35]}, {"time": 0.2333, "value": -4.35, "curve": [0.289, -4.35, 0.344, 0]}, {"time": 0.4}]}}}, "t1_IDLE": {"slots": {"blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "Body_outline": {"attachment": [{"name": "Body_outline"}]}, "Brow_l": {"attachment": [{"name": "Brow_l"}]}, "Brow_r": {"attachment": [{"name": "Brow_r"}]}, "Eyelid_l_r": {"attachment": [{"name": "Eyelid_l_r"}]}, "Eyelid_l_r2": {"attachment": [{"name": "Eyelid_l_r"}]}, "Eyelid_u_r": {"attachment": [{}, {"time": 0.5333, "name": "Eyelid_u_r"}, {"time": 0.7667}, {"time": 2.4333, "name": "Eyelid_u_r"}, {"time": 2.6667}, {"time": 3.7667, "name": "Eyelid_u_r"}, {"time": 4}]}, "Eyelid_u_r2": {"attachment": [{}, {"time": 0.6667, "name": "Eyelid_u_r"}, {"time": 0.9}, {"time": 2.2333, "name": "Eyelid_u_r"}, {"time": 2.4667}, {"time": 3.8333, "name": "Eyelid_u_r"}, {"time": 4.0667}]}, "Eye_L": {"attachment": [{"name": "Eye_r"}]}, "Eye_L2": {"attachment": [{"name": "Eye_r"}]}, "Eye_Pupil_r": {"attachment": [{"name": "Eye_Pupil_r"}]}, "Eye_Pupil_r2": {"attachment": [{"name": "Eye_Pupil_r"}]}, "Eye_red_L": {"rgba": [{"color": "ffffff00"}], "attachment": [{"name": "Eye_red_r"}]}, "Eye_red_L2": {"rgba": [{"color": "ffffff00"}], "attachment": [{"name": "Eye_red_r"}]}, "Leg_L_outline": {"attachment": [{"name": "Leg_l_outline"}]}, "Leg_R_outline": {"attachment": [{"name": "Leg_l_outline"}]}, "Mouth_base": {"attachment": [{"name": "Mouth_base"}]}, "Mouth_open": {"attachment": [{"name": "Mouth_open"}]}, "Saliva": {"attachment": [{"name": "Saliva"}]}, "Slippy_body": {"attachment": [{"name": "Slippy_body"}]}, "Slippy_leg_L": {"attachment": [{"name": "Slippy_leg_l"}]}, "Slippy_leg_R": {"attachment": [{"name": "Slippy_leg_l"}]}, "Teeth": {"attachment": [{"name": "<PERSON><PERSON>"}]}}, "bones": {"Eye_R_Eyelid_t": {"translate": [{"x": -2.44, "y": 31.52, "curve": "stepped"}, {"time": 0.6333, "x": -2.44, "y": 31.52, "curve": [0.661, -2.44, 0.672, 0, 0.661, 31.52, 0.672, 0]}, {"time": 0.7, "curve": "stepped"}, {"time": 0.7667, "curve": [0.822, 0, 0.878, -2.44, 0.822, 0, 0.878, 31.52]}, {"time": 0.9333, "x": -2.44, "y": 31.52, "curve": "stepped"}, {"time": 2.2, "x": -2.44, "y": 31.52, "curve": [2.228, -2.44, 2.239, 0, 2.228, 31.52, 2.239, 0]}, {"time": 2.2667, "curve": "stepped"}, {"time": 2.3333, "curve": [2.389, 0, 2.444, -2.44, 2.389, 0, 2.444, 31.52]}, {"time": 2.5, "x": -2.44, "y": 31.52, "curve": "stepped"}, {"time": 3.8, "x": -2.44, "y": 31.52, "curve": [3.828, -2.44, 3.839, 0, 3.828, 31.52, 3.839, 0]}, {"time": 3.8667, "curve": "stepped"}, {"time": 3.9333, "curve": [3.989, 0, 4.044, -2.44, 3.989, 0, 4.044, 31.52]}, {"time": 4.1, "x": -2.44, "y": 31.52}]}, "Eye_L_Eyelid_t": {"translate": [{"x": -2.41, "y": 31.13, "curve": "stepped"}, {"time": 0.5, "x": -2.41, "y": 31.13, "curve": [0.528, -2.41, 0.539, 0, 0.528, 31.13, 0.539, 0]}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.6333, "curve": [0.689, 0, 0.744, -2.41, 0.689, 0, 0.744, 31.13]}, {"time": 0.8, "x": -2.41, "y": 31.13, "curve": "stepped"}, {"time": 2.4, "x": -2.41, "y": 31.13, "curve": [2.428, -2.41, 2.439, 0, 2.428, 31.13, 2.439, 0]}, {"time": 2.4667, "curve": "stepped"}, {"time": 2.5333, "curve": [2.589, 0, 2.644, -2.41, 2.589, 0, 2.644, 31.13]}, {"time": 2.7, "x": -2.41, "y": 31.13, "curve": "stepped"}, {"time": 3.7333, "x": -2.41, "y": 31.13, "curve": [3.761, -2.41, 3.772, 0, 3.761, 31.13, 3.772, 0]}, {"time": 3.8, "curve": "stepped"}, {"time": 3.8667, "curve": [3.922, 0, 3.978, -2.41, 3.922, 0, 3.978, 31.13]}, {"time": 4.0333, "x": -2.41, "y": 31.13}]}, "Eye_L": {"translate": [{}], "scale": [{"curve": "stepped"}, {"time": 0.5, "curve": [0.537, 1, 0.53, 1.1, 0.537, 1, 0.53, 0.9]}, {"time": 0.5667, "x": 1.1, "y": 0.9, "curve": "stepped"}, {"time": 0.6333, "x": 1.1, "y": 0.9, "curve": [0.689, 1.1, 0.744, 1, 0.689, 0.9, 0.744, 1]}, {"time": 0.8, "curve": "stepped"}, {"time": 2.4, "curve": [2.437, 1, 2.43, 1.1, 2.437, 1, 2.43, 0.9]}, {"time": 2.4667, "x": 1.1, "y": 0.9, "curve": "stepped"}, {"time": 2.5333, "x": 1.1, "y": 0.9, "curve": [2.589, 1.1, 2.644, 1, 2.589, 0.9, 2.644, 1]}, {"time": 2.7, "curve": "stepped"}, {"time": 3.7333, "curve": [3.77, 1, 3.763, 1.1, 3.77, 1, 3.763, 0.9]}, {"time": 3.8, "x": 1.1, "y": 0.9, "curve": "stepped"}, {"time": 3.8667, "x": 1.1, "y": 0.9, "curve": [3.922, 1.1, 3.978, 1, 3.922, 0.9, 3.978, 1]}, {"time": 4.0333}]}, "Eye_R": {"translate": [{}], "scale": [{"curve": "stepped"}, {"time": 0.6333, "curve": [0.67, 1, 0.663, 1.1, 0.67, 1, 0.663, 0.9]}, {"time": 0.7, "x": 1.1, "y": 0.9, "curve": "stepped"}, {"time": 0.7667, "x": 1.1, "y": 0.9, "curve": [0.822, 1.1, 0.878, 1, 0.822, 0.9, 0.878, 1]}, {"time": 0.9333, "curve": "stepped"}, {"time": 2.2, "curve": [2.237, 1, 2.23, 1.1, 2.237, 1, 2.23, 0.9]}, {"time": 2.2667, "x": 1.1, "y": 0.9, "curve": "stepped"}, {"time": 2.3333, "x": 1.1, "y": 0.9, "curve": [2.389, 1.1, 2.444, 1, 2.389, 0.9, 2.444, 1]}, {"time": 2.5, "curve": "stepped"}, {"time": 3.8, "curve": [3.837, 1, 3.83, 1.1, 3.837, 1, 3.83, 0.9]}, {"time": 3.8667, "x": 1.1, "y": 0.9, "curve": "stepped"}, {"time": 3.9333, "x": 1.1, "y": 0.9, "curve": [3.989, 1.1, 4.044, 1, 3.989, 0.9, 4.044, 1]}, {"time": 4.1}]}, "Main_Cntr": {"rotate": [{"value": -1.8, "curve": [0.044, -1.8, 0.089, 1.04]}, {"time": 0.1333, "value": 1.04, "curve": "stepped"}, {"time": 1.5333, "value": 1.04, "curve": [1.578, 1.04, 1.622, -1.8]}, {"time": 1.6667, "value": -1.8, "curve": "stepped"}, {"time": 4.5333, "value": -1.8}], "translate": [{"x": -8.91, "y": 0.28, "curve": [0.067, -8.91, 0.133, 7.76, 0.067, 0.28, 0.133, -0.24]}, {"time": 0.2, "x": 7.76, "y": -0.24, "curve": "stepped"}, {"time": 1.5333, "x": 7.76, "y": -0.24, "curve": [1.6, 7.76, 1.667, -8.91, 1.6, -0.24, 1.667, 0.28]}, {"time": 1.7333, "x": -8.91, "y": 0.28, "curve": "stepped"}, {"time": 4.5333, "x": -8.91, "y": 0.28}], "scale": [{}]}, "Low_Cntr": {"scale": [{"x": 1.037, "y": 0.963, "curve": [0.011, 1.044, 0.022, 1.05, 0.011, 0.956, 0.022, 0.95]}, {"time": 0.0333, "x": 1.05, "y": 0.95, "curve": [0.054, 1.05, 0.165, 1, 0.054, 0.95, 0.165, 1]}, {"time": 0.2, "curve": "stepped"}, {"time": 1.4667, "curve": [1.5, 1, 1.533, 1.05, 1.5, 1, 1.533, 0.95]}, {"time": 1.5667, "x": 1.05, "y": 0.95, "curve": [1.588, 1.05, 1.699, 1, 1.588, 0.95, 1.699, 1]}, {"time": 1.7333, "curve": "stepped"}, {"time": 4.4667, "curve": [4.489, 1, 4.511, 1.022, 4.489, 1, 4.511, 0.978]}, {"time": 4.5333, "x": 1.037, "y": 0.963}], "shear": [{"y": 3, "curve": "stepped"}, {"time": 0.0333, "y": 3, "curve": [0.078, 0, 0.122, 0, 0.078, 3, 0.122, -4]}, {"time": 0.1667, "y": -4, "curve": [0.222, 0, 0.278, 0, 0.222, -4, 0.278, -3]}, {"time": 0.3333, "y": -3, "curve": "stepped"}, {"time": 1.5667, "y": -3, "curve": [1.611, 0, 1.656, 0, 1.611, -3, 1.656, 4]}, {"time": 1.7, "y": 4, "curve": [1.756, 0, 1.811, 0, 1.756, 4, 1.811, 3]}, {"time": 1.8667, "y": 3}]}, "Eye_R_Pupil": {"translate": [{"x": 8.88, "y": 2.22, "curve": [0.022, 8.88, 0.044, -20.6, 0.022, 2.22, 0.044, 3.72]}, {"time": 0.0667, "x": -20.6, "y": 3.72, "curve": "stepped"}, {"time": 0.6333, "x": -20.6, "y": 3.72, "curve": [0.667, -20.6, 0.7, -17.81, 0.667, 3.72, 0.7, 10.93]}, {"time": 0.7333, "x": -17.81, "y": 10.93, "curve": "stepped"}, {"time": 1.1333, "x": -17.81, "y": 10.93, "curve": [1.156, -17.81, 1.178, -21.56, 1.156, 10.93, 1.178, 4.22]}, {"time": 1.2, "x": -21.56, "y": 4.22, "curve": "stepped"}, {"time": 1.5, "x": -21.56, "y": 4.22, "curve": [1.522, -21.56, 1.544, 8.53, 1.522, 4.22, 1.544, 9.7]}, {"time": 1.5667, "x": 8.53, "y": 9.7, "curve": "stepped"}, {"time": 2.1, "x": 8.53, "y": 9.7, "curve": [2.122, 8.53, 2.144, 8.69, 2.122, 9.7, 2.144, 4.62]}, {"time": 2.1667, "x": 8.69, "y": 4.62, "curve": "stepped"}, {"time": 2.4, "x": 8.69, "y": 4.62, "curve": [2.422, 8.69, 2.444, 6.75, 2.422, 4.62, 2.444, 12.3]}, {"time": 2.4667, "x": 6.75, "y": 12.3, "curve": "stepped"}, {"time": 3.2667, "x": 6.75, "y": 12.3, "curve": [3.289, 6.75, 3.311, 6.26, 3.289, 12.3, 3.311, 4.78]}, {"time": 3.3333, "x": 6.26, "y": 4.78, "curve": "stepped"}, {"time": 4, "x": 6.26, "y": 4.78, "curve": [4.022, 6.26, 4.044, 5.63, 4.022, 4.78, 4.044, 9.37]}, {"time": 4.0667, "x": 5.63, "y": 9.37, "curve": "stepped"}, {"time": 4.3, "x": 5.63, "y": 9.37, "curve": [4.322, 5.63, 4.344, 8.88, 4.322, 9.37, 4.344, 2.22]}, {"time": 4.3667, "x": 8.88, "y": 2.22}], "scale": [{}]}, "Eye_L_Pupil": {"translate": [{"x": 8.88, "y": 2.22, "curve": [0.022, 8.88, 0.044, -20.6, 0.022, 2.22, 0.044, 3.72]}, {"time": 0.0667, "x": -20.6, "y": 3.72, "curve": "stepped"}, {"time": 0.6333, "x": -20.6, "y": 3.72, "curve": [0.667, -20.6, 0.7, -17.81, 0.667, 3.72, 0.7, 10.93]}, {"time": 0.7333, "x": -17.81, "y": 10.93, "curve": "stepped"}, {"time": 1.1333, "x": -17.81, "y": 10.93, "curve": [1.156, -17.81, 1.178, -21.56, 1.156, 10.93, 1.178, 4.22]}, {"time": 1.2, "x": -21.56, "y": 4.22, "curve": "stepped"}, {"time": 1.5, "x": -21.56, "y": 4.22, "curve": [1.522, -21.56, 1.544, 8.53, 1.522, 4.22, 1.544, 9.7]}, {"time": 1.5667, "x": 8.53, "y": 9.7, "curve": "stepped"}, {"time": 2.1, "x": 8.53, "y": 9.7, "curve": [2.122, 8.53, 2.144, 8.69, 2.122, 9.7, 2.144, 4.62]}, {"time": 2.1667, "x": 8.69, "y": 4.62, "curve": "stepped"}, {"time": 2.4, "x": 8.69, "y": 4.62, "curve": [2.422, 8.69, 2.444, 6.75, 2.422, 4.62, 2.444, 12.3]}, {"time": 2.4667, "x": 6.75, "y": 12.3, "curve": "stepped"}, {"time": 3.2667, "x": 6.75, "y": 12.3, "curve": [3.289, 6.75, 3.311, 6.26, 3.289, 12.3, 3.311, 4.78]}, {"time": 3.3333, "x": 6.26, "y": 4.78, "curve": "stepped"}, {"time": 4, "x": 6.26, "y": 4.78, "curve": [4.022, 6.26, 4.044, 5.63, 4.022, 4.78, 4.044, 9.37]}, {"time": 4.0667, "x": 5.63, "y": 9.37, "curve": "stepped"}, {"time": 4.3, "x": 5.63, "y": 9.37, "curve": [4.322, 5.63, 4.344, 8.88, 4.322, 9.37, 4.344, 2.22]}, {"time": 4.3667, "x": 8.88, "y": 2.22}], "scale": [{}]}, "Mouth": {"translate": [{"y": 1.5, "curve": "stepped"}, {"time": 0.0333, "y": 1.5, "curve": [0.167, 0, 0.267, 0, 0.167, 1.5, 0.267, -1.58]}, {"time": 0.4, "y": -1.58, "curve": "stepped"}, {"time": 0.4333, "y": -1.58, "curve": [0.556, 0, 0.644, 0, 0.556, -1.58, 0.644, 1.5]}, {"time": 0.7667, "y": 1.5, "curve": "stepped"}, {"time": 0.8, "y": 1.5, "curve": [0.944, 0, 1.056, 0, 0.944, 1.5, 1.056, -1.58]}, {"time": 1.2, "y": -1.58, "curve": "stepped"}, {"time": 1.2333, "y": -1.58, "curve": [1.344, 0, 1.422, 0, 1.344, -1.58, 1.422, 1.5]}, {"time": 1.5333, "y": 1.5, "curve": "stepped"}, {"time": 1.5667, "y": 1.5, "curve": [1.7, 0, 1.8, 0, 1.7, 1.5, 1.8, -1.58]}, {"time": 1.9333, "y": -1.58, "curve": "stepped"}, {"time": 1.9667, "y": -1.58, "curve": [2.089, 0, 2.178, 0, 2.089, -1.58, 2.178, 1.5]}, {"time": 2.3, "y": 1.5, "curve": "stepped"}, {"time": 2.3333, "y": 1.5, "curve": [2.478, 0, 2.589, 0, 2.478, 1.5, 2.589, -1.58]}, {"time": 2.7333, "y": -1.58, "curve": "stepped"}, {"time": 2.7667, "y": -1.58, "curve": [2.878, 0, 2.956, 0, 2.878, -1.58, 2.956, 1.5]}, {"time": 3.0667, "y": 1.5, "curve": "stepped"}, {"time": 3.1, "y": 1.5, "curve": [3.233, 0, 3.333, 0, 3.233, 1.5, 3.333, -1.58]}, {"time": 3.4667, "y": -1.58, "curve": "stepped"}, {"time": 3.5, "y": -1.58, "curve": [3.622, 0, 3.711, 0, 3.622, -1.58, 3.711, 1.5]}, {"time": 3.8333, "y": 1.5, "curve": "stepped"}, {"time": 3.8667, "y": 1.5, "curve": [3.948, 0, 4.085, 0, 3.948, 1.5, 4.085, -1.58]}, {"time": 4.1667, "y": -1.58, "curve": "stepped"}, {"time": 4.2, "y": -1.58, "curve": [4.322, 0, 4.411, 0, 4.322, -1.58, 4.411, 1.5]}, {"time": 4.5333, "y": 1.5}]}, "Eye_L_Eyelid_b": {"translate": [{}]}, "Eye_R_Eyelid_b": {"translate": [{}]}, "Mouth_cntrl": {"scale": [{"x": 0.998, "y": 1.002, "curve": [0.118, 0.987, 0.212, 0.926, 0.118, 1.012, 0.212, 1.072]}, {"time": 0.3333, "x": 0.926, "y": 1.072, "curve": "stepped"}, {"time": 0.3667, "x": 0.926, "y": 1.072, "curve": [0.489, 0.926, 0.578, 1, 0.489, 1.072, 0.578, 1]}, {"time": 0.7, "curve": "stepped"}, {"time": 0.7333, "curve": [0.878, 1, 0.989, 0.926, 0.878, 1, 0.989, 1.072]}, {"time": 1.1333, "x": 0.926, "y": 1.072, "curve": "stepped"}, {"time": 1.1667, "x": 0.926, "y": 1.072, "curve": [1.278, 0.926, 1.356, 1, 1.278, 1.072, 1.356, 1]}, {"time": 1.4667, "curve": "stepped"}, {"time": 1.5, "curve": [1.633, 1, 1.733, 0.926, 1.633, 1, 1.733, 1.072]}, {"time": 1.8667, "x": 0.926, "y": 1.072, "curve": "stepped"}, {"time": 1.9, "x": 0.926, "y": 1.072, "curve": [2.022, 0.926, 2.111, 1, 2.022, 1.072, 2.111, 1]}, {"time": 2.2333, "curve": "stepped"}, {"time": 2.2667, "curve": [2.411, 1, 2.522, 0.926, 2.411, 1, 2.522, 1.072]}, {"time": 2.6667, "x": 0.926, "y": 1.072, "curve": "stepped"}, {"time": 2.7, "x": 0.926, "y": 1.072, "curve": [2.811, 0.926, 2.889, 1, 2.811, 1.072, 2.889, 1]}, {"time": 3, "curve": "stepped"}, {"time": 3.0333, "curve": [3.167, 1, 3.267, 0.926, 3.167, 1, 3.267, 1.072]}, {"time": 3.4, "x": 0.926, "y": 1.072, "curve": "stepped"}, {"time": 3.4333, "x": 0.926, "y": 1.072, "curve": [3.535, 0.926, 3.656, 1, 3.535, 1.072, 3.656, 1]}, {"time": 3.7667, "curve": "stepped"}, {"time": 3.8, "curve": [3.933, 1, 4.049, 0.926, 3.933, 1, 4.049, 1.072]}, {"time": 4.1, "x": 0.926, "y": 1.072, "curve": "stepped"}, {"time": 4.1333, "x": 0.926, "y": 1.072, "curve": [4.256, 0.926, 4.344, 1, 4.256, 1.072, 4.344, 1]}, {"time": 4.4667, "curve": "stepped"}, {"time": 4.5, "curve": [4.512, 1, 4.523, 0.999, 4.512, 1, 4.523, 1.001]}, {"time": 4.5333, "x": 0.998, "y": 1.002}]}, "Saliva": {"translate": [{"y": 0.86, "curve": [0.034, 0, 0.067, 0, 0.034, 1.57, 0.067, 2.09]}, {"time": 0.1, "y": 2.09, "curve": [0.233, 0, 0.367, 0, 0.233, 2.09, 0.367, -4.58]}, {"time": 0.5, "y": -4.58, "curve": [0.622, 0, 0.744, 0, 0.622, -4.58, 0.744, 2.09]}, {"time": 0.8667, "y": 2.09, "curve": [1.011, 0, 1.156, 0, 1.011, 2.09, 1.156, -4.58]}, {"time": 1.3, "y": -4.58, "curve": [1.411, 0, 1.522, 0, 1.411, -4.58, 1.522, 2.09]}, {"time": 1.6333, "y": 2.09, "curve": [1.767, 0, 1.9, 0, 1.767, 2.09, 1.9, -4.58]}, {"time": 2.0333, "y": -4.58, "curve": [2.156, 0, 2.278, 0, 2.156, -4.58, 2.278, 2.09]}, {"time": 2.4, "y": 2.09, "curve": [2.556, 0, 2.711, 0, 2.556, 2.09, 2.711, -4.58]}, {"time": 2.8667, "y": -4.58, "curve": [2.967, 0, 3.067, 0, 2.967, -4.58, 3.067, 2.09]}, {"time": 3.1667, "y": 2.09, "curve": [3.3, 0, 3.433, 0, 3.3, 2.09, 3.433, -4.58]}, {"time": 3.5667, "y": -4.58, "curve": [3.689, 0, 3.811, 0, 3.689, -4.58, 3.811, 2.09]}, {"time": 3.9333, "y": 2.09, "curve": [4.044, 0, 4.156, 0, 4.044, 2.09, 4.156, -4.58]}, {"time": 4.2667, "y": -4.58, "curve": [4.356, 0, 4.445, 0, 4.356, -4.58, 4.445, -1.07]}, {"time": 4.5333, "y": 0.86}]}, "Cntr": {"translate": [{}], "scale": [{"x": 0.898, "y": 0.898, "curve": [0.128, 0.898, 0.272, 1.231, 0.128, 0.898, 0.272, 1.231]}, {"time": 0.4, "x": 1.231, "y": 1.231, "curve": [0.528, 1.231, 0.639, 0.898, 0.528, 1.231, 0.639, 0.898]}, {"time": 0.7667, "x": 0.898, "y": 0.898, "curve": [0.895, 0.898, 1.072, 1.231, 0.895, 0.898, 1.072, 1.231]}, {"time": 1.2, "x": 1.231, "y": 1.231, "curve": [1.328, 1.231, 1.422, 0.898, 1.328, 1.231, 1.422, 0.898]}, {"time": 1.5333, "x": 0.898, "y": 0.898, "curve": [1.661, 0.898, 1.805, 1.231, 1.661, 0.898, 1.805, 1.231]}, {"time": 1.9333, "x": 1.231, "y": 1.231, "curve": [2.061, 1.231, 2.172, 0.898, 2.061, 1.231, 2.172, 0.898]}, {"time": 2.3, "x": 0.898, "y": 0.898, "curve": [2.428, 0.898, 2.605, 1.231, 2.428, 0.898, 2.605, 1.231]}, {"time": 2.7333, "x": 1.231, "y": 1.231, "curve": [2.861, 1.231, 2.956, 0.898, 2.861, 1.231, 2.956, 0.898]}, {"time": 3.0667, "x": 0.898, "y": 0.898, "curve": [3.195, 0.898, 3.339, 1.231, 3.195, 0.898, 3.339, 1.231]}, {"time": 3.4667, "x": 1.231, "y": 1.231, "curve": [3.595, 1.231, 3.705, 0.898, 3.595, 1.231, 3.705, 0.898]}, {"time": 3.8333, "x": 0.898, "y": 0.898, "curve": [3.908, 0.898, 4.092, 1.231, 3.908, 0.898, 4.092, 1.231]}, {"time": 4.1667, "x": 1.231, "y": 1.231, "curve": [4.308, 1.231, 4.392, 0.898, 4.308, 1.231, 4.392, 0.898]}, {"time": 4.5333, "x": 0.898, "y": 0.898}]}, "Brow_R": {"rotate": [{}], "translate": [{"curve": "stepped"}, {"time": 0.6333, "curve": [0.656, 0, 0.678, 0, 0.656, 0, 0.678, 6.05]}, {"time": 0.7, "y": 6.05, "curve": "stepped"}, {"time": 0.7667, "y": 6.05, "curve": [0.822, 0, 0.878, 0, 0.822, 6.05, 0.878, 0]}, {"time": 0.9333, "curve": "stepped"}, {"time": 2.2, "curve": [2.222, 0, 2.244, 0, 2.222, 0, 2.244, 6.05]}, {"time": 2.2667, "y": 6.05, "curve": "stepped"}, {"time": 2.3333, "y": 6.05, "curve": [2.389, 0, 2.444, 0, 2.389, 6.05, 2.444, 0]}, {"time": 2.5, "curve": "stepped"}, {"time": 3.7333, "curve": [3.756, 0, 3.778, 0, 3.756, 0, 3.778, 6.05]}, {"time": 3.8, "y": 6.05, "curve": "stepped"}, {"time": 3.8667, "y": 6.05, "curve": [3.922, 0, 3.978, 0, 3.922, 6.05, 3.978, 0]}, {"time": 4.0333}]}, "Brow_L": {"rotate": [{}], "translate": [{"curve": "stepped"}, {"time": 0.5333, "curve": [0.556, 0, 0.578, 0, 0.556, 0, 0.578, -6.05]}, {"time": 0.6, "y": -6.05, "curve": "stepped"}, {"time": 0.6667, "y": -6.05, "curve": [0.722, 0, 0.778, 0, 0.722, -6.05, 0.778, 0]}, {"time": 0.8333, "curve": "stepped"}, {"time": 2.4, "curve": [2.422, 0, 2.444, 0, 2.422, 0, 2.444, -6.05]}, {"time": 2.4667, "y": -6.05, "curve": "stepped"}, {"time": 2.5333, "y": -6.05, "curve": [2.589, 0, 2.644, 0, 2.589, -6.05, 2.644, 0]}, {"time": 2.7, "curve": "stepped"}, {"time": 3.7333, "curve": [3.756, 0, 3.778, 0, 3.756, 0, 3.778, -6.05]}, {"time": 3.8, "y": -6.05, "curve": "stepped"}, {"time": 3.8667, "y": -6.05, "curve": [3.922, 0, 3.978, 0, 3.922, -6.05, 3.978, 0]}, {"time": 4.0333}]}, "Leg_L_IK": {"translate": [{}]}, "Leg_R_IK": {"translate": [{}]}, "Mouth_Bottom": {"translate": [{}]}, "blot": {"translate": [{"x": -12.21, "y": 15.4}], "scale": [{"x": 0.7, "y": 0.7}]}, "blot_drops_control": {"translate": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}, "Right": {"rotate": [{}]}, "Left": {"rotate": [{}]}}}, "t1_IDLE2": {"slots": {"blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "Body_outline": {"attachment": [{"name": "Body_outline"}]}, "Brow_l": {"attachment": [{"name": "Brow_l"}]}, "Brow_r": {"attachment": [{"name": "Brow_r"}]}, "Eyelid_l_r": {"attachment": [{"name": "Eyelid_l_r"}]}, "Eyelid_l_r2": {"attachment": [{"name": "Eyelid_l_r"}]}, "Eyelid_u_r": {"attachment": [{"name": "Eyelid_u_r"}]}, "Eyelid_u_r2": {"attachment": [{"name": "Eyelid_u_r"}]}, "Eye_L": {"attachment": [{"name": "Eye_r"}]}, "Eye_L2": {"attachment": [{"name": "Eye_r"}]}, "Eye_Pupil_r": {"attachment": [{"name": "Eye_Pupil_r"}]}, "Eye_Pupil_r2": {"attachment": [{"name": "Eye_Pupil_r"}]}, "Eye_red_L": {"rgba": [{"color": "ffffff00"}], "attachment": [{"name": "Eye_red_r"}]}, "Eye_red_L2": {"rgba": [{"color": "ffffff00"}], "attachment": [{"name": "Eye_red_r"}]}, "Leg_L_outline": {"attachment": [{"name": "Leg_l_outline"}]}, "Leg_R_outline": {"attachment": [{"name": "Leg_l_outline"}]}, "Mouth_base": {"attachment": [{"name": "Mouth_base"}]}, "Mouth_open": {"attachment": [{"name": "Mouth_open"}]}, "Saliva": {"attachment": [{"name": "Saliva"}]}, "Slippy_body": {"attachment": [{"name": "Slippy_body"}]}, "Slippy_leg_L": {"attachment": [{"name": "Slippy_leg_l"}]}, "Slippy_leg_R": {"attachment": [{"name": "Slippy_leg_l"}]}, "Teeth": {"attachment": [{"name": "<PERSON><PERSON>"}]}}, "bones": {"Eye_R_Eyelid_t": {"translate": [{"x": -2.12, "y": 27.44}]}, "Eye_L_Eyelid_t": {"translate": [{"x": -2.12, "y": 27.44}]}, "Eye_L": {"translate": [{}], "scale": [{}]}, "Eye_R": {"translate": [{}], "scale": [{}]}, "Main_Cntr": {"rotate": [{"curve": [0.023, 0, 0.045, 1]}, {"time": 0.0667, "value": 1, "curve": [0.089, 1, 0.112, -0.99]}, {"time": 0.1333, "value": -1, "curve": [0.491, -1.11, 0.179, 1]}, {"time": 0.2, "value": 1, "curve": [0.223, 1, 0.245, -0.99]}, {"time": 0.2667, "value": -1, "curve": [0.624, -1.11, 0.312, 1]}, {"time": 0.3333, "value": 1, "curve": [0.356, 1, 0.379, -0.99]}, {"time": 0.4, "value": -1, "curve": [0.757, -1.11, 0.445, 1]}, {"time": 0.4667, "value": 1, "curve": [0.489, 1, 0.512, -0.99]}, {"time": 0.5333, "value": -1, "curve": [0.891, -1.11, 0.579, 1]}, {"time": 0.6, "value": 1, "curve": [0.623, 1, 0.645, -0.99]}, {"time": 0.6667, "value": -1, "curve": [1.024, -1.11, 0.712, 1]}, {"time": 0.7333, "value": 1, "curve": [0.756, 1, 0.779, -0.99]}, {"time": 0.8, "value": -1, "curve": [1.157, -1.11, 0.845, 1]}, {"time": 0.8667, "value": 1, "curve": [0.889, 1, 0.912, -0.99]}, {"time": 0.9333, "value": -1, "curve": [1.291, -1.11, 0.979, 1]}, {"time": 1, "value": 1, "curve": [1.023, 1, 1.045, -0.99]}, {"time": 1.0667, "value": -1, "curve": [1.424, -1.11, 1.112, 1]}, {"time": 1.1333, "value": 1, "curve": [1.156, 1, 1.178, 0]}, {"time": 1.2}], "translate": [{"y": -9.16}], "scale": [{}]}, "Low_Cntr": {"scale": [{"x": 1.05, "y": 0.95}], "shear": [{}]}, "Eye_R_Pupil": {"translate": [{"x": -3.97, "y": -5.53}], "scale": [{"x": 0.694, "y": 0.694}]}, "Eye_L_Pupil": {"translate": [{"x": -3.97, "y": -5.53}], "scale": [{"x": 0.694, "y": 0.694}]}, "Mouth": {"translate": [{"curve": [0.078, 0, 0.156, 0, 0.078, 0, 0.156, -2.65]}, {"time": 0.2333, "y": -2.65, "curve": [0.344, 0, 0.456, 0, 0.344, -2.65, 0.456, 1.5]}, {"time": 0.5667, "y": 1.5, "curve": [0.7, 0, 0.833, 0, 0.7, 1.5, 0.833, -2.65]}, {"time": 0.9667, "y": -2.65, "curve": [1.044, 0, 1.122, 0, 1.044, -2.65, 1.122, 0]}, {"time": 1.2}]}, "Eye_L_Eyelid_b": {"translate": [{"x": 0.54, "y": -8.19}]}, "Eye_R_Eyelid_b": {"translate": [{"x": 0.54, "y": -8.19}]}, "Mouth_cntrl": {"scale": [{"x": 1.118, "y": 0.632, "curve": [0.022, 1.118, 0.044, 1.186, 0.022, 0.632, 0.044, 0.514]}, {"time": 0.0667, "x": 1.186, "y": 0.514, "curve": [0.089, 1.186, 0.111, 1.118, 0.089, 0.514, 0.111, 0.632]}, {"time": 0.1333, "x": 1.118, "y": 0.632, "curve": [0.156, 1.118, 0.178, 1.186, 0.156, 0.632, 0.178, 0.514]}, {"time": 0.2, "x": 1.186, "y": 0.514, "curve": [0.222, 1.186, 0.244, 1.118, 0.222, 0.514, 0.244, 0.632]}, {"time": 0.2667, "x": 1.118, "y": 0.632, "curve": [0.289, 1.118, 0.311, 1.186, 0.289, 0.632, 0.311, 0.514]}, {"time": 0.3333, "x": 1.186, "y": 0.514, "curve": [0.356, 1.186, 0.378, 1.118, 0.356, 0.514, 0.378, 0.632]}, {"time": 0.4, "x": 1.118, "y": 0.632, "curve": [0.422, 1.118, 0.444, 1.186, 0.422, 0.632, 0.444, 0.514]}, {"time": 0.4667, "x": 1.186, "y": 0.514, "curve": [0.489, 1.186, 0.511, 1.118, 0.489, 0.514, 0.511, 0.632]}, {"time": 0.5333, "x": 1.118, "y": 0.632, "curve": [0.556, 1.118, 0.578, 1.186, 0.556, 0.632, 0.578, 0.514]}, {"time": 0.6, "x": 1.186, "y": 0.514, "curve": [0.622, 1.186, 0.644, 1.118, 0.622, 0.514, 0.644, 0.632]}, {"time": 0.6667, "x": 1.118, "y": 0.632, "curve": [0.689, 1.118, 0.711, 1.186, 0.689, 0.632, 0.711, 0.514]}, {"time": 0.7333, "x": 1.186, "y": 0.514, "curve": [0.756, 1.186, 0.778, 1.118, 0.756, 0.514, 0.778, 0.632]}, {"time": 0.8, "x": 1.118, "y": 0.632, "curve": [0.822, 1.118, 0.844, 1.186, 0.822, 0.632, 0.844, 0.514]}, {"time": 0.8667, "x": 1.186, "y": 0.514, "curve": [0.889, 1.186, 0.911, 1.118, 0.889, 0.514, 0.911, 0.632]}, {"time": 0.9333, "x": 1.118, "y": 0.632, "curve": [0.956, 1.118, 0.978, 1.186, 0.956, 0.632, 0.978, 0.514]}, {"time": 1, "x": 1.186, "y": 0.514, "curve": [1.022, 1.186, 1.044, 1.118, 1.022, 0.514, 1.044, 0.632]}, {"time": 1.0667, "x": 1.118, "y": 0.632, "curve": [1.089, 1.118, 1.111, 1.186, 1.089, 0.632, 1.111, 0.514]}, {"time": 1.1333, "x": 1.186, "y": 0.514, "curve": [1.156, 1.186, 1.178, 1.118, 1.156, 0.514, 1.178, 0.632]}, {"time": 1.2, "x": 1.118, "y": 0.632}]}, "Saliva": {"translate": [{"y": -27.68}]}, "Cntr": {"translate": [{}], "scale": [{"curve": [0.078, 1, 0.156, 1.231, 0.078, 1, 0.156, 1.231]}, {"time": 0.2333, "x": 1.231, "y": 1.231, "curve": [0.361, 1.231, 0.456, 0.898, 0.361, 1.231, 0.456, 0.898]}, {"time": 0.5667, "x": 0.898, "y": 0.898, "curve": [0.695, 0.898, 0.839, 1.231, 0.695, 0.898, 0.839, 1.231]}, {"time": 0.9667, "x": 1.231, "y": 1.231, "curve": [1.044, 1.231, 1.122, 1, 1.044, 1.231, 1.122, 1]}, {"time": 1.2}]}, "Brow_L": {"rotate": [{"value": -18.62}], "translate": [{"x": 6.91, "y": -0.85}]}, "Brow_R": {"rotate": [{"value": 24.11}], "translate": [{"x": 8.64, "y": 1.27}]}, "Leg_L_IK": {"translate": [{}]}, "Leg_R_IK": {"translate": [{}]}, "Mouth_Bottom": {"translate": [{}]}, "blot": {"translate": [{"x": -12.21, "y": 15.4}], "scale": [{"x": 0.7, "y": 0.7}]}, "blot_drops_control": {"translate": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}, "Right": {"rotate": [{}]}, "Left": {"rotate": [{}]}}}, "t1_IDLE3": {"slots": {"blot": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop3": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop4": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop5": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop6": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop7": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop8": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s1": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "blot_drop_s2": {"rgba": [{"color": "ffffffff"}], "attachment": [{}]}, "Body_outline": {"attachment": [{"name": "Body_outline"}]}, "Brow_l": {"attachment": [{"name": "Brow_l"}]}, "Brow_r": {"attachment": [{"name": "Brow_r"}]}, "Eyelid_l_r": {"attachment": [{"name": "Eyelid_l_r"}]}, "Eyelid_l_r2": {"attachment": [{"name": "Eyelid_l_r"}]}, "Eyelid_u_r": {"attachment": [{"name": "Eyelid_u_r"}]}, "Eyelid_u_r2": {"attachment": [{"name": "Eyelid_u_r"}]}, "Eye_L": {"attachment": [{"name": "Eye_r"}]}, "Eye_L2": {"attachment": [{"name": "Eye_r"}]}, "Eye_Pupil_r": {"attachment": [{"name": "Eye_Pupil_r"}]}, "Eye_Pupil_r2": {"attachment": [{"name": "Eye_Pupil_r"}]}, "Eye_red_L": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "Eye_red_r"}]}, "Eye_red_L2": {"rgba": [{"color": "ffffffff"}], "attachment": [{"name": "Eye_red_r"}]}, "Leg_L_outline": {"attachment": [{"name": "Leg_l_outline"}]}, "Leg_R_outline": {"attachment": [{"name": "Leg_l_outline"}]}, "Mouth_base": {"attachment": [{"name": "Mouth_base"}]}, "Mouth_open": {"attachment": [{"name": "Mouth_open"}]}, "Saliva": {"attachment": [{"name": "Saliva"}]}, "Slippy_body": {"attachment": [{"name": "Slippy_body"}]}, "Slippy_leg_L": {"attachment": [{"name": "Slippy_leg_l"}]}, "Slippy_leg_R": {"attachment": [{"name": "Slippy_leg_l"}]}, "Teeth": {"attachment": [{"name": "<PERSON><PERSON>"}]}}, "bones": {"Eye_R_Eyelid_t": {"translate": [{"x": -2.12, "y": 27.44}]}, "Eye_L_Eyelid_t": {"translate": [{"x": -2.12, "y": 27.44}]}, "Eye_L": {"translate": [{}], "scale": [{}]}, "Eye_R": {"translate": [{}], "scale": [{}]}, "Main_Cntr": {"rotate": [{"curve": [0.023, 0, 0.045, 1]}, {"time": 0.0667, "value": 1, "curve": [0.089, 1, 0.112, -0.99]}, {"time": 0.1333, "value": -1, "curve": [0.491, -1.11, 0.179, 1]}, {"time": 0.2, "value": 1, "curve": [0.223, 1, 0.245, -0.99]}, {"time": 0.2667, "value": -1, "curve": [0.624, -1.11, 0.312, 1]}, {"time": 0.3333, "value": 1, "curve": [0.356, 1, 0.379, -0.99]}, {"time": 0.4, "value": -1, "curve": [0.757, -1.11, 0.445, 1]}, {"time": 0.4667, "value": 1, "curve": [0.489, 1, 0.512, -0.99]}, {"time": 0.5333, "value": -1, "curve": [0.891, -1.11, 0.579, 1]}, {"time": 0.6, "value": 1, "curve": [0.623, 1, 0.645, -0.99]}, {"time": 0.6667, "value": -1, "curve": [1.024, -1.11, 0.712, 1]}, {"time": 0.7333, "value": 1, "curve": [0.756, 1, 0.779, -0.99]}, {"time": 0.8, "value": -1, "curve": [1.157, -1.11, 0.845, 1]}, {"time": 0.8667, "value": 1, "curve": [0.889, 1, 0.912, -0.99]}, {"time": 0.9333, "value": -1, "curve": [1.291, -1.11, 0.979, 1]}, {"time": 1, "value": 1, "curve": [1.023, 1, 1.045, -0.99]}, {"time": 1.0667, "value": -1, "curve": [1.424, -1.11, 1.112, 1]}, {"time": 1.1333, "value": 1, "curve": [1.156, 1, 1.178, 0]}, {"time": 1.2}], "translate": [{"y": -9.16}], "scale": [{}]}, "Low_Cntr": {"scale": [{"x": 1.05, "y": 0.95}], "shear": [{}]}, "Eye_R_Pupil": {"translate": [{"x": -3.97, "y": -5.53}], "scale": [{"x": 0.694, "y": 0.694}]}, "Eye_L_Pupil": {"translate": [{"x": -3.97, "y": -5.53}], "scale": [{"x": 0.694, "y": 0.694}]}, "Mouth": {"translate": [{"curve": [0.078, 0, 0.156, 0, 0.078, 0, 0.156, -2.65]}, {"time": 0.2333, "y": -2.65, "curve": [0.344, 0, 0.456, 0, 0.344, -2.65, 0.456, 1.5]}, {"time": 0.5667, "y": 1.5, "curve": [0.7, 0, 0.833, 0, 0.7, 1.5, 0.833, -2.65]}, {"time": 0.9667, "y": -2.65, "curve": [1.044, 0, 1.122, 0, 1.044, -2.65, 1.122, 0]}, {"time": 1.2}]}, "Eye_L_Eyelid_b": {"translate": [{"x": 0.54, "y": -8.19}]}, "Eye_R_Eyelid_b": {"translate": [{"x": 0.54, "y": -8.19}]}, "Mouth_cntrl": {"scale": [{"x": 1.118, "y": 0.632, "curve": [0.022, 1.118, 0.044, 1.186, 0.022, 0.632, 0.044, 0.514]}, {"time": 0.0667, "x": 1.186, "y": 0.514, "curve": [0.089, 1.186, 0.111, 1.118, 0.089, 0.514, 0.111, 0.632]}, {"time": 0.1333, "x": 1.118, "y": 0.632, "curve": [0.156, 1.118, 0.178, 1.186, 0.156, 0.632, 0.178, 0.514]}, {"time": 0.2, "x": 1.186, "y": 0.514, "curve": [0.222, 1.186, 0.244, 1.118, 0.222, 0.514, 0.244, 0.632]}, {"time": 0.2667, "x": 1.118, "y": 0.632, "curve": [0.289, 1.118, 0.311, 1.186, 0.289, 0.632, 0.311, 0.514]}, {"time": 0.3333, "x": 1.186, "y": 0.514, "curve": [0.356, 1.186, 0.378, 1.118, 0.356, 0.514, 0.378, 0.632]}, {"time": 0.4, "x": 1.118, "y": 0.632, "curve": [0.422, 1.118, 0.444, 1.186, 0.422, 0.632, 0.444, 0.514]}, {"time": 0.4667, "x": 1.186, "y": 0.514, "curve": [0.489, 1.186, 0.511, 1.118, 0.489, 0.514, 0.511, 0.632]}, {"time": 0.5333, "x": 1.118, "y": 0.632, "curve": [0.556, 1.118, 0.578, 1.186, 0.556, 0.632, 0.578, 0.514]}, {"time": 0.6, "x": 1.186, "y": 0.514, "curve": [0.622, 1.186, 0.644, 1.118, 0.622, 0.514, 0.644, 0.632]}, {"time": 0.6667, "x": 1.118, "y": 0.632, "curve": [0.689, 1.118, 0.711, 1.186, 0.689, 0.632, 0.711, 0.514]}, {"time": 0.7333, "x": 1.186, "y": 0.514, "curve": [0.756, 1.186, 0.778, 1.118, 0.756, 0.514, 0.778, 0.632]}, {"time": 0.8, "x": 1.118, "y": 0.632, "curve": [0.822, 1.118, 0.844, 1.186, 0.822, 0.632, 0.844, 0.514]}, {"time": 0.8667, "x": 1.186, "y": 0.514, "curve": [0.889, 1.186, 0.911, 1.118, 0.889, 0.514, 0.911, 0.632]}, {"time": 0.9333, "x": 1.118, "y": 0.632, "curve": [0.956, 1.118, 0.978, 1.186, 0.956, 0.632, 0.978, 0.514]}, {"time": 1, "x": 1.186, "y": 0.514, "curve": [1.022, 1.186, 1.044, 1.118, 1.022, 0.514, 1.044, 0.632]}, {"time": 1.0667, "x": 1.118, "y": 0.632, "curve": [1.089, 1.118, 1.111, 1.186, 1.089, 0.632, 1.111, 0.514]}, {"time": 1.1333, "x": 1.186, "y": 0.514, "curve": [1.156, 1.186, 1.178, 1.118, 1.156, 0.514, 1.178, 0.632]}, {"time": 1.2, "x": 1.118, "y": 0.632}]}, "Saliva": {"translate": [{"y": -27.68}]}, "Cntr": {"translate": [{}], "scale": [{"curve": [0.078, 1, 0.156, 1.231, 0.078, 1, 0.156, 1.231]}, {"time": 0.2333, "x": 1.231, "y": 1.231, "curve": [0.361, 1.231, 0.456, 0.898, 0.361, 1.231, 0.456, 0.898]}, {"time": 0.5667, "x": 0.898, "y": 0.898, "curve": [0.695, 0.898, 0.839, 1.231, 0.695, 0.898, 0.839, 1.231]}, {"time": 0.9667, "x": 1.231, "y": 1.231, "curve": [1.044, 1.231, 1.122, 1, 1.044, 1.231, 1.122, 1]}, {"time": 1.2}]}, "Brow_L": {"rotate": [{"value": -18.62}], "translate": [{"x": 6.91, "y": -0.85}]}, "Brow_R": {"rotate": [{"value": 24.11}], "translate": [{"x": 8.64, "y": 1.27}]}, "Leg_L_IK": {"translate": [{}]}, "Leg_R_IK": {"translate": [{}]}, "Mouth_Bottom": {"translate": [{}]}, "blot": {"translate": [{"x": -12.21, "y": 15.4}], "scale": [{"x": 0.7, "y": 0.7}]}, "blot_drops_control": {"translate": [{}]}, "blot_drop2": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop3": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop4": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s1": {"rotate": [{}], "translate": [{}]}, "blot_drop_s2": {"rotate": [{}], "translate": [{}]}, "blot_drop5": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s3": {"rotate": [{}], "translate": [{}]}, "blot_drop6": {"rotate": [{}], "translate": [{}], "scale": [{}]}, "blot_drop_s4": {"rotate": [{}], "translate": [{}]}, "Right": {"rotate": [{}]}, "Left": {"rotate": [{}]}}}}}
FROM node:20-alpine AS base

FROM base AS dependencies

WORKDIR /app
COPY package.json package-lock.json ./

RUN npm install --ignore-scripts

FROM base AS build

WORKDIR /app

ARG VITE_APP_ENV
ENV VITE_APP_ENV=${VITE_APP_ENV}
ARG VITE_CLIENT_URL
ENV VITE_CLIENT_URL=${VITE_CLIENT_URL}
ARG VITE_PRELOAD_SERVER_URL
ENV VITE_PRELOAD_SERVER_URL=${VITE_PRELOAD_SERVER_URL}
ARG VITE_TONCONNECT_MANIFEST
ENV VITE_TONCONNECT_MANIFEST=${VITE_TONCONNECT_MANIFEST}
ARG VITE_REFLINK_ORIGIN
ENV VITE_REFLINK_ORIGIN=${VITE_REFLINK_ORIGIN}
ARG VITE_GTM_ID
ENV VITE_GTM_ID=${VITE_GTM_ID}
ARG VITE_MAIN_WALLET_ADDR
ENV VITE_MAIN_WALLET_ADDR=${VITE_MAIN_WALLET_ADDR}
ARG VITE_TON_STATION_BOT_LINK
ENV VITE_TON_STATION_BOT_LINK=${VITE_TON_STATION_BOT_LINK}

COPY . .
COPY --from=dependencies /app/node_modules ./node_modules
RUN npm run build
RUN npm prune --production --ignore-scripts

FROM nginx:stable-alpine AS nginx

WORKDIR /app
COPY --from=build /app/dist/ ./dist/
COPY --from=build /app/nginx/default.conf /etc/nginx/conf.d/default.conf
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]

{"actions": {"allowAccess": "Разрешить доступ", "back": "Назад", "cancelTransaction": "Отменить транзакцию", "claim": "Получить", "collect": "Забрать", "connectWallet": "Подключить кошелек", "continue": "Продолжить", "create": "Создать", "disconnect": "Отключить", "getReward": "Получить награду", "goToHome": "Перейти на главную", "goToShop": "Перейти в магазин", "goToSkins": "Перейти к скинам", "inviteFriend": "Пригласить друга", "leave": "Выйти", "makeTransaction": "Совершить транзакцию", "play": "Играть", "playAgain": "Играть снова", "select": "Выбрать", "startFarming": "Фармить", "subscribe": "Подписаться", "tapToCollect": "Получить", "tapToContinue": "Нажмите\nчтобы продолжить", "tapToJump": "Коснитесь, чтобы прыгнуть", "tapToOpen": "Нажмите, чтобы открыть", "tapToPlay": "Нажмите чтобы играть", "tapToShoot": "Нажмите, чтобы стрелять", "tapToSkip": "Нажмите, чтобы пропустить", "withdraw": "Снять", "hold": "Удерживать для автовращения", "spin": "Крутить", "stop": "Стоп", "openChat": "Открыть чат", "startEvent": "Начать событие", "join": "Присоединиться", "got": "Понятно!", "open_telegram": "Открыть Telegram"}, "search": "Поиск", "boosters": {"stackableAimbot": {"description": "Увеличивает количество монет, собранных за сессию.", "fullName": "<PERSON><PERSON>", "name": "<PERSON><PERSON>"}, "stackableJumper": {"description": "Увеличивает количество монет, собранных за сессию.", "fullName": "Пружина", "name": "Пружина"}, "stackableMagneticField": {"description": "Увеличивает количество монет, собранных за сессию.", "fullName": "Магнитное поле", "name": "Маг<PERSON>ит"}, "title": "Бустеры", "endlessRun": "Endless Run", "rewards": "Награды, которые вы можете получить:", "select": "Выберите бустеры:"}, "warning": "Внимание", "mainMenu": "Главное меню", "pausedOnBack": "Вы уверены, что хотите покинуть игру?", "gameover": "Конец игры", "totalScore": "Общий счет:", "currentScore": "Текущий счет:", "allowGyroscope": "Разрешить\nгироскоп", "free": "Бесплатно", "selected": "Выбрано", "hotRecord": "Hot Record", "joinUs": "Присоединяйся!", "termsOfUse": "Условия Использования", "faq": "Часто задаваемые вопросы", "swipeScreen": "Управляйте пальцем\nпо экрану", "controlMethod": {"gyroscope": "Гироскоп", "swipe": "Свайп", "selectMsg": "Выберите удобный способ управления", "selected": "Выбрано", "select": "Выбрать"}, "features": {"customCoinEvent": "Событие со скинами", "dailyReward": "Ежедневные награды", "dynamicCoins_1": "Волшебная радуга", "dynamicCoins_2": "Глубокое погружение", "farming": "<PERSON>ар<PERSON><PERSON><PERSON><PERSON>", "hotRecordEvent": "Горячий рекорд", "lives": "Жизни", "onePercentEvent": "Клуб 1%", "tonMiningEvent": "Тон <PERSON>айнинг", "withdraw": "Снятие", "puzzleCoins": "Ледяной фрагмент"}, "lootboxes": {"rainbowLootBox": "Rainbow Box", "luckyLootBox": "Lucky Box", "magicLootBox": "Magic Box", "fightLootBox": "Fight Box", "duckyLootBox": "Ducky Box"}, "skins": {"title": "Скины", "description": "Множитель скинов — покупай больше для повышения множителя", "yourSkins": "Твои скины: {amount}/{total}", "newSkin": "Вы получили новый скин!", "list": {"0": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> is spinning into chaos, ready for a wild ride!"}, "1": {"title": "Emo-Uni", "description": "<PERSON><PERSON> doesn't smile, but it's all about the feels."}, "2": {"title": "Uni Plumber", "description": "Serious plumbing skills, magical twist — Uni Plumber at your service!"}, "3": {"title": "Uni Kitty", "description": "Uni Kitty is the purr-fect blend of fun and adventure!"}, "4": {"title": "Uni Ninja", "description": "Silent and swift, Uni Ninja is always one step ahead."}, "5": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Fries first, questions later — <PERSON><PERSON><PERSON><PERSON> is busy eating!"}, "6": {"title": "<PERSON><PERSON>", "description": "With every Uni Potter leap, the world fills with magic!"}, "7": {"title": "Devil Uni", "description": "Ready for some trouble? Devil Uni is on the case!"}, "8": {"title": "Super Uni", "description": "Super Uni is here to save the day!"}, "9": {"title": "Resistance Uni", "description": "Join Digital Resistance with Resistance Uni!"}, "10": {"title": "Uni Diver", "description": "Uni Diver leaves behind bubble trails everywhere, literally!"}, "11": {"title": "Chicken Uni", "description": "<PERSON> is a feathered friend, making each journey egg-citing!"}, "12": {"title": "Capy Uni", "description": "Relax mode: ON<PERSON> <PERSON><PERSON> brings calm vibes to every journey."}, "13": {"title": "<PERSON><PERSON>", "description": "Smelly but proud, <PERSON><PERSON> isn't afraid to be himself!"}, "14": {"title": "Pepe Uni", "description": "Adventures are better with <PERSON><PERSON><PERSON>'s classic look!"}, "1000": {"title": "Uni Inu", "description": "Bark, sparkle, repeat — <PERSON><PERSON> is unstoppable!"}, "1001": {"title": "<PERSON><PERSON>", "description": "\"Make Adventures Great Again\" is the motto of Uni Trump!"}, "1002": {"title": "Chainsaw Uni", "description": "Chainsaw Uni turns obstacles into sawdust. Bzzz!"}, "1003": {"title": "SWAG Uni", "description": "SWAG Uni — gold, glam, and instant charm!"}, "1004": {"title": "Uni Fremen", "description": "Jump for freedom, smash every monster!"}, "1005": {"title": "Grump Uni", "description": "Jump, fall, sigh… Repeat. The saddest ticket collector in the Universe"}, "1006": {"title": "<PERSON>", "description": "Jumps fast, falls hard, complains always. Big drama, small bird!"}, "1007": {"title": "Uni Duo", "description": "Funny owl teaching you how to jump. Don't skip her lessons."}, "1008": {"title": "Spring Uni", "description": "Fresh, funny and loves green tickets."}, "1009": {"title": "Uni Magic", "description": "<PERSON> Uni with some magic, but not much luck."}, "1010": {"title": "<PERSON><PERSON> Luck", "description": "Rich and lucky, old <PERSON><PERSON> who catches 10X more coins!"}, "2000": {"title": "Chill Uni", "description": "Always calm, always stylish. His cool mind is the key to victory!"}, "2001": {"title": "Mystic Uni", "description": "Master of magic and mysteries. One movement – and reality changes!"}, "2002": {"title": "Uni Beat", "description": "He doesn't just listen to music – he controls it!"}, "2003": {"title": "Uni Relic", "description": "The ancient spirit that has returned. Its power is beyond time!"}, "2004": {"title": "Uni Reaper", "description": "A mysterious souls hunter that comes on the darkest nights."}, "2005": {"title": "Uni Inferno", "description": "A real fire demon that burns everything in its path."}, "2006": {"title": "Uni Bera", "description": "Cute Bera with Ooga Booga vibes"}, "2007": {"title": "Ponke Uni", "description": "Mad, degen, and always craving tickets"}, "2008": {"title": "Uni Bull", "description": "Jumps UP Only like every bull market"}, "2009": {"title": "Uni Doge", "description": "Woof woof, pump and collect"}, "2010": {"title": "Uni Popcat", "description": "Eats TON with wide open mouth"}, "2011": {"title": "Uni Pengu", "description": "Soft vibes, funny moves, big wins"}, "2012": {"title": "<PERSON><PERSON>", "description": "Jumps for fries and cola, but collects tickets."}, "2013": {"title": "Uni Art", "description": "Smiles when Exploiters scream, and collects your tickets."}, "2014": {"title": "<PERSON><PERSON>", "description": "The face remains hidden, the loyalty — never!"}, "2015": {"title": "Uni Shroom", "description": "Cap so red, Airdrop ahead!"}, "2016": {"title": "Uni Princess", "description": "Jumps so clean, like a queen!"}, "2017": {"title": "Uni Cloud", "description": "Soft and high, TON from sky!"}, "2018": {"title": "Uni Fairy", "description": "Liquid power, tickets shower!"}, "2019": {"title": "Uni Mixture", "description": "Magic charm, Airdrop farm!"}, "2020": {"title": "Uni Genie", "description": "Wish comes true—TON for you!"}, "2021": {"title": "Uni Rubik", "description": "Twist and turn, TON to earn!"}, "2022": {"title": "Uni Gift", "description": "Unwrap the fun, gifts for everyone!"}, "2023": {"title": "Uni Rich", "description": "Gold on top, farming Airdrop nonstop!"}, "2024": {"title": "Uni Neko", "description": "Paw for luck, <PERSON><PERSON> gets stuck!"}, "2025": {"title": "Uni Rabbit", "description": "Fast and free, tickets for me!"}, "2026": {"title": "Uni Dice", "description": "Jump and spin, lucky win!"}, "2027": {"title": "Uni Radio", "description": "Signal strong, TON all day long!"}, "2028": {"title": "Uni NightSky", "description": "Night so clear, TON is near!"}, "2029": {"title": "Uni Lumpy", "description": "Let the lumps guide you to epic rewards!"}, "2030": {"title": "Uni Rocket", "description": "Flying To The Moon while pumping UNICOIN!"}, "2031": {"title": "Uni Luna", "description": "Magic Moon cat transforming jumps into tickets."}, "2032": {"title": "Astro Uni", "description": "Jumps so high that can't breath without a costume."}, "2033": {"title": "Uni Basket", "description": "Bonus-packed and ready to jump"}, "2034": {"title": "Chicken Uni", "description": "Can't fly, but jumps like a champ!"}, "2035": {"title": "<PERSON><PERSON>", "description": "Hop-hop to the top!"}, "2036": {"title": "<PERSON><PERSON>", "description": "Sweet, orange, unstoppable."}, "2037": {"title": "Uni Egg", "description": "What's inside? Only jumping reveals!"}, "2038": {"title": "<PERSON><PERSON>", "description": "A flower with jump power."}, "2039": {"title": "Uni Citizen", "description": "Basic skin, still a win!"}, "2040": {"title": "Uni TNT", "description": "Jump, collect, and Boom connect!"}, "2041": {"title": "Uni Zombie", "description": "From grave to sky, he flies high!"}, "2042": {"title": "Uni Diamond", "description": "<PERSON><PERSON> set, best jump yet!"}, "2043": {"title": "<PERSON><PERSON>", "description": "From cube to cloud, <PERSON> jumps proud!"}, "2044": {"title": "<PERSON><PERSON>", "description": "Creepy and cool, breaking every rule!"}, "2045": {"title": "Uni Creeper", "description": "One big boom, clears the room!"}, "2046": {"title": "<PERSON><PERSON>", "description": "With big-eyed cheer, <PERSON><PERSON> is near!"}, "2047": {"title": "<PERSON><PERSON>", "description": "Brave and slick, jumps that stick!"}, "2048": {"title": "Uni Balthazar", "description": "Dance and spin, jump to win!"}, "2049": {"title": "<PERSON><PERSON>", "description": "Yellow and wild, jumpin' child!"}, "2050": {"title": "Uni Evil", "description": "Purple beast, ticket feast!"}, "2051": {"title": "Uni Gru", "description": "Evil plan, TON for the clan!"}, "2052": {"title": "Uni Vector", "description": "Vector's plan? Catch if you can!"}, "2053": {"title": "<PERSON><PERSON>", "description": "Jump and dive, feel alive!"}, "2054": {"title": "Jedi Uni", "description": "Force in the air, tickets everywhere!"}, "2055": {"title": "Sith Uni", "description": "Shadows call, tickets fall!"}, "2056": {"title": "Uni Storm", "description": "Shooting in monsters – targeting tickets!"}, "2057": {"title": "<PERSON><PERSON><PERSON>i", "description": "Small and quick, magic trick!"}, "2058": {"title": "Wookiee Uni", "description": "Big and loud, jumps proud!"}, "2059": {"title": "Mando Uni", "description": "Catching tickets - this is the way!"}, "2060": {"title": "Captain <PERSON><PERSON>", "description": "Leap with might, win the fight!"}, "2061": {"title": "<PERSON>", "description": "Big green hop, can't stop!"}, "2062": {"title": "Iron Uni", "description": "Armor on, jumping strong!"}, "2063": {"title": "<PERSON><PERSON> Loki", "description": "Laugh and flee, ticket spree!"}, "2064": {"title": "<PERSON>", "description": "Web of luck, tickets stuck!"}, "2065": {"title": "<PERSON><PERSON>", "description": "Power punch, big ticket bunch!"}, "2066": {"title": "<PERSON>", "description": "Thunder power, ticket shower!"}, "2067": {"title": "Uni Po", "description": "Hero of snacks, fights with whacks!"}, "2068": {"title": "<PERSON><PERSON>", "description": "Calm and quick, master trick!"}, "2069": {"title": "Mr. <PERSON>", "description": "Bowl jump fun, tickets run!"}, "2070": {"title": "<PERSON>", "description": "Horned and mean, TON machine!"}, "2071": {"title": "Tai Lung Uni", "description": "Cold and bold, TON unfolds!"}, "2072": {"title": "Tigress Uni", "description": "Eyes that burn, watch her turn!"}, "2073": {"title": "<PERSON><PERSON><PERSON>", "description": "Wise and slow, TON will grow!"}, "2074": {"title": "<PERSON>", "description": "Slow but sure, TONs secure!"}, "2075": {"title": "Krabs Uni", "description": "Shell so slick, TO<PERSON> comes quick!"}, "2076": {"title": "<PERSON>", "description": "Starry spin, tickets win!"}, "2077": {"title": "Sponge Uni", "description": "Jelly jump, tickets pump!"}, "2078": {"title": "Plankton Uni", "description": "Secret plan, tickets in the pan!"}, "2079": {"title": "<PERSON>", "description": "Jumps with brains, TON in chains!"}, "2080": {"title": "<PERSON><PERSON><PERSON>", "description": "Grumpy dash, still gets cash!"}, "3000": {"title": "<PERSON><PERSON>", "description": "Every jump is a gamble, every fall is a lesson."}, "-1": {"title": "Uni", "description": "<PERSON><PERSON> is the ultimate dreamer, believing in the impossible and inspiring everyone around!"}}, "requirenments": {"inviteFriend": "Пригласи больше друзей: {amount}", "wallet": "Подключи кошелек", "transaction": "Сделай первую транзакцию", "box": "{boxType} Бокс", "dailyReward": "Дней до получения: {days}", "inProgress": "В процессе..."}}, "achievements": {"completed": "Завершено", "description": "Получите бонус к своему множителю!", "list": {"1": {"1": {"description": "Пригласить 1 друга", "name": "Счастливы вместе"}, "2": {"description": "Пригласить 10 друзей", "name": "Местный босс"}, "3": {"description": "Пригласить 100 друзей", "name": "Реферальный хвастун"}, "4": {"description": "Пригласить 500 друзей", "name": "Инфлюенсер"}, "5": {"description": "Пригласить 1000 друзей", "name": "<PERSON>и<PERSON><PERSON><PERSON> Едино<PERSON><PERSON>гов"}}, "2": {"1": {"description": "Возродиться 1 раз", "name": "Упс..."}, "2": {"description": "Возродиться 10 раз", "name": "Больше, чем кот"}, "3": {"description": "Возродиться 100 раз", "name": "Я вернусь"}, "4": {"description": "Возродиться 1 000 раз", "name": "Не сегодня"}, "5": {"description": "Возродиться 10 000 раз", "name": "Феникс"}}, "3": {"1": {"description": "Рекорд: \n10 000", "name": "Новая зависимость"}, "2": {"description": "Рекорд: \n100 000", "name": "Студент-прыгун"}, "3": {"description": "Рекорд: \n300 000", "name": "Вверх! Вверх! Вверх!"}, "4": {"description": "Рекорд: \n500 000", "name": "Мастер прыжков"}, "5": {"description": "Рекорд: \n1 000 000", "name": "На Луну"}}, "4": {"1": {"description": "Всего потрачено монет: \n10 000", "name": "Бедный студент"}, "2": {"description": "Всего потрачено монет: \n100 000", "name": "Умный транжира"}, "3": {"description": "Всего потрачено монет: \n500 000", "name": "Скрудж Макдак"}, "4": {"description": "Всего потрачено монет: \n1 000 000", "name": "Золотой миллионер"}, "5": {"description": "Всего потрачено монет: \n2 000 000", "name": "Золотая карта"}}, "5": {"1": {"description": "Прыгнуть на 10 монстров", "name": "Боинк Боинк"}, "2": {"description": "Прыгнуть на 30 монстров", "name": "Упс, раздавил"}, "3": {"description": "Прыгнуть на 50 монстров", "name": "Прыжкопокалипсис"}, "4": {"description": "Прыгнуть на 100 монстров", "name": "Пружинящая Месть"}, "5": {"description": "Прыгнуть на 300 монстров", "name": "Раздавленные закуски"}}, "6": {"1": {"description": "Застрелить 10 монстров", "name": "Создатель боли"}, "2": {"description": "Застрелить 30 монстров", "name": "Хирург с дробовиком"}, "3": {"description": "Застрелить 50 монстров", "name": "Рукоделие"}, "4": {"description": "Застрелить 100 монстров", "name": "Бей и беги"}, "5": {"description": "Застрелить 300 монстров", "name": "Хит-парад"}}, "7": {"1": {"description": "Использовать пропеллер/джетпак 3 раза за игру", "name": "Разгон на Луну"}, "2": {"description": "Использовать пропеллер/джетпак 5 раз за игру", "name": "Слишком много сока"}, "3": {"description": "Использовать пропеллер/джетпак 10 раз за игру", "name": "Усиленный до предела"}}, "8": {"1": {"description": "Убить 100 монстров", "name": "Первая кровь"}, "2": {"description": "Убить 500 монстров", "name": "Злой убийца"}, "3": {"description": "Убить 1 000 монстров", "name": "Каратель"}, "4": {"description": "Убить 5 000 монстров", "name": "Термина<PERSON>ор"}, "5": {"description": "Убить 10 000 монстров", "name": "Режим зверя включен"}}, "9": {"1": {"description": "Разбить 10 хрупких платформ в игре", "name": "Разрушитель платформ"}, "2": {"description": "Разбить 50 хрупких платформ в игре", "name": "Толстяк"}, "3": {"description": "Разбить 100 хрупких платформ в игре", "name": "Слишком рано приземлился"}, "4": {"description": "Разбить 300 хрупких платформ в игре", "name": "Я снова его сломал"}, "5": {"description": "Разбить 500 хрупких платформ в игре", "name": "Падающая звезда"}}, "10": {"1": {"description": "Пролететь с джетпаком 10 раз", "name": "Прогрев двигателя"}, "2": {"description": "Пролететь с джетпаком 50 раз", "name": "Пилот-Ас"}, "3": {"description": "Пролететь с джетпаком 200 раз", "name": "Взлетаем!"}, "4": {"description": "Пролететь с джетпаком 1 000 раз", "name": "Наркоман джетпака"}, "5": {"description": "Пролететь с джетпаком 2 500 раз", "name": "Зависимый от высоты"}}, "11": {"1": {"description": "Пролететь с пропеллером 10 раз", "name": "Фантастическая игра"}, "2": {"description": "Пролететь с пропеллером 50 раз", "name": "Оператор дрона"}, "3": {"description": "Пролететь с пропеллером 200 раз", "name": "Мастер ветроэнергетики"}, "4": {"description": "Пролететь с пропеллером 1 000 раз", "name": "Король воздушных потоков"}, "5": {"description": "Пролететь с пропеллером 2 500 раз", "name": "Пропеллер Про 2500"}}, "12": {"1": {"description": "Разблокировать 1 скин", "name": "Новий гардероб"}, "2": {"description": "Разблокировать 5 скинов", "name": "Скажите 'да' костюму"}, "3": {"description": "Разблокировать 10 скинов", "name": "На стиле"}, "4": {"description": "Разблокировать 20 скинов", "name": "Элитный коллекционер"}, "5": {"description": "Разблокировать 40 скинов", "name": "Бог стиля"}}, "13": {"1": {"description": "Об<PERSON>ий счет во всех играх: \n10 000", "name": "Счёт слабака"}, "2": {"description": "Общий счет во всех играх: \n100 000", "name": "Собрать их всех!"}, "3": {"description": "Общий счет во всех играх: \n1 000 000", "name": "Сон переоценен"}, "4": {"description": "Об<PERSON>ий счет во всех играх: \n10 000 000", "name": "Режим гринда активирован"}, "5": {"description": "Общий счет во всех играх: \n100 000 000", "name": "Forbes 100 до 100"}}, "14": {"1": {"description": "Прыгнуть 1 000 раз", "name": "Прыжки вокруг"}, "2": {"description": "Прыгнуть 10 000 раз", "name": "Чемпион по прыжкам"}, "3": {"description": "Прыгнуть 100 000 раз", "name": "Дотянуться до облаков"}, "4": {"description": "Прыгнуть 250 000 раз", "name": "Первоклассный прыгун"}, "5": {"description": "Прыгнуть 1 000 000 раз", "name": "Выше облаков"}}, "15": {"1": {"description": "Прыгнуть 10 раз на батуте", "name": "Новичок в отскоках"}, "2": {"description": "Прыгнуть 100 раз на батуте", "name": "Вверх, вниз, и снова"}, "3": {"description": "Прыгнуть 1 000 раз на батуте", "name": "Прыжок в неприятности"}, "4": {"description": "Прыгнуть 10 000 раз на батуте", "name": "Колени все еще подпрыгивают"}, "5": {"description": "Прыгнуть 100 000 раз на батуте", "name": "<PERSON>ас<PERSON><PERSON><PERSON> батутов"}}, "16": {"1": {"description": "Прыгнуть 10 раз на пружине", "name": "Новичок в пружинах"}, "2": {"description": "Прыгнуть 100 раз на пружине", "name": "Тошнота при прыжках"}, "3": {"description": "Прыгнуть 1 000 раз на пружине", "name": "Коленям хана"}, "4": {"description": "Прыгнуть 10 000 раз на пружине", "name": "Я больше так не могу."}, "5": {"description": "Прыгнуть 100 000 раз на пружине", "name": "Кому нужны батуты?"}}}, "tapToClaim": "Получить", "title": "Достижения"}, "soon": "Скоро", "portraitOrientationRequired": "Используйте портретную ориентацию для лучшего опыта", "loading": "Загрузка", "settings": {"title": "Настройки", "music": "Музыка", "sound": "Звук", "haptic": "Вибрация", "language": "Язык", "support": "Поддержка"}, "farming": {"start": "Начать фарминг", "farming": "<PERSON>ар<PERSON><PERSON><PERSON><PERSON>"}, "friends": {"title": "Друзья", "inviteFriends": "Пригласить друзей", "description": "Получай бонус за каждого друга!", "invitedCount": "Твои рефералы: {count}", "freeInviteDesc": "За друга", "premiumInviteDesc": "За друга с Premium", "emptyRefs": "Пригла<PERSON>ай друзей, чтобы получить\nсочные награды и играть вместе"}, "earn": {"title": "Миссии", "name": "Задания", "description": "Выполняй задания и получай награды", "timeToVerify": "Время проверки задания — 1 час", "startTasks": "Начальные задания", "dailyTasks": "Ежедневные задания", "completedMissions": "Завершенные задания", "tasksCompleted": "Миссий выполнено:", "missions": {"title": "Миссии", "partners": "Партнеры", "invite_5_friend": {"name": "Пригласи 5 друзей", "action": "Пригласить"}, "invite_3_friend": {"name": "Пригласи 3 друзей", "action": "Пригласить"}, "invite_1_friend": {"name": "Пригласи 1 друга", "action": "Пригласить"}, "connect_wallet": {"name": "Подключить TON кошелек", "action": "Подключить"}, "first_transaction": {"name": "Сделай первую TON транзакцию", "action": "Сделать транзакцию"}, "subscribe_main_channel": {"name": "Подпишись на основной канал", "action": "Подписаться"}, "use_booster": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> бустеров {goal}", "action": "Играть"}, "jump_to_score": {"name": "Набери счет за игру: {goal}", "action": "Играть"}, "kill_monster": {"name": "Убей {goal} мон<PERSON>тров", "action": "Играть"}, "invite_ref": {"name": "Пригласи друзей: {goal}", "action": "Пригласить"}, "play_game": {"name": "Сыг<PERSON><PERSON>й игр: {goal}", "action": "Играть"}, "catch_ticket": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {goal} Б<PERSON><PERSON><PERSON><PERSON>ов", "action": "Играть"}, "daily_total_jump": {"name": "Получи {goal} за все игры", "action": "Играть"}, "use_aimbot_booster": {"name": "Используй {goal} AIM Бот", "action": "Играть"}, "use_jumper_booster": {"name": "Используй {goal} Пружину", "action": "Играть"}, "use_magnet_booster": {"name": "Используй {goal} Магнит", "action": "Играть"}, "unlock_league": {"name": "Разблок<PERSON><PERSON><PERSON><PERSON> {goal} Лигу", "action": "Играть"}, "buy_skin": {"name": "Получи скин", "action": "К скинам"}, "use_revive": {"name": "Используй {goal} возрождений", "action": "Играть"}, "subscribe_x": {"name": "Подпишись на наш X", "action": "Подписаться"}, "subscribe_community_chat": {"name": "Присоединись к Uni Jump сообществу", "action": "Присоединиться"}, "add_to_home_screen": {"name": "Добавь Uni Jump на домашнюю страницу", "action": "Добавить"}, "purchase_in_shop_for_stars": {"name": "Купи на 100 телеграм звезд в магазине", "action": "Купить"}, "purchase_skin_for_stars": {"name": "Купи любой скин за звезды", "action": "Купить"}, "boost_telegram_channel": {"name": "Забусти наш Телеграм канал", "action": "Забустить"}, "go_to_miniapp_9": {"name": "Play Match-3, watch ads, and earn USDt!", "action": "Play MatchMoney"}, "go_to_miniapp_10": {"name": "Boinker: spin the slot and collect Artifacts", "action": "Join <PERSON>"}, "go_to_miniapp_15": {"name": "Play <PERSON>r to win $300", "action": "Play Miner"}, "go_to_miniapp_19": {"name": "Open chests — get USDT", "action": "Join Digger game"}, "go_to_miniapp_22": {"name": "Start merging cars today!", "action": "Join DRFT Party"}, "go_to_miniapp_23": {"name": "You Play - We Really Pay!", "action": "Join <PERSON>"}, "go_to_miniapp_24": {"name": "Play Outmine", "action": "Join <PERSON>"}, "go_to_miniapp_25": {"name": "Breed ducks to earn $EGG tokens!", "action": "Play <PERSON>yGram"}, "go_to_miniapp_26": {"name": "Launch Symptomify", "action": "Play Symptomify"}, "go_to_miniapp_27": {"name": "Play WORK DOGS", "action": "Join WORK DOGS"}, "go_to_miniapp_28": {"name": "Tap & Earn $HASH", "action": "Play HashCats"}, "go_to_miniapp_29": {"name": "Check in, open boxes & get $RICH", "action": "Join <PERSON>"}, "go_to_miniapp_30": {"name": "Play Simple Tap", "action": "Join Simple Tap"}, "go_to_miniapp_31": {"name": "Play Pookie & get TON boxes!", "action": "Play Pookie Cheese"}, "go_to_miniapp_32": {"name": "Mine TON in TonTower", "action": "Play TonTower"}, "go_to_miniapp_33": {"name": "Play Fasqon & eran FSQN tokens", "action": "<PERSON><PERSON>"}, "go_to_miniapp_34": {"name": "Play Capybara MEME", "action": "Join <PERSON>"}, "go_to_miniapp_35": {"name": "Launch 🚀 and withdraw $$$", "action": "Join Space Adventure"}, "go_to_miniapp_36": {"name": "Get Free Stars🌟", "action": "Join <PERSON>"}, "go_to_miniapp_37": {"name": "Join Daily Combo Updates", "action": "Join Daily Combo"}, "go_to_miniapp_38": {"name": "Play JustFab", "action": "Join <PERSON>"}, "go_to_miniapp_39": {"name": "Join T<PERSON>", "action": "Join T<PERSON>"}, "go_to_miniapp_40": {"name": "Play Pixiland & Claim $wPIXI now!", "action": "Join <PERSON>"}, "go_to_miniapp_41": {"name": "Play Biz Tycoon Now", "action": "Join <PERSON>  "}, "go_to_miniapp_42": {"name": "Join <PERSON><PERSON>", "action": "Join <PERSON><PERSON>"}, "go_to_miniapp_43": {"name": "Join <PERSON> and spin 1 slot", "action": "Join <PERSON>"}, "go_to_miniapp_44": {"name": "<PERSON><PERSON>", "action": "<PERSON><PERSON>"}}}, "shop": {"title": "Мага<PERSON>ин", "friendsDescription": "Покупай виртуального друга и \nполучай бонусы как за настоящего!", "bestDeal": "Выгода", "new": "НОВОЕ", "free": "БЕСПЛАТНО", "boxDescription": "Вы можете получить одну из этих наград", "nextFree": "Следующий бесплатный через"}, "airdrop": {"button": "Дроп", "title": "Airdrop", "tasks": {"info": "Выполняй задания, чтобы принять участие в Airdrop!", "connectWallet": "Подключите свой кошелек TON", "transaction": "Сделайте TON транзакцию"}, "ticketsBanner": "Билеты — единственный способ получить Airdrop, больше билетов - больше наград", "instructionSteps": {"1": "1. Подключите свой кошелек TON", "2": "2. Давайте сделаем вашу первую транзакцию, чтобы доказать, что вы готовы к Airdrop! Это стоит всего 0,5 ТОН."}, "instructionText": "Вы можете получить TON на свой кошелек на любой из следующих бирж:\n      Binance, Bybit, KuCoin, OKX или Bitget.", "comingSoon": "AIRDROP — СКОРО ПОЯВИТСЯ!"}, "wallet": {"title": "Кошелек", "assets": "Активы", "connectWallet": "Подключите кошелек для вывода средств", "selectAsset": "Выберите актив для вывода", "disconnectWalletAlert": "Вы уверены, что хотите отключить свой кошелек?\n\nБез подключенного кошелька вы не можете отправить запрос на снятие средств!", "history": "История", "details": {"title": "Детали", "amount": "Сумма", "fee": "Комиссия", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "status": "Статус", "lastUpdate": "Последнее обновление"}, "emptyHistory": "Истории пока нет", "withdraw": {"title": "Вывод", "yourWallet": "Твой кошелек", "amount": "Количество", "available": "Доступно {amount}", "minimum": "Минимум {amount} {currency}", "successMessage": "Транзакция была\nуспешно отправлена.\n\nВыполнение транзакции\nможет занять до 3 дней."}}, "leagues": {"title": "<PERSON>и<PERSON>и", "description": "Рейтинг зависит от баланса билетов", "league": "Лига", "blocker": "Достигните {{league}},\nщоб разблокировать {{feature}}"}, "playerProfile": {"title": "Профиль", "allTimeScore": "<PERSON><PERSON><PERSON><PERSON><PERSON> счет", "averageScore": "Средний счет", "gamesPlayed": "Сыграно игр"}, "reward": {"boxes": "Лутбоксы", "customCoin": "Г<PERSON>б<PERSON>а", "dynamicCoins_1": "Ракета", "dynamicCoins_2": "Ракушка", "fullLives": "Макс. жизни", "hard": "Звезды", "lives": "Жизни", "magicHorns": "Рога", "refs": "Друзья", "refsFake": "Друзья", "rewards": "Награды", "stackableAimbot": "<PERSON><PERSON>", "stackableJumper": "Пружина", "stackableMagneticField": "Маг<PERSON>ит", "tickets": "Билеты", "timeBoundAimbot": "<PERSON><PERSON>", "timeBoundJumper": "Пружина", "timeBoundMagneticField": "Маг<PERSON>ит", "title": "Награда", "ton": "ТОН", "unlimitedLives": "Безлимитные жизни", "wheelSpins": "Колесо Фортуны | Колесо", "boosters": "Бустеры", "youGot": "Вы получили", "puzzleCoins": "Фрагмент"}, "magnetFields": {"magnet": "МАГНИТ", "magnetic_field_1": "МАЛЫЙ", "magnetic_field_2": "БОЛЬШОЙ"}, "dailyRewards": {"title": "Ежедневные награды", "info": "Возвращайся завтра за новыми наградами!", "skinInfo": "Заходите каждый день, щоб получить {skin} Скин з бонусом {bonus}", "day": "День", "almostThere": "Еще немного", "youNeedRefs": "Вам нужно больше друзей, щоб разблокировать"}, "subscription": {"description": "Вы хорошо справляетесь!", "details": "Чтобы продолжить собирать TON \n— присоединяйтесь к нашему каналу"}, "achievementRewards": {"newAchievement": "Новое Достижение"}, "onepercent": {"description": "Для получения награды нужно иметь суммарный счет больше чем {targetScore}", "eventStartDescription": "Тебе нужно иметь суммарный счет\nбольше {targetScore} для награды", "eventBlockedDescription": "Пригласи хотя бы 1 друга, чтобы присоединиться к событию. Или купи виртуального в нашем магазине. Призовой фонд:", "eventEndDescription": "Событие завершилось!\nТы набрал в сумме {targetScore} баллов.\nТвоя награда:"}, "hotrecord": {"description": "Больше счет - больше награда {rewardType}!", "eventEndDescription": "Событие завершилось!\nТвой рекорд {highScore}.\nТвоя награда:", "letsGo": "Поехали!"}, "reviveWindow": {"title": "Продолжить?", "score": "Счет: {score}", "highScore": "Рекорд: {score}", "newHighscore": "Новый рекорд {score}!", "pointsLeftToHighscore": "Нужно всего {score} баллов\nдля нового рекорда!", "nextTONin": "Всего {distance} баллов чтобы\nпоймать TON!", "nextCustomCoinIn": "Всего {distance} баллов чтобы\nпоймать Монету!", "saveMe": "ЕЩЁ!", "maxRevive": "Макс Попыток", "freeRevive": "Бесплатно", "revive": " Играть", "end": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "customCoin": {"description": "Купите {box} и выиграйте скини для фарма {x} больше монет! Соревнуйтесь за {ton} и {stars}", "eventStartDescription": "Вам нужно иметь больший счет, чтобы получить награду", "boost": "До {x} бонуса монет"}, "lives": {"full": "<PERSON>а<PERSON><PERSON>", "in": "+{amount} через", "moreLives": "Больше жизней", "noMoreLives": "Нет жизней", "timeToNext": "Время до следующей жизни", "inviteFriendToGet": " Пригласи друга, чтобы получить полные жизни!", "inviteFriend": "Пригласить друга", "goToShop": "Перейти в магазин"}, "joinCommunity": "Присоединиться к сообществу", "state": {"yourScore": "Твой счет", "ticketsCollected": "Собрано билетов", "coinsCollected": "Собрано монет"}, "tonEvent": {"eventDescription": "Ежедневный фонд: {{limit}}.\nОбщий фонд событий: {{totalLimit}}.\nУвеличивайте пул, приглашая друзей.\nКаждые {{friends}} друга +{{bonus}}!", "eventLimitDescription": "ЕЖЕДНЕВНЫЙ ПУЛ:", "letsPlay": "Играть!", "nextPoolIn": "Следующий пул:", "eventNoTonAvailableDescription": "Упс... Дневной лимит ТОН достигнут.\n Попробуйте завтра!", "eventStartDescription": "Воспользуйтесь своим шансом поймать ТОН!", "ton": "ТОН"}, "tonLimit": {"description": "Вы собрали {ton}\n Чтобы собрать больше TON разблокируйте\n {league}"}, "skinForTon": {"description": "Эксклюзивный скин за {ton}. Отправьте транзакцию и получите скин Bored Uni."}, "reviveBanner": {"description": "Когда вы упадете, вы можете использовать Звезды, чтобы продолжить прыгать и собирать больше TON!"}, "fragmentOffer": {"description": "Разблокируйте всю картинку, чтобы получить главный приз"}, "deepDiveOffer": {"description": "Завершите, чтобы получить главный приз"}, "lootBoxOffer": {"description": "Откройте 5 боксов с лимитированными скинами Губки и выиграйте призы из пула 30 TON и 20 000 Звезд."}, "endsIn": "Конец через ", "nextFreeAfter": "Следующий бесплатный после:", "connection": {"title": "Соединение потеряно", "description": "Проверьте подключение к Интернету, чтобы продолжить игру"}, "longLoad": {"title": "Важно!", "shortDescription": "Загрузка длится дольше, чем ожидалось", "fullDescription": "Пожалуйста, убедите<PERSON><PERSON>, что у вас хорошее соединение с сетью, и подождите, пока все ресурсы не будут загружены"}, "errors": {"appVersion": "Пожалуйста, обновите Telegram до последней версии", "walletNotConnectedForTransaction": "Нужно подключить кошелек перед проведением транзакции"}, "warnings": {"inviteToCollect": "Пригласи больше друзей, чтобы получить"}, "contest": {"successCaption": "Поздравляем! Вы участвуете в розыгрыше!", "failCaption": "Вы не выполнили все задания. Пожалуйста, перечитайте условия розыгрыша и попробуйте еще раз!", "errorCaption": "Розыгрыш недоступен.", "task": {"tickets": "Соберите {value} билетов", "friends": "Пригласите {value} друзей", "multiplier": "Достигните множителя {value}", "skin": "Откройте скин '{value}'", "starsTotal": "Потрачено всего {value} звезд", "starsDuringContest": "Потрачено {value} звезд во время розыгрыша"}}, "clans": {"clans": "Кланы", "myClan": "<PERSON><PERSON><PERSON> клан", "title": "<PERSON><PERSON><PERSON><PERSON>", "topClans": "Лучшие кланы", "event": {"description": "Собирай больше билетов, чтобы получить больше TON", "name": "Войны кланов", "total_prize": "Об<PERSON>ий приз\n{ton}", "description_1": "Участвуйте в событиях кланов\nи выигрывайте до {ton}", "requirenment": "Пригласите больше друзей\nчтобы начать событие"}}, "clan_event": {"description": "Собирай больше билетов, чтобы получить больше TON"}, "instructions": {"hot_record": {"1": "Играйте в Uni Jump", "2": "Достигните<br/><span class=\"instruction__step-text_yellow\">МАКС</span> рекорда", "3": "Зарабатывайте звезды", "4": "<span class=\"instruction__step-text_blue\">1000</span> лучших игроков<br/>получат <span class=\"instruction__step-text_yellow\">звезды</span>"}, "one_percent": {"1": "Играйте в Uni Jump", "2": "Достигните <span class=\"instruction__step-text_yellow\">лучшего</span> общего<br/>счета", "3": "Зарабатывайте звезды"}, "ton_mining": {"1": "Играйте в Uni Jump", "2": "Собирайте TON", "3": "Используйте МАГНИТ для<br/>сбора <span class=\"instruction__step-text_yellow\">X3</span> TON"}, "custom_coin": {"1": "Получите эксклюзивные скины<br/>из лимитированных боксов", "2": "Играйте и собирайте монеты - каждый скин открывает лучший множитель монет!", "3": "Больше монет — выше ранг, лучше награды!"}, "leagues": {"1": "Улучшите<br/><span class=\"instruction__step-text_yellow\">X</span> множитель", "2": "Собирайте больше<br/>бил<PERSON><PERSON>ов", "3": "Разблокируйте новые <span class=\"instruction__step-text_yellow\">лиги</span>", "4": "Новые <span class=\"instruction__step-text_yellow\">лиги</span> разблокируют<br/>больше событий!"}, "ice_fragment": {"1": "Играйте в Uni Jump", "2": "Собирайте монеты", "3": "Получайте награды!"}, "clan_create": {"1": "Создайте<br/>группу Telegram", "2": "Добавьте бота <span class=\"instruction__step-text_yellow\">UniJump</span>", "3": "Дайте боту права администратора", "4": "Приглашайте друзей в группу<br/>и развивайте клан!"}, "clan_event": {"1": "Приглашайте друзей<br/>в клан", "2": "Достигните <span class=\"instruction__step-text_yellow\">500</span> участников<br/>3 лиги", "3": "Начните<br/>событие", "4": "Получите награду <span class=\"instruction__step-text_blue\">50 TON</span>!"}}, "hoursFull": "<PERSON>а<PERSON>ы", "days": "д", "hours": "ч", "minutes": "м", "seconds": "с", "linkCopied": "Скопировано", "error": "Ошибка", "claimed": "Получено", "canceled": "Отменено", "success": "Успешно", "pending": "В процессе", "processing": "Обработка", "minutesFull": "Минуты", "exploiters": {"collectDescription": "Хорошая работа! Ваш TON теперь в безопасности. Заберите его!", "count": "Убить {count} Экс<PERSON>л<PERSON><PERSON><PERSON>еров", "heist": "Ограбление", "lastChance": "ПОСЛЕДНИЙ ШАНС!", "lastChanceDescription": "Ой... У вас есть последний шанс вернуть TON! Попробуйте сейчас.", "task": "Убейте {count} за 1 игру", "ton": "ТОН", "welcomeDescription": "Внимание! Эксплойтеры украли ваш TON. Верните его!"}}
<script setup lang="ts">
import "@glidejs/glide/dist/css/glide.core.min.css";
import { onMounted, ref } from 'vue'
import { RouterView } from 'vue-router'

import MainLoadingScreen from '@/components/MainLoadingScreen.vue'
import PortraitOrientationAlert from '@/components/PortraitOrientationAlert.vue'
import FadeTransition from '@/components/animations/FadeTransition.vue'
import GameContainer from '@/components/game/GameContainer.vue'

import { initGame } from '@/game/entry'
import { logger } from './shared/Logger'

import VInstruction from '@/components/UI/VInstruction.vue'
import VNotification from '@/components/UI/VNotification.vue'
import VToast from '@/components/UI/VToast.vue'
import RewardScreen from '@/components/rewards/RewardScreen.vue'
import { useHeaderColor } from '@/composables/useHeaderColor'
import { useTelegramEvents } from '@/composables/useTelegramEvents'
import { useUser } from '@/composables/useUser'
import { usePlayerState } from '@/services/client/usePlayerState'
import { isTMA, retrieveLaunchParams } from '@telegram-apps/sdk'
import AchievementRewardScreen from './components/rewards/AchievementRewardScreen.vue'
import SkinRewardScreen from './components/rewards/SkinRewardScreen.vue'
import TonConnectProvider from './providers/TonConnectProvider.vue'

const isAuthReady = ref(false)
const { isLoading: isLoadingPlayerState } = usePlayerState(isAuthReady)

function waitForAuth(): Promise<void> {
  return new Promise(resolve => {
    if (__DEV__) resolve()
    const checkAuth = () => {
      if (window.__AUTH__ === true) {
        resolve()
      } else {
        setTimeout(checkAuth, 10)
      }
    }
    checkAuth()
  })
}

const {
  registerTgEvents,
  handleTgButtonDisplayState,
  registerAddedToHomeScreenEvent
} = useTelegramEvents()

const { setHeaderForApp } = useHeaderColor()

const { setUser } = useUser()

onMounted(async () => {
  waitForAuth().then(() => {
    isAuthReady.value = true
  })

  initGame()

  if (await isTMA()) {
    setHeaderForApp()
    registerTgEvents()
    handleTgButtonDisplayState()
    registerAddedToHomeScreenEvent()
    setUser()
    const launchParams = retrieveLaunchParams()
    if (__DEV__)
      logger.log('Launch Params', `retrieveLaunchParams: ${JSON.stringify(launchParams, null, 2)}`)
  }
})

const mainScreenLoaded = ref(false)
const onMainLoadingScreenClose = () => {
  mainScreenLoaded.value = true
}
</script>

<template>
  <TonConnectProvider id="app-view">
    <MainLoadingScreen :isLoading="isLoadingPlayerState" @close="onMainLoadingScreenClose" />
    <RouterView v-if="mainScreenLoaded && !isLoadingPlayerState" />
    <GameContainer />
    <FadeTransition />
    <PortraitOrientationAlert />
    <VToast />
    <VInstruction />
    <VNotification />
    <RewardScreen />
    <SkinRewardScreen />
    <AchievementRewardScreen />
  </TonConnectProvider>
</template>

<style lang="scss">
.banner {
  font-size: 14px;
  line-height: 19px;
  color: #1d3161;
  background-color: #dbeaff;
  display: flex;
  justify-content: center;
  align-items: center;
  white-space: pre;
  text-align: center;

  &__tickets {
    position: relative;
    width: 60px;
  }
}
</style>

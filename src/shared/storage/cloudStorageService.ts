import { localStorageService } from '@/shared/storage/localStorageService.ts'
import { cloudStorage } from '@telegram-apps/sdk'

export class CloudStorageService {
  isSupported(): boolean {
    return cloudStorage.isSupported()
  }

  async save(key: string, value: any): Promise<void> {
    try {
      if (!this.isSupported() || !cloudStorage.setItem.isAvailable()) {
        console.warn('Cloud storage is not supported on this device. Using local storage instead.')
        localStorageService.save(key, value)
        return
      }
      const jsonValue = JSON.stringify(value)
      await cloudStorage.setItem(key, jsonValue)
    } catch (error) {
      console.error(`Error saving data to cloud storage with key "${key}":`, error)
    }
  }

  async load<T>(key: string): Promise<T | null> {
    try {
      if (!this.isSupported() || !cloudStorage.getItem.isAvailable()) {
        console.warn('Cloud storage is not supported on this device. Using local storage instead.')
        return localStorageService.load(key)
      }

      const jsonResult = await cloudStorage.getItem(key)
      return jsonResult ? (JSON.parse(jsonResult) as T) : null
    } catch (error) {
      console.error(`Error loading data from cloud storage with key "${key}":`, error)
      return null
    }
  }

  async delete(key: string): Promise<void> {
    try {
      if (!this.isSupported() || !cloudStorage.deleteItem.isAvailable()) {
        console.warn('Cloud storage is not supported on this device. Using local storage instead.')
        localStorageService.delete(key)
        return
      }

      await cloudStorage.deleteItem(key)
    } catch (error) {
      console.error(`Error deleting data from cloud storage with key "${key}":`, error)
    }
  }

  async getKeys(): Promise<string[]> {
    if (!this.isSupported() || !cloudStorage.getKeys.isAvailable()) {
      console.warn('Cloud storage is not supported on this device. Using local storage instead.')
      return localStorageService.getKeys()
    }
    return await cloudStorage.getKeys()
  }
}

export const cloudStorageService = new CloudStorageService()

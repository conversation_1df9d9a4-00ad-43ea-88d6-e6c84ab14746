import GameView from '@/views/GameView.vue'
import HomeView from '@/views/HomeView.vue'
import MainView from '@/views/MainView.vue'
import MenuView from '@/views/MenuView.vue'
import BattleEventView from '@/views/menu-views/BattleEventView.vue'
import ClanEventView from '@/views/menu-views/ClanEventView.vue'
import ClanView from '@/views/menu-views/ClanView.vue'
import CustomCoinEventView from '@/views/menu-views/CustomCoinEventView.vue'
import DropView from '@/views/menu-views/DropView.vue'
import EarnView from '@/views/menu-views/EarnView.vue'
import FriendsView from '@/views/menu-views/FriendsView.vue'
import HotrecordEventView from '@/views/menu-views/HotrecordEventView.vue'
import JackpotView from '@/views/menu-views/JackpotView.vue'
import LeaguesView from '@/views/menu-views/LeaguesView.vue'
import MeView from '@/views/menu-views/MeView.vue'
import OnePercentEventView from '@/views/menu-views/OnePercentEventView.vue'
import ShopView from '@/views/menu-views/ShopView.vue'
import WheelSpinView from '@/views/menu-views/WheelSpinView.vue'
import CoinAnimationView from '@/views/test/CoinAnimationView.vue'
import ParticlesView from '@/views/test/ParticlesView.vue'
import SpineView from '@/views/test/SpineView.vue'
import TestView from '@/views/test/TestView.vue'
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: MainView,
      name: 'main',
      children: [
        {
          path: '',
          component: HomeView,
          name: 'home',
          children: [
            {
              path: 'onepercent-event',
              name: 'onepercent-event',
              component: OnePercentEventView
            },
            {
              path: 'hotrecord-event',
              name: 'hotrecord-event',
              component: HotrecordEventView
            },
            {
              path: 'clan-event',
              name: 'clan-event',
              component: ClanEventView
            },
            {
              path: 'custom-coin-event',
              name: 'custom-coin-event',
              component: CustomCoinEventView
            },
            {
              path: 'battle-event',
              name: 'battle-event',
              component: BattleEventView
            }
          ]
        },
        {
          path: 'menu',
          component: MenuView,
          children: [
            {
              path: 'friends',
              name: 'friends',
              component: FriendsView
            },
            {
              path: 'me',
              name: 'me',
              component: MeView
            },
            {
              path: 'drop',
              name: 'drop',
              component: DropView
            },
            {
              path: 'earn',
              name: 'earn',
              component: EarnView
            },
            {
              path: 'shop',
              name: 'shop',
              component: ShopView
            },
            {
              path: 'leagues',
              name: 'leagues',
              component: LeaguesView
            },
            {
              path: 'wheel-spin',
              name: 'wheel-spin',
              component: WheelSpinView
            },
            {
              path: 'clan',
              name: 'clan',
              component: ClanView
            },
            {
              path: 'jackpot',
              name: 'jackpot',
              component: JackpotView
            }
          ]
        }
      ]
    },
    {
      path: '/game',
      name: 'game',
      component: GameView
    },
    {
      path: '/test-page',
      name: 'test',
      component: TestView
    },
    {
      path: '/test-page/particles',
      name: 'particles',
      component: ParticlesView
    },
    {
      path: '/test-page/spine',
      name: 'spine',
      component: SpineView
    },
    {
      path: '/test-page/coin-animation',
      name: 'coin-animation',
      component: CoinAnimationView
    }
  ]
})

export default router

import './assets/main.scss'

import { VueQueryPlugin } from '@tanstack/vue-query'
import Particles from '@tsparticles/vue3'
import { loadFull } from 'tsparticles'
import { createApp } from 'vue'
import App from './App.vue'
import { i18n } from './plugins/i18n'
import { pinia } from './plugins/pinia'
import router from './router'

import { datadogLogs } from '@datadog/browser-logs'
import { datadogRum } from '@datadog/browser-rum'

import TelegramAnalytics from '@telegram-apps/analytics'

export function initApp() {
  datadogRum.init({
    applicationId: '82b0c764-81f2-4b80-bd1a-a0133fbef51e',
    clientToken: 'pub51fbdc9396f3eac513ebcce7eac4bd78',
    site: 'datadoghq.eu',
    service: 'uni',
    env: __ENV__,
    version: __VERSION__,
    sessionSampleRate: 100,
    sessionReplaySampleRate: 0,
    trackUserInteractions: true,
    trackResources: true,
    trackLongTasks: true,
    defaultPrivacyLevel: 'mask-user-input'
  })

  datadogLogs.init({
    clientToken: 'pub51fbdc9396f3eac513ebcce7eac4bd78',
    site: 'datadoghq.eu',
    forwardErrorsToLogs: true,
    sessionSampleRate: 100
  })

  if (__DEV__) {
    try {
      import('eruda').then(eruda => {
        eruda.default.init({
          useShadowDom: false,
          tool: ['console', 'elements', 'network', 'resources', 'info']
        })
        eruda.default.position({ x: 20, y: -200 })
      })
    } catch (e) {
      console.error(e)
    }
  }

  let token =
    'eyJhcHBfbmFtZSI6Im9yaW9uZ2FtZXNfZGV2IiwiYXBwX3VybCI6Imh0dHBzOi8vdC5tZS91bXBpbmdHYW1lc19kZXYyX2JvdCIsImFwcF9kb21haW4iOiJodHRwczovL3VuaWp1bXAtZGV2ZWxvcG1lbnQuazhzLm9yaW9uZ2FtZXMubG9sLyJ9!63BysGgg9J20ONojwRFzI6rF6L68S5EvFRjcHeTg5+U='
  let appName = 'oriongames_dev'

  if (!__DEV__) {
    token =
      'eyJhcHBfbmFtZSI6InVuaV9qdW1wXzEiLCJhcHBfdXJsIjoiaHR0cHM6Ly90Lm1lL3VuaWp1bXBfYm90IiwiYXBwX2RvbWFpbiI6Imh0dHBzOi8vdW5panVtcC54eXovIn0=!sqi8fcVFzkx5zgL3FFPtNMsKE5z6wJ3VBeEe4FtmRrI='
    appName = 'uni_jump_1'
  }

  TelegramAnalytics.init({
    token: token,
    appName: appName
  })

  const app = createApp(App)
  app.use(i18n)
  app.use(pinia)
  app.use(router)
  app.use(VueQueryPlugin)
  app.use(Particles, {
    init: async engine => {
      await loadFull(engine)
    }
  })

  return app
}

import { useCountdownTimer } from '@/composables/useCountdownTimer'
import { useNowTimestamp } from '@/services/client/useNowTimestamp.ts'
import { addToPlayerState, usePlayerState } from '@/services/client/usePlayerState.ts'
import { defineStore } from 'pinia'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

export const useWheelSpinStore = defineStore('wheelSpin', () => {
  const isFreeSpin = ref(true)

  const { playerState } = usePlayerState()
  const { updatePlayerState } = addToPlayerState()

  const { getNow } = useNowTimestamp()
  const { hours, minutes, seconds, initTimerWithTotal } = useCountdownTimer('wheelSpin', {
    onTimerEnd: () => {
      isFreeSpin.value = true
      updatePlayerState('wheelSpins', 1)
    }
  })

  const freeSpinAvailableAtTime = computed(() => {
    return playerState.value?.wheelSpins?.freeAvailableAt ?? 0
  })

  const availableWheelSpinsCount = computed(() => {
    return playerState.value?.wheelSpins?.amount ?? 0
  })

  const recalculateFreeSpinAvailableTime = async (availableAt: number) => {
    const now = await getNow()
    const timeLeft = Math.floor(availableAt - now)

    if (timeLeft > 0) {
      isFreeSpin.value = false
      initTimerWithTotal(timeLeft)
    } else {
      isFreeSpin.value = true
    }
  }

  watch(
    freeSpinAvailableAtTime,
    () => {
      recalculateFreeSpinAvailableTime(freeSpinAvailableAtTime.value)
    },
    { immediate: true }
  )

  onMounted(() => {
    window.addEventListener('visibilitychange', () => {
      recalculateFreeSpinAvailableTime(freeSpinAvailableAtTime.value)
    })
  })

  onUnmounted(() => {
    window.removeEventListener('visibilitychange', () => {
      recalculateFreeSpinAvailableTime(freeSpinAvailableAtTime.value)
    })
  })

  return { isFreeSpin, availableWheelSpinsCount, hours, minutes, seconds }
})

import { useCountdownTimer } from '@/composables/useCountdownTimer'
import { useNowTimestamp } from '@/services/client/useNowTimestamp'
import { usePlayerState } from '@/services/client/usePlayerState.ts'
import { defineStore } from 'pinia'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

export const useJackpotStore = defineStore('jackpotStore', () => {
  const route = useRoute()
  const router = useRouter()
  const { getNow } = useNowTimestamp()
  const { playerState, refetchPlayerState } = usePlayerState()

  const canPurchaseCoupons = ref(false)

  const isJackpotActive = computed(() => {
    return playerState.value?.jackpotEvent ?? false
  })

  const jackpotCouponsEndAt = computed(() => {
    return playerState.value?.jackpotEvent?.purchaseEndsAt ?? 0
  })

  const jackpotEndsAt = computed(() => {
    return playerState.value?.jackpotEvent?.endsAt ?? 0
  })

  const {
    countdown: countdownCoupons,
    days: daysCoupons,
    hours: hoursCoupons,
    minutes: minutesCoupons,
    seconds: secondsCoupons,
    initTimerWithTotal: initTimerWithTotalCoupons
  } = useCountdownTimer('jackpotEventCoupons', {
    onTimerEnd: async () => {
      if (!isJackpotActive.value) return
      if (route.name === 'jackpot') router.push('/')
      await refetchPlayerState()
      await recalculateTime()
    }
  })

  const { countdown, days, hours, minutes, seconds, initTimerWithTotal } = useCountdownTimer(
    'jackpotEventEnd',
    {
      onTimerEnd: async () => {
        canPurchaseCoupons.value = false
      }
    }
  )

  const recalculateTime = async () => {
    if (isJackpotActive.value === null) return
    const now = await getNow()
    const timeLeft = jackpotEndsAt.value - now
    if (timeLeft > 0) {
      initTimerWithTotal(timeLeft)
    }
    const timeLeftCoupons = jackpotCouponsEndAt.value - now
    canPurchaseCoupons.value = timeLeftCoupons > 0
    if (timeLeftCoupons > 0) {
      initTimerWithTotalCoupons(timeLeftCoupons)
    }
  }

  watch(
    () => isJackpotActive.value,
    async () => {
      if (!isJackpotActive.value) return
      await recalculateTime()
    },
    { immediate: true }
  )

  onMounted(() => {
    window.addEventListener('visibilitychange', async () => {
      await recalculateTime()
    })
  })

  onUnmounted(() => {
    window.removeEventListener('visibilitychange', async () => {
      await recalculateTime()
    })
  })

  return {
    isJackpotActive,
    canPurchaseCoupons,
    countdown,
    days,
    hours,
    minutes,
    seconds,
    countdownCoupons,
    daysCoupons,
    hoursCoupons,
    minutesCoupons,
    secondsCoupons
  }
})

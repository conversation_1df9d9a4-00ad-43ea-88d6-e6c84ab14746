import ducky from '@/assets/images/temp/big-icons/duckyBox.png'
import fight from '@/assets/images/temp/big-icons/fightBox.png'
import lucky from '@/assets/images/temp/big-icons/luckyBox.png'
import magic from '@/assets/images/temp/big-icons/magicBox.png'
import rainbow from '@/assets/images/temp/big-icons/rainbowBox.png'

import shineBlue from '@/assets/images/temp/big-icons/shine-blue.png'
import shineGreen from '@/assets/images/temp/big-icons/shine-green.png'
import shinePink from '@/assets/images/temp/big-icons/shine-pink.png'

import type { LootBoxType } from '@/services/openapi'

export const LOOTBOX_TYPE_TO_IMAGE: Record<LootBoxType, string> = {
  rainbowLootBox: rainbow,
  luckyLootBox: lucky,
  magicLootBox: magic,
  fightLootBox: fight,
  duckyLootBox: ducky
}

export const LOOTBOX_TYPE_TO_SHINE_IMAGE: Record<LootBoxType, string> = {
  rainbowLootBox: shinePink,
  luckyLootBox: shineGreen,
  magicLootBox: shineBlue,
  fightLootBox: shinePink,
  duckyLootBox: shineBlue
}

export const RAINBOW_LOOTBOX_REWARD_ID_TO_CHANCE: Record<string, number> = {
  common: 15,
  rare: 10,
  epic: 5,
  tickets: 35,
  boosters: 20,
  wheelSpins: 15
}

export const LUCKY_LOOTBOX_REWARD_ID_TO_CHANCE: Record<string, number> = {
  common: 15,
  rare: 14,
  epic: 5,
  tickets: 30,
  boosters: 20,
  wheelSpins: 16
}

export const MAGIC_LOOTBOX_REWARD_ID_TO_CHANCE: Record<string, number> = {
  common: 15,
  rare: 15,
  epic: 8,
  tickets: 25,
  boosters: 20,
  wheelSpins: 17
}

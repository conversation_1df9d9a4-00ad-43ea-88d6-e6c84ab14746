import {
  getCollectionOptions,
  getCollectionsOptions
} from '@/services/openapi/@tanstack/vue-query.gen'
import { useQuery } from '@tanstack/vue-query'
import { computed } from 'vue'

export function useSkinCollections() {
  const { data, isLoading } = useQuery({
    ...getCollectionsOptions()
  })

  const collections = computed(() => data.value?.list ?? [])

  return {
    isLoading,
    collections
  }
}

export function useSkinCollection(id: number) {
  const { data, isLoading } = useQuery({
    ...getCollectionOptions({
      path: {
        collectionId: id
      }
    })
  })

  const skins = computed(() => data.value?.list ?? [])

  return {
    isLoading,
    skins
  }
}

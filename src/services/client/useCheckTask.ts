import { useDefaultError<PERSON>and<PERSON> } from '@/composables/useErrorHandling'
import {
  checkGlobalTaskMutation,
  getGlobalTasksQueryKey
} from '@/services/openapi/@tanstack/vue-query.gen'
import type { PlayerTasksResponse } from '@/services/openapi/types.gen'
import { useMutation, useQueryClient } from '@tanstack/vue-query'

export function useCheckGlobalTask() {
  const queryClient = useQueryClient()

  const { mutateAsync } = useMutation({
    ...checkGlobalTaskMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: (data, variables) => {
      const taskId = variables.path.taskId
      const isDone = data.ok
      queryClient.setQueryData(getGlobalTasksQueryKey(), (oldData: PlayerTasksResponse) => {
        if (!oldData) return oldData
        const newTasks: PlayerTasksResponse['tasks'] = oldData.tasks.slice().map(task => {
          if (task.taskId === taskId) {
            return {
              ...task,
              completed: isDone
            }
          }
          return task
        })
        return {
          ...oldData,
          tasks: newTasks
        }
      })
    }
  })

  const checkGlobalTask = (id: number) => {
    return mutateAsync({ path: { taskId: id } }).then(data => data.ok)
  }

  return { checkGlobalTask }
}

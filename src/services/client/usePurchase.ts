import { useDefaultE<PERSON><PERSON><PERSON><PERSON><PERSON>, usePurchaseErrorHandling } from '@/composables/useErrorHandling'
import {
  getBattleEventBoostersQueryKey,
  getPlayerStateQueryKey,
  getShopItemsQueryKey,
  purchaseEventBoosterMutation,
  purchaseJackpotCouponsMutation,
  purchaseMutation,
  purchaseProgressiveMutation,
  purchasePuzzleMutation,
  purchaseRansomMutation
} from '@/services/openapi/@tanstack/vue-query.gen'
import type { InGamePrice } from '@/types'
import { QueryClient, useMutation, useQueryClient } from '@tanstack/vue-query'
import type { EventType, GetShopItemsResponse, PlayerStateResponse } from '../openapi'
import { updatePlayerStateDataProp } from './utils'

const updatePlayerStateBalance = (queryClient: QueryClient, price: InGamePrice) => {
  queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
    if (!oldData) return oldData
    const currency = price.currency
    const amount = price.amount
    const newData: PlayerStateResponse = {
      ...oldData,
      [currency]: updatePlayerStateDataProp(oldData[currency], -amount)
    }
    return newData
  })
}

export function usePurchase() {
  const queryClient = useQueryClient()
  let priceToSpend: InGamePrice | null = null

  const { mutateAsync, isPending } = useMutation({
    ...purchaseMutation(),
    onSuccess: () => {
      if (priceToSpend) {
        updatePlayerStateBalance(queryClient, priceToSpend)
      }
    },
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
      usePurchaseErrorHandling(error.error)
    }
  })

  const purchase = (itemId: number, price: InGamePrice) => {
    priceToSpend = price
    return mutateAsync({
      body: { itemId, currency: price.currency }
    })
  }

  return { purchase, isPending }
}

export function usePurchaseProgressive() {
  const queryClient = useQueryClient()
  let priceToSpend: InGamePrice | null = null

  const { mutateAsync, isPending } = useMutation({
    ...purchaseProgressiveMutation(),
    onSuccess: () => {
      if (priceToSpend) {
        updatePlayerStateBalance(queryClient, priceToSpend)
      }
    },
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
      usePurchaseErrorHandling(error.error)
    }
  })

  const purchase = (offerId: number, price: InGamePrice | null = null) => {
    priceToSpend = price
    return mutateAsync({
      body: { offerId }
    })
  }

  // This is not in onSuccess callback because of purchasing with TG invoice
  const onProgressivePurchased = (offerId: number) => {
    queryClient.setQueryData(getShopItemsQueryKey(), (oldData: GetShopItemsResponse) => {
      const newData = { ...oldData }
      if (!newData.progressiveOffers) return oldData
      const offer = newData.progressiveOffers.find(offer => offer.id === offerId)
      if (!offer) return oldData
      offer.items.sort((a, b) => a.idx - b.idx)
      const offerIndex = offer.items.findIndex(offer => offer.isAvailable)
      if (offerIndex < 0) return
      offer.items[offerIndex].isPurchased = true
      offer.items[offerIndex].isAvailable = false

      const hasNoMoreOffers = offer.items.every(offer => offer.isPurchased)
      if (hasNoMoreOffers) {
        queryClient.invalidateQueries({ queryKey: getShopItemsQueryKey() }).then(() => {
          const data = queryClient.getQueryData<GetShopItemsResponse>(getShopItemsQueryKey())
          if (
            !data?.progressiveOffers ||
            data.progressiveOffers.find(offer => offer.id === offerId)?.isCompleted
          ) {
            queryClient.setQueryData(
              getPlayerStateQueryKey(),
              (oldPlayerStateData: PlayerStateResponse) => {
                if (!oldPlayerStateData) return oldPlayerStateData
                const newPlayerStateData: PlayerStateResponse = {
                  ...oldPlayerStateData,
                  progressiveOffers: oldPlayerStateData.progressiveOffers.map(offer =>
                    offer.id === offerId ? { ...offer, isCompleted: true } : offer
                  )
                }
                return newPlayerStateData
              }
            )
          }
        })
      } else {
        offer.items[offerIndex + 1].isAvailable = true
      }

      return newData
    })
  }

  return { purchase, isPending, onProgressivePurchased }
}

export function usePurchasePuzzle() {
  const queryClient = useQueryClient()
  let priceToSpend: InGamePrice | null = null

  const { mutateAsync, isPending } = useMutation({
    ...purchasePuzzleMutation(),
    onSuccess: () => {
      if (priceToSpend) {
        updatePlayerStateBalance(queryClient, priceToSpend)
      }
    },
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
      usePurchaseErrorHandling(error.error)
    }
  })

  const purchase = (offerId: number, itemIndex: number, price: InGamePrice | null = null) => {
    priceToSpend = price
    return mutateAsync({
      body: { offerId, itemIndex }
    })
  }

  // This is not in onSuccess callback because of purchasing with TG invoice
  const onPuzzlePurchased = (offerId: number, itemIndex: number) => {
    queryClient.setQueryData(getShopItemsQueryKey(), (oldData: GetShopItemsResponse) => {
      const newData = { ...oldData }
      if (!newData.puzzleOffers) return oldData
      const offer = newData.puzzleOffers.find(offer => offer.id === offerId)
      if (!offer) return oldData
      const offerIndex = offer.items.findIndex(offer => offer.idx === itemIndex)
      if (offerIndex < 0) return
      offer.items[offerIndex].isPurchased = true

      return newData
    })
  }

  const getNextPuzzle = (offerId: number) => {
    const data = queryClient.getQueryData<GetShopItemsResponse>(getShopItemsQueryKey())
    if (!data) return
    const offer = data.puzzleOffers.find(offer => offer.id === offerId)
    if (!offer) return

    const hasNoMoreOffers = offer.items.every(offer => offer.isPurchased)
    if (hasNoMoreOffers) {
      queryClient.invalidateQueries({ queryKey: getShopItemsQueryKey() }).then(() => {
        const data = queryClient.getQueryData<GetShopItemsResponse>(getShopItemsQueryKey())
        if (
          !data?.puzzleOffers ||
          data.puzzleOffers.find(offer => offer.id === offerId)?.isCompleted
        ) {
          queryClient.setQueryData(
            getPlayerStateQueryKey(),
            (oldPlayerStateData: PlayerStateResponse) => {
              if (!oldPlayerStateData) return oldPlayerStateData
              const newPlayerStateData: PlayerStateResponse = {
                ...oldPlayerStateData,
                puzzleOffers: oldPlayerStateData.puzzleOffers.map(offer =>
                  offer.id === offerId ? { ...offer, isCompleted: true } : offer
                )
              }
              return newPlayerStateData
            }
          )
        }
      })
    }
  }

  return { purchase, isPending, onPuzzlePurchased, getNextPuzzle }
}

export function usePurchaseRansom() {
  const queryClient = useQueryClient()
  let priceToSpend: InGamePrice | null = null

  const { mutateAsync, isPending } = useMutation({
    ...purchaseRansomMutation(),
    onSuccess: () => {
      if (priceToSpend) {
        updatePlayerStateBalance(queryClient, priceToSpend)
      }
    },
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
      usePurchaseErrorHandling(error.error)
    }
  })

  const purchaseRansom = (price: InGamePrice) => {
    priceToSpend = price
    return mutateAsync({})
  }

  return { purchaseRansom, isPending }
}

export function usePurchaseEventBoost() {
  const queryClient = useQueryClient()
  let priceToSpend: InGamePrice | null = null

  const { mutateAsync, isPending } = useMutation({
    ...purchaseEventBoosterMutation(),
    onSuccess: () => {
      if (priceToSpend) {
        updatePlayerStateBalance(queryClient, priceToSpend)
      }
      onEventBoostPurchased()
    },
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
      usePurchaseErrorHandling(error.error)
    }
  })

  // for purchase with TG invoice
  const onEventBoostPurchased = () => {
    queryClient.invalidateQueries({ queryKey: getBattleEventBoostersQueryKey() })
  }

  const purchase = (boosterId: number, event: EventType, price: InGamePrice | null = null) => {
    priceToSpend = price
    return mutateAsync({
      body: { boosterId, event }
    })
  }

  return { purchase, onEventBoostPurchased, isPending }
}

export function usePurchaseJackpotCoupon() {
  const queryClient = useQueryClient()
  let priceToSpend: InGamePrice | null = null

  const { mutateAsync, isPending } = useMutation({
    ...purchaseJackpotCouponsMutation(),
    onSuccess: (_, variables) => {
      if (priceToSpend) {
        updatePlayerStateBalance(queryClient, priceToSpend)
      }
      const couponTier = variables.body.tier
      queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
        if (!oldData || !oldData.jackpotEvent) return oldData
        const newData: PlayerStateResponse = {
          ...oldData,
          jackpotEvent: {
            ...oldData.jackpotEvent,
            couponTiers: oldData.jackpotEvent.couponTiers.map(el => ({
              ...el,
              isPurchased: el.tier === couponTier || el.isPurchased
            }))
          }
        }
        return newData
      })
    },
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
      usePurchaseErrorHandling(error.error)
    }
  })

  const purchase = (tier: number, price: InGamePrice | null = null) => {
    priceToSpend = price
    return mutateAsync({
      body: { tier }
    })
  }

  return { purchase, isPending }
}

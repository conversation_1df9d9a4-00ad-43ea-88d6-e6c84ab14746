import { useDefaultErrorHand<PERSON> } from '@/composables/useErrorHandling'
import {
  claimWithdrawTaskMutation,
  getAssetsQueryKey
} from '@/services/openapi/@tanstack/vue-query.gen'
import type { GetAssetsResponse } from '@/services/openapi/types.gen'
import { useMutation, useQueryClient } from '@tanstack/vue-query'

export function useClaimWithdrawTask() {
  const queryClient = useQueryClient()

  const { mutateAsync } = useMutation({
    ...claimWithdrawTaskMutation(),
    onError: () => {
      useDefaultErrorHandler('')
    },
    onSuccess: (_, variables) => {
      const taskId = variables.body.taskId
      queryClient.setQueryData(getAssetsQueryKey(), (oldData: GetAssetsResponse) => {
        if (!oldData) return oldData
        const newTasks: GetAssetsResponse['tasks'] = oldData.tasks.slice().map(task => {
          if (task.id === taskId) {
            return {
              ...task,
              isClaimed: true
            }
          }
          return task
        })
        return {
          ...oldData,
          tasks: newTasks
        }
      })
    }
  })

  const claimWithdrawTask = (id: number) => {
    return mutateAsync({
      body: { taskId: id }
    })
  }

  return { claimWithdrawTask }
}

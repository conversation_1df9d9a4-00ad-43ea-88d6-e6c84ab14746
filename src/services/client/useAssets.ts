import { useDefaultError<PERSON>and<PERSON> } from '@/composables/useErrorHandling'
import { getCurrencyRealAmount } from '@/constants/currency'
import {
  cancelWithdrawMutation,
  createWithdrawMutation,
  getAssetsOptions,
  getAssetsQueryKey,
  getTransactionsHistoryOptions,
  getWithdrawHistoryOptions,
  getWithdrawHistoryQueryKey
} from '@/services/openapi/@tanstack/vue-query.gen'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { computed, type Ref } from 'vue'
import type { GetWithdrawHistoryResponse } from '../openapi'

export function useAssetsList() {
  const { data, isLoading } = useQuery({
    ...getAssetsOptions()
  })

  const assets = computed(() => data.value?.assets ?? [])
  const balance = computed(() => data.value?.usdTotal ?? 0)
  const tasks = computed(() => {
    return (
      data.value?.tasks.map(task => {
        return {
          ...task,
          isDone: task.currentValue >= task.requiredValue,
          currentValue:
            task.name === 'ton_amount'
              ? Math.round(getCurrencyRealAmount(task.currentValue, 'ton') * 100) / 100
              : task.currentValue,
          requiredValue:
            task.name === 'ton_amount'
              ? getCurrencyRealAmount(task.requiredValue, 'ton')
              : task.requiredValue
        }
      }) ?? []
    )
  })
  const areTasksCompleted = computed(() => {
    return tasks.value.every(task => task.isDone && task.isClaimed)
  })
  const tasksLeft = computed(() => {
    return tasks.value.filter(task => !task.isDone).length
  })

  return {
    isLoading,
    assets,
    balance,
    tasks,
    tasksLeft,
    areTasksCompleted
  }
}

export function useWithdrawHistory(enabled: Ref<boolean>) {
  const { data, isLoading } = useQuery({
    ...getWithdrawHistoryOptions(),
    enabled
  })

  const transactions = computed(() => data.value?.list ?? [])

  return {
    isLoading,
    transactions
  }
}

export function useTransactionHistory(enabled: Ref<boolean>) {
  const { data, isLoading } = useQuery({
    ...getTransactionsHistoryOptions(),
    enabled
  })

  const transactions = computed(() => data.value?.list ?? [])

  return {
    isLoading,
    transactions
  }
}

export function useCancelTransaction() {
  const queryClient = useQueryClient()

  const { isPending, mutateAsync } = useMutation({
    ...cancelWithdrawMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: (_, variables) => {
      const transactionId = variables.body.withdrawId
      queryClient.setQueryData(
        getWithdrawHistoryQueryKey(),
        (oldData: GetWithdrawHistoryResponse) => {
          if (!oldData) return oldData
          const newTransactions = oldData.list.slice().map(t => {
            if (t.id === transactionId) {
              return {
                ...t,
                status: 'canceled'
              }
            }
            return t
          })
          return {
            list: newTransactions
          }
        }
      )
      queryClient.invalidateQueries({ queryKey: getAssetsQueryKey() })
    }
  })

  const cancelTransaction = (id: string) => {
    return mutateAsync({
      body: {
        withdrawId: id
      }
    })
  }

  return { cancelTransaction, isPending }
}

export function useMakeTransaction(onSuccess?: Function) {
  const queryClient = useQueryClient()

  const { isPending, mutateAsync } = useMutation({
    ...createWithdrawMutation(),
    onError: (error: any) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: () => {
      onSuccess && onSuccess()
      queryClient.invalidateQueries({ queryKey: getAssetsQueryKey() })
    }
  })

  const makeTransaction = (currency: string, amount: number) => {
    return mutateAsync({
      body: {
        currency,
        amount
      }
    })
  }

  return { makeTransaction, isPending }
}

import { useDefaultErrorHandler } from '@/composables/useErrorHandling'
import {
  getFortuneWheelConfigOptions,
  spinWheelMutation
} from '@/services/openapi/@tanstack/vue-query.gen'
import { useMutation, useQuery } from '@tanstack/vue-query'
import { computed } from 'vue'

export function useWheelSpinConfig() {
  const { data, isLoading } = useQuery({
    ...getFortuneWheelConfigOptions()
  })

  const sectors = computed(() => data.value?.sectors ?? [])

  return {
    sectors,
    isLoading
  }
}

export function useGetWheelSpin() {
  const { mutateAsync, isPending } = useMutation({
    ...spinWheelMutation(),
    retry: 3,
    retryDelay: 1000,
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    }
  })

  const getWheelSpin = async () => {
    return mutateAsync({
      body: {}
    }).then(data => {
      return data
    })
  }

  return { getWheelSpin, isPending }
}

import { useDefaultErrorHandler } from '@/composables/useErrorHandling'
import {
  claimLeagueRewardMutation,
  getPlayerStateQueryKey
} from '@/services/openapi/@tanstack/vue-query.gen'
// import { isTimeRewardType } from '@/types'
import { useMutation, useQueryClient } from '@tanstack/vue-query'
import type { PlayerStateResponse } from '../openapi'
// import { updatePlayerStateDataProp } from './utils'

export function useClaimLeagueReward() {
  const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    ...claimLeagueRewardMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: () => {
      queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
        if (!oldData) return oldData
        const newData: PlayerStateResponse = {
          ...oldData,
          leaguesToClaim: oldData.leaguesToClaim?.filter((_, index) => index !== 0)
        }
        return newData
      })
    }
    /*  onSuccess: data => {
      const isTimeReward = data.rewards.some(reward => isTimeRewardType(reward.type))
      if (isTimeReward) {
        queryClient.invalidateQueries({ queryKey: getPlayerStateQueryKey() })
      } else {
        queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
          if (!oldData) return oldData
          const hardReward = data.rewards.find(reward => reward.type === 'hard')?.value ?? 0
          const isFullLives = data.rewards.find(reward => reward.type === 'fullLives') !== undefined
          const newData: PlayerStateResponse = {
            ...oldData,
            hard: updatePlayerStateDataProp(oldData.hard, hardReward),
            lives: isFullLives ? oldData.livesMax : oldData.lives,
            leaguesToClaim: oldData.leaguesToClaim?.filter((_, index) => index !== 0)
          }
          return newData
        })
      }
    }*/
  })

  const claimLeagueReward = (league: number) => {
    return mutateAsync({ path: { league_id: league } })
  }

  return { claimLeagueReward, isClaimingLeagueReward: isPending }
}

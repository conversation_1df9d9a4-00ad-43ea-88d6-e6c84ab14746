import { useDefaultErrorHandler } from '@/composables/useErrorHandling'
import type { GetCollectionsResponse } from '@/services/openapi'
import {
  claimCollectionMutation,
  getCollectionsQueryKey
} from '@/services/openapi/@tanstack/vue-query.gen'
import { useMutation, useQueryClient } from '@tanstack/vue-query'

export function useClaimCollectionReward() {
  const queryClient = useQueryClient()

  const { mutateAsync } = useMutation({
    ...claimCollectionMutation(),
    onError: () => {
      useDefaultErrorHandler('')
    },
    onSuccess: (_, variables) => {
      const collectionId = variables.path.collectionId
      queryClient.setQueryData(getCollectionsQueryKey(), (oldData: GetCollectionsResponse) => {
        if (!oldData) return oldData
        const newCollections = oldData.list.slice().map(collection => {
          if (collection.id === collectionId) {
            return { ...collection, isClaimed: true }
          }
          return collection
        })
        return {
          ...oldData,
          list: newCollections
        }
      })
    }
  })

  const claimCollectionReward = (id: number) => {
    return mutateAsync({
      path: { collectionId: id }
    })
  }

  return { claimCollectionReward }
}

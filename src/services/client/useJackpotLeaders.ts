import { getJackpotLeaderboard } from '@/services/openapi'
import { useInfiniteQuery } from '@tanstack/vue-query'
import { computed, ref } from 'vue'

export function getJackpotLeadersKey() {
  return ['getJackpotLeadersKey']
}

export function useJackpotLeaders() {
  const isUserInLeaders = ref(false)
  const { data, hasNextPage, fetchNextPage, isFetching } = useInfiniteQuery({
    queryKey: getJackpotLeadersKey(),
    queryFn: async ({ pageParam = '' }) => {
      const response = await getJackpotLeaderboard({
        query: { cursor: pageParam }
      })
      if (!isUserInLeaders.value && response.data) {
        isUserInLeaders.value =
          response.data.list.find(leader => leader.id === response.data.me?.id) !== undefined
      }
      return response
    },
    getNextPageParam: lastPage => lastPage.data?.cursor ?? undefined,
    initialPageParam: '',
    enabled: true,
    staleTime: 0,
    gcTime: 0
  })

  const jackpotLeaders = computed(() => {
    return data.value ? data.value.pages.flatMap(page => page.data?.list ?? []) : []
  })

  const userInfo = computed(() => {
    return data.value?.pages[0].data?.me ?? null
  })

  return {
    isFetching,
    jackpotLeaders,
    userInfo,
    isUserInLeaders,
    hasNextPage,
    fetchNextPage
  }
}

// This file is auto-generated by @hey-api/openapi-ts

import { createClient, createConfig, type Options } from '@hey-api/client-fetch'
import type {
  CancelWithdrawData,
  CancelWithdrawError,
  CancelWithdrawResponse2,
  CheckGlobalTaskData,
  CheckGlobalTaskError,
  CheckGlobalTaskResponse2,
  CheckTonInvoiceData,
  CheckTonInvoiceError,
  CheckTonInvoiceResponse2,
  ClaimAchievementData,
  ClaimAchievementError,
  ClaimAchievementResponse2,
  ClaimAllCollectionsError,
  ClaimAllCollectionsResponse,
  ClaimBattleEventBoosterData,
  ClaimBattleEventBoosterError,
  ClaimBattleEventBoosterResponse2,
  ClaimCollectionData,
  ClaimCollectionError,
  ClaimCollectionResponse,
  ClaimDailyRewardData,
  ClaimDailyRewardError,
  ClaimDailyRewardResponse2,
  ClaimDailyTasksData,
  ClaimDailyTasksError,
  ClaimDailyTasksLootboxError,
  ClaimDailyTasksLootboxResponse,
  ClaimDailyTasksResponse,
  ClaimFarmingData,
  ClaimFarmingError,
  ClaimFarmingResponse2,
  ClaimGlobalTaskData,
  ClaimGlobalTaskError,
  ClaimGlobalTaskResponse2,
  ClaimLeagueRewardData,
  ClaimLeagueRewardError,
  ClaimLeagueRewardResponse2,
  ClaimLockedBalanceError,
  ClaimLockedBalanceResponse,
  ClaimReferralRewardData,
  ClaimReferralRewardError,
  ClaimReferralRewardResponse2,
  ClaimSkinData,
  ClaimSkinError,
  ClaimSkinResponse2,
  ClaimWithdrawTaskData,
  ClaimWithdrawTaskError,
  ClaimWithdrawTaskResponse,
  ClansRatingError,
  ClansRatingResponse2,
  CompleteGlobalTaskData,
  CompleteGlobalTaskError,
  CompleteGlobalTaskResponse2,
  ConnectWalletData,
  ConnectWalletError,
  ConnectWalletResponse2,
  ConvertCustomCoinsData,
  ConvertCustomCoinsError,
  ConvertCustomCoinsResponse2,
  CreateGameplaySessionOctetData,
  CreateGameplaySessionOctetError,
  CreateGameplaySessionOctetResponse,
  CreateInvoiceData,
  CreateInvoiceError,
  CreateInvoiceResponse2,
  CreateReferralLinkData,
  CreateReferralLinkError,
  CreateReferralLinkResponse2,
  CreateReviveInvoiceByStarsData,
  CreateReviveInvoiceByStarsError,
  CreateReviveInvoiceByStarsResponse2,
  CreateWithdrawData,
  CreateWithdrawError,
  CreateWithdrawResponse2,
  DenyWriteOffRansomError,
  DenyWriteOffRansomResponse,
  DisconnectWalletData,
  DisconnectWalletError,
  DisconnectWalletResponse2,
  GetAchievementsListError,
  GetAchievementsListResponse,
  GetAchievementsStateError,
  GetAchievementsStateResponse,
  GetAssetsError,
  GetAssetsResponse,
  GetBattleEventBoostersError,
  GetBattleEventBoostersResponse,
  GetBattleEventLeadersError,
  GetBattleEventLeadersResponse,
  GetBattleEventUserInfoError,
  GetBattleEventUserInfoResponse,
  GetClanEventLeadersError,
  GetClanEventLeadersResponse,
  GetClanEventUserInfoError,
  GetClanEventUserInfoResponse,
  GetCollectionData,
  GetCollectionError,
  GetCollectionResponse,
  GetCollectionsError,
  GetCollectionsResponse,
  GetContestInfoData,
  GetContestInfoError,
  GetContestInfoResponse,
  GetCustomCoinEventUserInfoError,
  GetCustomCoinEventUserInfoResponse,
  GetCustomCoinsLeadersError,
  GetCustomCoinsLeadersResponse,
  GetDailyTasksError,
  GetDailyTasksResponse,
  GetFortuneWheelConfigError,
  GetFortuneWheelConfigResponse,
  GetFreeLootboxError,
  GetFreeLootboxResponse,
  GetGlobalTasksError,
  GetGlobalTasksResponse,
  GetHotrecordLeadersError,
  GetHotrecordLeadersResponse,
  GetHotrecordUserInfoError,
  GetHotrecordUserInfoResponse,
  GetJackpotLeaderboardData,
  GetJackpotLeaderboardError,
  GetJackpotLeaderboardResponse,
  GetLeagueListError,
  GetLeagueListResponse,
  GetLeaguesLeadersData,
  GetLeaguesLeadersError,
  GetLeaguesLeadersResponse,
  GetLeaguesUserInfoError,
  GetLeaguesUserInfoResponse,
  GetOnepercentLeadersError,
  GetOnepercentLeadersResponse,
  GetOnepercentUserInfoError,
  GetOnepercentUserInfoResponse,
  GetPlayerProfileError,
  GetPlayerProfileResponse,
  GetPlayerStateError,
  GetPlayerStateResponse,
  GetReferralsListData,
  GetReferralsListError,
  GetReferralsListResponse,
  GetReviveInfoData,
  GetReviveInfoError,
  GetReviveInfoResponse,
  GetShopItemsError,
  GetShopItemsResponse,
  GetSkinsError,
  GetSkinsResponse,
  GetTransactionsHistoryError,
  GetTransactionsHistoryResponse,
  GetUserProfileData,
  GetUserProfileError,
  GetUserProfileResponse,
  GetUtcError,
  GetUtcResponse,
  GetWithdrawHistoryError,
  GetWithdrawHistoryResponse,
  LoginData,
  LoginError,
  LoginResponse2,
  OpenLootboxData,
  OpenLootboxError,
  OpenLootboxResponse,
  PurchaseData,
  PurchaseError,
  PurchaseEventBoosterData,
  PurchaseEventBoosterError,
  PurchaseEventBoosterResponse2,
  PurchaseJackpotCouponsData,
  PurchaseJackpotCouponsError,
  PurchaseJackpotCouponsResponse2,
  PurchaseProgressiveData,
  PurchaseProgressiveError,
  PurchaseProgressiveResponse,
  PurchasePuzzleData,
  PurchasePuzzleError,
  PurchasePuzzleResponse,
  PurchaseRansomError,
  PurchaseRansomResponse,
  PurchaseResponse2,
  PurchaseReviveData,
  PurchaseReviveError,
  PurchaseReviveResponse2,
  PurchaseSkinData,
  PurchaseSkinError,
  PurchaseSkinResponse2,
  RemovePlayerFlagData,
  RemovePlayerFlagError,
  RemovePlayerFlagResponse2,
  SelectSkinData,
  SelectSkinError,
  SelectSkinResponse2,
  SpinWheelError,
  SpinWheelResponse,
  StartClanEventError,
  StartClanEventResponse2,
  StartFarmingData,
  StartFarmingError,
  StartFarmingResponse2,
  UpdateGameplaySessionOctetData,
  UpdateGameplaySessionOctetError,
  UpdateGameplaySessionOctetResponse,
  UsersInClanRatingData,
  UsersInClanRatingError,
  UsersInClanRatingResponse
} from './types.gen'

export const client = createClient(createConfig())

export const claimAchievement = <ThrowOnError extends boolean = false>(
  options: Options<ClaimAchievementData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    ClaimAchievementResponse2,
    ClaimAchievementError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/achievements/claim'
  })
}

export const getAchievementsList = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetAchievementsListResponse,
    GetAchievementsListError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/achievements/list'
  })
}

export const getAchievementsState = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetAchievementsStateResponse,
    GetAchievementsStateError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/achievements/state'
  })
}

export const login = <ThrowOnError extends boolean = false>(
  options?: Options<LoginData, ThrowOnError>
) => {
  return (options?.client ?? client).get<LoginResponse2, LoginError, ThrowOnError>({
    ...options,
    url: '/api/v1/auth/login'
  })
}

export const clansRating = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<ClansRatingResponse2, ClansRatingError, ThrowOnError>({
    ...options,
    url: '/api/v1/clans/rating'
  })
}

export const usersInClanRating = <ThrowOnError extends boolean = false>(
  options: Options<UsersInClanRatingData, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    UsersInClanRatingResponse,
    UsersInClanRatingError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/clans/rating/{clan_id}'
  })
}

export const getContestInfo = <ThrowOnError extends boolean = false>(
  options: Options<GetContestInfoData, ThrowOnError>
) => {
  return (options?.client ?? client).get<GetContestInfoResponse, GetContestInfoError, ThrowOnError>(
    {
      ...options,
      url: '/api/v1/contest/{contest_id}'
    }
  )
}

export const getBattleEventBoosters = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetBattleEventBoostersResponse,
    GetBattleEventBoostersError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/events/battle/boosters'
  })
}

export const claimBattleEventBooster = <ThrowOnError extends boolean = false>(
  options: Options<ClaimBattleEventBoosterData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    ClaimBattleEventBoosterResponse2,
    ClaimBattleEventBoosterError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/events/battle/boosters/claim'
  })
}

export const getBattleEventLeaders = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetBattleEventLeadersResponse,
    GetBattleEventLeadersError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/events/battle/leaders'
  })
}

export const getBattleEventUserInfo = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetBattleEventUserInfoResponse,
    GetBattleEventUserInfoError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/events/battle/me'
  })
}

export const getClanEventLeaders = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetClanEventLeadersResponse,
    GetClanEventLeadersError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/events/clan/leaders'
  })
}

export const getClanEventUserInfo = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetClanEventUserInfoResponse,
    GetClanEventUserInfoError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/events/clan/me'
  })
}

export const startClanEvent = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    StartClanEventResponse2,
    StartClanEventError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/events/clan/start'
  })
}

export const convertCustomCoins = <ThrowOnError extends boolean = false>(
  options: Options<ConvertCustomCoinsData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    ConvertCustomCoinsResponse2,
    ConvertCustomCoinsError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/events/custom-coin/convert'
  })
}

export const getCustomCoinsLeaders = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetCustomCoinsLeadersResponse,
    GetCustomCoinsLeadersError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/events/custom-coin/leaders'
  })
}

export const getCustomCoinEventUserInfo = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetCustomCoinEventUserInfoResponse,
    GetCustomCoinEventUserInfoError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/events/custom-coin/me'
  })
}

export const getHotrecordLeaders = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetHotrecordLeadersResponse,
    GetHotrecordLeadersError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/events/hotrecord/leaders'
  })
}

export const getHotrecordUserInfo = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetHotrecordUserInfoResponse,
    GetHotrecordUserInfoError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/events/hotrecord/me'
  })
}

export const getJackpotLeaderboard = <ThrowOnError extends boolean = false>(
  options?: Options<GetJackpotLeaderboardData, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetJackpotLeaderboardResponse,
    GetJackpotLeaderboardError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/events/jackpot/leaderboard'
  })
}

export const getOnepercentLeaders = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetOnepercentLeadersResponse,
    GetOnepercentLeadersError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/events/onepercent/leaders'
  })
}

export const getOnepercentUserInfo = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetOnepercentUserInfoResponse,
    GetOnepercentUserInfoError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/events/onepercent/me'
  })
}

export const claimFarming = <ThrowOnError extends boolean = false>(
  options: Options<ClaimFarmingData, ThrowOnError>
) => {
  return (options?.client ?? client).post<ClaimFarmingResponse2, ClaimFarmingError, ThrowOnError>({
    ...options,
    url: '/api/v1/farming/claim'
  })
}

export const startFarming = <ThrowOnError extends boolean = false>(
  options: Options<StartFarmingData, ThrowOnError>
) => {
  return (options?.client ?? client).post<StartFarmingResponse2, StartFarmingError, ThrowOnError>({
    ...options,
    url: '/api/v1/farming/start'
  })
}

export const getFortuneWheelConfig = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetFortuneWheelConfigResponse,
    GetFortuneWheelConfigError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/fortune-wheel/config'
  })
}

export const spinWheel = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).post<SpinWheelResponse, SpinWheelError, ThrowOnError>({
    ...options,
    url: '/api/v1/fortune-wheel/spin'
  })
}

export const claimLockedBalance = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    ClaimLockedBalanceResponse,
    ClaimLockedBalanceError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/gameplay/heist/claim'
  })
}

export const denyWriteOffRansom = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    DenyWriteOffRansomResponse,
    DenyWriteOffRansomError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/gameplay/heist/deny'
  })
}

export const createReviveInvoiceByStars = <ThrowOnError extends boolean = false>(
  options: Options<CreateReviveInvoiceByStarsData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    CreateReviveInvoiceByStarsResponse2,
    CreateReviveInvoiceByStarsError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/gameplay/revive/create_revive_invoice_by_stars'
  })
}

export const getReviveInfo = <ThrowOnError extends boolean = false>(
  options: Options<GetReviveInfoData, ThrowOnError>
) => {
  return (options?.client ?? client).post<GetReviveInfoResponse, GetReviveInfoError, ThrowOnError>({
    ...options,
    url: '/api/v1/gameplay/revive/info'
  })
}

export const purchaseRevive = <ThrowOnError extends boolean = false>(
  options: Options<PurchaseReviveData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    PurchaseReviveResponse2,
    PurchaseReviveError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/gameplay/revive/purchase'
  })
}

export const createGameplaySessionOctet = <ThrowOnError extends boolean = false>(
  options: Options<CreateGameplaySessionOctetData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    CreateGameplaySessionOctetResponse,
    CreateGameplaySessionOctetError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/gameplay/session/octet/create'
  })
}

export const updateGameplaySessionOctet = <ThrowOnError extends boolean = false>(
  options: Options<UpdateGameplaySessionOctetData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    UpdateGameplaySessionOctetResponse,
    UpdateGameplaySessionOctetError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/gameplay/session/octet/update'
  })
}

export const getLeagueList = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<GetLeagueListResponse, GetLeagueListError, ThrowOnError>({
    ...options,
    url: '/api/v1/leagues/list'
  })
}

export const getLeaguesUserInfo = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetLeaguesUserInfoResponse,
    GetLeaguesUserInfoError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/leagues/me'
  })
}

export const claimLeagueReward = <ThrowOnError extends boolean = false>(
  options: Options<ClaimLeagueRewardData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    ClaimLeagueRewardResponse2,
    ClaimLeagueRewardError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/leagues/reward/claim/{league_id}'
  })
}

export const getLeaguesLeaders = <ThrowOnError extends boolean = false>(
  options: Options<GetLeaguesLeadersData, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetLeaguesLeadersResponse,
    GetLeaguesLeadersError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/leagues/{league_id}/leaders'
  })
}

export const getFreeLootbox = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    GetFreeLootboxResponse,
    GetFreeLootboxError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/lootboxes/get_free'
  })
}

export const openLootbox = <ThrowOnError extends boolean = false>(
  options: Options<OpenLootboxData, ThrowOnError>
) => {
  return (options?.client ?? client).post<OpenLootboxResponse, OpenLootboxError, ThrowOnError>({
    ...options,
    url: '/api/v1/lootboxes/open'
  })
}

export const claimDailyReward = <ThrowOnError extends boolean = false>(
  options: Options<ClaimDailyRewardData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    ClaimDailyRewardResponse2,
    ClaimDailyRewardError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/player/daily-reward/claim'
  })
}

export const claimDailyTasks = <ThrowOnError extends boolean = false>(
  options: Options<ClaimDailyTasksData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    ClaimDailyTasksResponse,
    ClaimDailyTasksError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/player/daily-task/claim'
  })
}

export const claimDailyTasksLootbox = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    ClaimDailyTasksLootboxResponse,
    ClaimDailyTasksLootboxError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/player/daily-task/claim-lootbox'
  })
}

export const getDailyTasks = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<GetDailyTasksResponse, GetDailyTasksError, ThrowOnError>({
    ...options,
    url: '/api/v1/player/daily-tasks'
  })
}

export const removePlayerFlag = <ThrowOnError extends boolean = false>(
  options: Options<RemovePlayerFlagData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    RemovePlayerFlagResponse2,
    RemovePlayerFlagError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/player/flag/remove'
  })
}

export const getPlayerProfile = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetPlayerProfileResponse,
    GetPlayerProfileError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/player/profile'
  })
}

export const getPlayerState = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<GetPlayerStateResponse, GetPlayerStateError, ThrowOnError>(
    {
      ...options,
      url: '/api/v1/player/state'
    }
  )
}

export const checkGlobalTask = <ThrowOnError extends boolean = false>(
  options: Options<CheckGlobalTaskData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    CheckGlobalTaskResponse2,
    CheckGlobalTaskError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/player/task/check/{taskId}'
  })
}

export const claimGlobalTask = <ThrowOnError extends boolean = false>(
  options: Options<ClaimGlobalTaskData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    ClaimGlobalTaskResponse2,
    ClaimGlobalTaskError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/player/task/claim'
  })
}

export const completeGlobalTask = <ThrowOnError extends boolean = false>(
  options: Options<CompleteGlobalTaskData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    CompleteGlobalTaskResponse2,
    CompleteGlobalTaskError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/player/task/complete'
  })
}

export const getGlobalTasks = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<GetGlobalTasksResponse, GetGlobalTasksError, ThrowOnError>(
    {
      ...options,
      url: '/api/v1/player/tasks'
    }
  )
}

export const getUtc = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<GetUtcResponse, GetUtcError, ThrowOnError>({
    ...options,
    url: '/api/v1/player/utc'
  })
}

export const createReferralLink = <ThrowOnError extends boolean = false>(
  options: Options<CreateReferralLinkData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    CreateReferralLinkResponse2,
    CreateReferralLinkError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/referral/link/create'
  })
}

export const getReferralsList = <ThrowOnError extends boolean = false>(
  options?: Options<GetReferralsListData, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetReferralsListResponse,
    GetReferralsListError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/referral/list'
  })
}

export const claimReferralReward = <ThrowOnError extends boolean = false>(
  options: Options<ClaimReferralRewardData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    ClaimReferralRewardResponse2,
    ClaimReferralRewardError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/referral/reward/claim'
  })
}

export const createInvoice = <ThrowOnError extends boolean = false>(
  options: Options<CreateInvoiceData, ThrowOnError>
) => {
  return (options?.client ?? client).post<CreateInvoiceResponse2, CreateInvoiceError, ThrowOnError>(
    {
      ...options,
      url: '/api/v1/shop/invoice/create'
    }
  )
}

export const getShopItems = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<GetShopItemsResponse, GetShopItemsError, ThrowOnError>({
    ...options,
    url: '/api/v1/shop/list'
  })
}

export const purchase = <ThrowOnError extends boolean = false>(
  options: Options<PurchaseData, ThrowOnError>
) => {
  return (options?.client ?? client).post<PurchaseResponse2, PurchaseError, ThrowOnError>({
    ...options,
    url: '/api/v1/shop/purchase'
  })
}

export const purchaseEventBooster = <ThrowOnError extends boolean = false>(
  options: Options<PurchaseEventBoosterData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    PurchaseEventBoosterResponse2,
    PurchaseEventBoosterError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/shop/purchase/event/booster'
  })
}

export const purchaseJackpotCoupons = <ThrowOnError extends boolean = false>(
  options: Options<PurchaseJackpotCouponsData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    PurchaseJackpotCouponsResponse2,
    PurchaseJackpotCouponsError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/shop/purchase/jackpot/coupons'
  })
}

export const purchaseProgressive = <ThrowOnError extends boolean = false>(
  options: Options<PurchaseProgressiveData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    PurchaseProgressiveResponse,
    PurchaseProgressiveError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/shop/purchase/progressive'
  })
}

export const purchasePuzzle = <ThrowOnError extends boolean = false>(
  options: Options<PurchasePuzzleData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    PurchasePuzzleResponse,
    PurchasePuzzleError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/shop/purchase/puzzle'
  })
}

export const purchaseRansom = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    PurchaseRansomResponse,
    PurchaseRansomError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/shop/purchase/ransom'
  })
}

export const checkTonInvoice = <ThrowOnError extends boolean = false>(
  options: Options<CheckTonInvoiceData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    CheckTonInvoiceResponse2,
    CheckTonInvoiceError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/shop/ton/invoice/check'
  })
}

export const claimSkin = <ThrowOnError extends boolean = false>(
  options: Options<ClaimSkinData, ThrowOnError>
) => {
  return (options?.client ?? client).post<ClaimSkinResponse2, ClaimSkinError, ThrowOnError>({
    ...options,
    url: '/api/v1/skins/claim'
  })
}

export const claimAllCollections = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    ClaimAllCollectionsResponse,
    ClaimAllCollectionsError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/skins/collection/claim/all'
  })
}

export const claimCollection = <ThrowOnError extends boolean = false>(
  options: Options<ClaimCollectionData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    ClaimCollectionResponse,
    ClaimCollectionError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/skins/collection/claim/{collectionId}'
  })
}

export const getCollection = <ThrowOnError extends boolean = false>(
  options: Options<GetCollectionData, ThrowOnError>
) => {
  return (options?.client ?? client).get<GetCollectionResponse, GetCollectionError, ThrowOnError>({
    ...options,
    url: '/api/v1/skins/collection/{collectionId}'
  })
}

export const getCollections = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<GetCollectionsResponse, GetCollectionsError, ThrowOnError>(
    {
      ...options,
      url: '/api/v1/skins/collections'
    }
  )
}

export const getSkins = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<GetSkinsResponse, GetSkinsError, ThrowOnError>({
    ...options,
    url: '/api/v1/skins/list'
  })
}

export const purchaseSkin = <ThrowOnError extends boolean = false>(
  options: Options<PurchaseSkinData, ThrowOnError>
) => {
  return (options?.client ?? client).post<PurchaseSkinResponse2, PurchaseSkinError, ThrowOnError>({
    ...options,
    url: '/api/v1/skins/purchase'
  })
}

export const selectSkin = <ThrowOnError extends boolean = false>(
  options: Options<SelectSkinData, ThrowOnError>
) => {
  return (options?.client ?? client).post<SelectSkinResponse2, SelectSkinError, ThrowOnError>({
    ...options,
    url: '/api/v1/skins/select'
  })
}

export const getUserProfile = <ThrowOnError extends boolean = false>(
  options: Options<GetUserProfileData, ThrowOnError>
) => {
  return (options?.client ?? client).get<GetUserProfileResponse, GetUserProfileError, ThrowOnError>(
    {
      ...options,
      url: '/api/v1/users/profile'
    }
  )
}

export const getAssets = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<GetAssetsResponse, GetAssetsError, ThrowOnError>({
    ...options,
    url: '/api/v1/wallet/assets'
  })
}

export const connectWallet = <ThrowOnError extends boolean = false>(
  options: Options<ConnectWalletData, ThrowOnError>
) => {
  return (options?.client ?? client).post<ConnectWalletResponse2, ConnectWalletError, ThrowOnError>(
    {
      ...options,
      url: '/api/v1/wallet/connect'
    }
  )
}

export const disconnectWallet = <ThrowOnError extends boolean = false>(
  options: Options<DisconnectWalletData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    DisconnectWalletResponse2,
    DisconnectWalletError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/wallet/disconnect'
  })
}

export const getTransactionsHistory = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetTransactionsHistoryResponse,
    GetTransactionsHistoryError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/wallet/transactions/list'
  })
}

export const cancelWithdraw = <ThrowOnError extends boolean = false>(
  options: Options<CancelWithdrawData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    CancelWithdrawResponse2,
    CancelWithdrawError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/wallet/withdraw/cancel'
  })
}

export const claimWithdrawTask = <ThrowOnError extends boolean = false>(
  options: Options<ClaimWithdrawTaskData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    ClaimWithdrawTaskResponse,
    ClaimWithdrawTaskError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/wallet/withdraw/claim'
  })
}

export const createWithdraw = <ThrowOnError extends boolean = false>(
  options: Options<CreateWithdrawData, ThrowOnError>
) => {
  return (options?.client ?? client).post<
    CreateWithdrawResponse2,
    CreateWithdrawError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/wallet/withdraw/create'
  })
}

export const getWithdrawHistory = <ThrowOnError extends boolean = false>(
  options?: Options<unknown, ThrowOnError>
) => {
  return (options?.client ?? client).get<
    GetWithdrawHistoryResponse,
    GetWithdrawHistoryError,
    ThrowOnError
  >({
    ...options,
    url: '/api/v1/wallet/withdraw/list'
  })
}

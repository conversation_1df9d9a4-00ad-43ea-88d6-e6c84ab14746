import { isTMA, setMiniAppHeaderColor, type RGB } from '@telegram-apps/sdk'
// import { nextTick } from 'vue'
// import { useRouter } from 'vue-router'

const MAIN_HEADER_COLOR = '#A2EEFF40'
const MAIN_HEADER_COLOR_RGB = '#1C9CE6'

export function useHeaderColor() {
  // const router = useRouter()

  const setHeaderColor = (gameHeaderColor: string, telegramColor: RGB) => async () => {
    // document.body?.setAttribute('style', `--header-color: ${gameHeaderColor}`)
    if ((await isTMA()) && setMiniAppHeaderColor.isSupported()) {
      setMiniAppHeaderColor(telegramColor)
    }
  }

  const setHeaderForApp = setHeaderColor(MAIN_HEADER_COLOR, MAIN_HEADER_COLOR_RGB)

  // const setHeaderAccordingToPath = (path: string) => {
  //   if (path === '/game') {
  //     setHeaderForGame()
  //   } else {
  //     setHeaderForHome()
  //   }
  // }

  // const registerHeaderAutoChange = () => {
  //   router.afterEach(to => setHeaderAccordingToPath(to.path))
  //   const currentPath = router.currentRoute.value.path
  //   nextTick(() => setHeaderAccordingToPath(currentPath))
  // }

  return { setHeaderForApp }
}

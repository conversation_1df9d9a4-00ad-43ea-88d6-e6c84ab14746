import { addToPlayerState } from '@/services/client/usePlayerState'
import { useAchievementRewardStore } from '@/stores/rewardStore'

export const useAchievementReward = () => {
  const { updatePlayerState } = addToPlayerState()
  const rewardStore = useAchievementRewardStore()

  /**
   * @param reward RewardInfo
   * @param options substractFromPlayerState - set to true when reward is already on player balance before showing
   * @returns Promise<void>
   */
  const showReward = (id: number, level: number, bonusMultiplier: number) => {
    return new Promise<void>(() => {
      rewardStore.showReward(id, level, bonusMultiplier, () => {
        updatePlayerState('multiplier', bonusMultiplier)
      })
    })
  }

  return { showReward }
}

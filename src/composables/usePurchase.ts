import { useReward } from '@/composables/useReward'
import { useInvoice } from '@/services/client/useCreateInvoice'
import { usePlayerState } from '@/services/client/usePlayerState'
import {
  usePurchaseEventBoost,
  usePurchaseJackpotCoupon,
  usePurchase as usePurchaseMutation,
  usePurchaseProgressive as usePurchaseProgressiveMutation,
  usePurchasePuzzle as usePurchasePuzzleMutation,
  usePurchaseRansom as usePurchaseRansomMutation
} from '@/services/client/usePurchase'
import type { Currency, EventType, Price, RewardInfo, RewardType } from '@/services/openapi'
import { openInvoice } from '@/services/telegram-sdk/invoice'
import { useToast } from '@/stores/toastStore'
import { isExternalPrice, isInGameCurrencyType, isInGamePrice, type InGamePrice } from '@/types'
import { computed } from 'vue'

export function usePurchase() {
  const { playerState } = usePlayerState()
  const { openInvoice: openInvoiceMutation, isPending: isPendingInvoice } = useInvoice()
  const { purchase: purchaseMutation, isPending: isPendingPurchase } = usePurchaseMutation()
  const { purchaseRansom: purchaseRansomMutation, isPending: isPendingPurchaseRansom } =
    usePurchaseRansomMutation()
  const {
    purchase: purchaseProgressiveMutation,
    isPending: isPendingPurchaseProgressive,
    onProgressivePurchased
  } = usePurchaseProgressiveMutation()
  const {
    purchase: purchaseEventBoostMutation,
    isPending: isPendingPurchaseEventBoost,
    onEventBoostPurchased
  } = usePurchaseEventBoost()
  const { purchase: purchaseJackpotCouponMutation, isPending: isPendingPurchaseJackpotCoupon } =
    usePurchaseJackpotCoupon()
  const {
    purchase: purchasePuzzleMutation,
    isPending: isPendingPurchasePuzzle,
    onPuzzlePurchased,
    getNextPuzzle
  } = usePurchasePuzzleMutation()
  const { showRewards, showReward } = useReward()
  const { showToast } = useToast()

  const isPurchasingItem = computed(() => {
    return isPendingInvoice.value || isPendingPurchase.value
  })

  // Refactor to accept an array of rewards like PurchaseProgressive
  const purchaseInvoice = (
    type: RewardType,
    item: number,
    value: number,
    price: Price,
    multiplier?: number
  ) => {
    return new Promise<Currency>((resolve, reject) => {
      if (isExternalPrice(price)) {
        openInvoiceMutation(item, price)
          .then(status => {
            if (status === 'paid') {
              return showReward({ type, value, multiplier }).then(() => resolve('stars'))
            } else if (status === 'paid_ton') {
              showToast('Transaction sent', 'info')
              resolve('ton')
              return
            } else if (status === 'failed') {
              showToast('Purchase failed', 'warning')
            } else if (status === 'canceled') {
              showToast('Transaction canceled', 'warning')
            }
            reject()
          })
          .catch(() => {
            showToast('Something went wrong', 'warning')
            reject()
          })
      } else {
        console.error('Used InGameCurrency for invoice')
        reject()
      }
    })
  }

  // Refactor to accept an array of rewards like PurchaseProgressive
  const purchase = (
    type: RewardType,
    itemId: number,
    value: number,
    price: InGamePrice,
    multiplier?: number
  ) => {
    return new Promise<Currency>((resolve, reject) => {
      const playerBalance = playerState.value?.[price.currency] ?? 0
      if (playerBalance < price.amount) {
        reject({ message: 'NOT_ENOUGH_FUNDS', payload: price.amount - playerBalance })
      } else {
        purchaseMutation(itemId, price)
          .then(() => {
            showReward({ type, value, multiplier }).then(() => resolve(price.currency))
          })
          .catch(() => {
            showToast('Something went wrong', 'warning')
            reject()
          })
      }
    })
  }

  const purchaseItem = (type: RewardType, itemId: number, value: number, price: Price) => {
    if (isInGamePrice(price)) {
      return purchase(type, itemId, value, price)
    } else {
      return purchaseInvoice(type, itemId, value, price)
    }
  }

  // Refactor to accept an array of rewards like PurchaseProgressive
  const purchaseRansom = (price: number) => {
    return new Promise<void>((resolve, reject) => {
      const playerBalance = playerState.value?.hard ?? 0
      if (playerBalance < price) {
        reject({ message: 'NOT_ENOUGH_FUNDS', payload: price - playerBalance })
      } else {
        purchaseRansomMutation({
          currency: 'hard',
          amount: price
        })
          .then(() => {
            resolve()
          })
          .catch(() => {
            showToast('Something went wrong', 'warning')
            reject()
          })
      }
    })
  }

  const purchaseProgressive = (offerId: number, price: Price, rewards: RewardInfo[]) => {
    return new Promise<void>((resolve, reject) => {
      if (isInGameCurrencyType(price.currency)) {
        const playerBalance = playerState.value?.[price.currency] ?? 0
        if (playerBalance < price.amount) {
          return reject({ message: 'NOT_ENOUGH_FUNDS', payload: price.amount - playerBalance })
        }
      }

      purchaseProgressiveMutation(offerId, isInGamePrice(price) ? price : null)
        .then(response => {
          if (response.invoiceLink) {
            openInvoice(response.invoiceLink)
              .then(status => {
                if (status === 'paid') {
                  showRewards(rewards).then(resolve)
                  onProgressivePurchased(offerId)
                } else if (status === 'failed') {
                  showToast('Purchase failed', 'warning')
                  reject()
                } else {
                  reject()
                }
              })
              .catch(() => {
                showToast('Something went wrong', 'warning')
                reject()
              })
          } else {
            showRewards(rewards).then(resolve)
            onProgressivePurchased(offerId)
          }
        })
        .catch(() => {
          showToast('Something went wrong', 'warning')
          reject()
        })
    })
  }

  const purchaseEventBoost = (boosterId: number, event: EventType, price: Price) => {
    return new Promise<void>((resolve, reject) => {
      if (isInGameCurrencyType(price.currency)) {
        const playerBalance = playerState.value?.[price.currency] ?? 0
        if (playerBalance < price.amount) {
          return reject({ message: 'NOT_ENOUGH_FUNDS', payload: price.amount - playerBalance })
        }
      }

      purchaseEventBoostMutation(boosterId, event, isInGamePrice(price) ? price : null)
        .then(response => {
          if (response.invoiceLink) {
            openInvoice(response.invoiceLink)
              .then(status => {
                if (status === 'paid') {
                  onEventBoostPurchased()
                  resolve()
                } else if (status === 'paid_ton') {
                  showToast('Transaction sent', 'info')
                  resolve()
                } else if (status === 'failed') {
                  showToast('Purchase failed', 'warning')
                  reject()
                } else {
                  reject()
                }
              })
              .catch(() => {
                showToast('Something went wrong', 'warning')
                reject()
              })
          } else {
            resolve()
          }
        })
        .catch(() => {
          showToast('Something went wrong', 'warning')
          reject()
        })
    })
  }

  const purchasePuzzle = (offerId: number, itemIndex: number, price: Price) => {
    return new Promise<void>((resolve, reject) => {
      if (isInGameCurrencyType(price.currency)) {
        const playerBalance = playerState.value?.[price.currency] ?? 0
        if (playerBalance < price.amount) {
          return reject({ message: 'NOT_ENOUGH_FUNDS', payload: price.amount - playerBalance })
        }
      }

      purchasePuzzleMutation(offerId, itemIndex, isInGamePrice(price) ? price : null)
        .then(response => {
          if (response.invoiceLink) {
            openInvoice(response.invoiceLink)
              .then(status => {
                if (status === 'paid') {
                  resolve()
                } else if (status === 'paid_ton') {
                  showToast('Transaction sent', 'info')
                  resolve()
                } else if (status === 'failed') {
                  showToast('Purchase failed', 'warning')
                  reject()
                } else {
                  reject()
                }
              })
              .catch(() => {
                showToast('Something went wrong', 'warning')
                reject()
              })
          } else {
            resolve()
          }
        })
        .catch(() => {
          showToast('Something went wrong', 'warning')
          reject()
        })
    })
  }

  const purchaseJackpotCoupon = (id: number, price: InGamePrice) => {
    return new Promise<void>((resolve, reject) => {
      const playerBalance = playerState.value?.[price.currency] ?? 0
      if (playerBalance < price.amount) {
        reject({ message: 'NOT_ENOUGH_FUNDS', payload: price.amount - playerBalance })
      } else {
        purchaseJackpotCouponMutation(id, price)
          .then(() => resolve())
          .catch(() => {
            showToast('Something went wrong', 'warning')
            reject()
          })
      }
    })
  }

  return {
    purchaseInvoice,
    purchase,
    purchaseProgressive,
    purchasePuzzle,
    purchaseRansom,
    purchaseItem,
    purchaseEventBoost,
    purchaseJackpotCoupon,
    isPendingInvoice,
    isPendingPurchase,
    isPurchasingItem,
    isPendingPurchaseProgressive,
    isPendingPurchasePuzzle,
    isPendingPurchaseRansom,
    isPendingPurchaseEventBoost,
    isPendingPurchaseJackpotCoupon,
    onPuzzlePurchased,
    getNextPuzzle
  }
}

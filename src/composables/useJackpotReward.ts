import jackpotImage from '@/assets/images/temp/jackpot/title.png'
import { useReward } from '@/composables/useReward'
import { usePlayerReceivedEventReward } from '@/services/client/usePlayerFlag'
import { usePlayerState } from '@/services/client/usePlayerState'
import { onMounted } from 'vue'

export function useJackpotReward() {
  const { playerState } = usePlayerState()
  const { showReward } = useReward()
  const { onPlayerRecievedJackpotReward } = usePlayerReceivedEventReward()

  onMounted(() => {
    if (playerState.value?.jackpotEventReward) {
      showReward(playerState.value.jackpotEventReward, {
        isAlreadyOnPlayerState: true,
        image: jackpotImage
      }).then(() => {
        onPlayerRecievedJackpotReward()
      })
    }
  })
}

import { intervalService } from '@/services/timer'
import { computed, ref } from 'vue'

type Time = {
  hours: number
  minutes: number
  seconds: number
}

export type TimerId =
  | 'onepercentEvent'
  | 'onepercentEventBanner'
  | 'onepercentEventWindow'
  | 'hotrecordEvent'
  | 'hotrecordEventWindow'
  | 'tonEvent'
  | 'tonEventBanner'
  | 'tonEventPool'
  | 'customCoinEvent'
  | 'customCoinEventPromo'
  | 'customCoinEventBanner'
  | 'customCoinEventWindow'
  | 'communityBattleEvent'
  | 'clanEvent'
  | 'clanEventWindow'
  | 'farming'
  | 'dailyTask'
  | 'lives'
  | 'exploiters'
  | 'deepDive'
  | 'scrollingOffer'
  | 'snakeOffer'
  | 'fragmentOffer'
  | 'wheelSpin'
  | 'boosterMagnetTimer'
  | 'boosterJumperTimer'
  | 'boosterAimTimer'
  | 'jackpotEventCoupons'
  | 'jackpotEventEnd'

export function useCountdownTimer(
  timerId: TimerId,
  callbacks?: { onTimerTick?: Function; onTimerEnd?: Function }
) {
  const countdown = ref(0)

  // Computed properties for hours, minutes, and seconds
  const days = computed(() => Math.floor(countdown.value / 86400))
  const hours = computed(() => Math.floor((countdown.value % 86400) / 3600))
  const minutes = computed(() => Math.floor((countdown.value % 3600) / 60))
  const seconds = computed(() => countdown.value % 60)

  const displayDays = computed(() => days.value > 0)
  const displayHours = computed(() => hours.value > 0)
  const displayMinutes = computed(() => minutes.value > 0 && !displayDays.value)
  const displaySeconds = computed(() => seconds.value > 0 && !displayHours.value)

  // Format time with leading zeroes
  const formatTime = (time: number) => time.toString().padStart(2, '0')

  // Start the countdown timer
  const startTimer = () => {
    stopTimer()
    intervalService.setInterval(timerId, () => {
      if (countdown.value > 0) {
        countdown.value--
        callbacks?.onTimerTick && callbacks.onTimerTick(countdown.value)
      } else {
        stopTimer()
        callbacks?.onTimerEnd && callbacks.onTimerEnd()
      }
    })
  }

  // Stop the timer when done
  const stopTimer = () => {
    intervalService.stopInterval(timerId)
  }

  const initTimerWithTime = (time: Time) => {
    stopTimer()
    countdown.value = time.hours * 3600 + time.minutes * 60 + time.seconds
    startTimer()
  }
  const initTimerWithTotal = (totalSeconds: number) => {
    stopTimer()
    countdown.value = totalSeconds > 0 ? totalSeconds : 0
    startTimer()
  }

  return {
    countdown,
    days,
    hours,
    minutes,
    seconds,
    displayDays,
    displayHours,
    displayMinutes,
    displaySeconds,
    formatTime,
    stopTimer,
    initTimerWithTime,
    initTimerWithTotal
  }
}

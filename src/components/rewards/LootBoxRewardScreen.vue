<script setup lang="ts">
import { nextTick, onMounted, ref } from 'vue';
import { useLootboxReward } from '@/stores/rewardStore'
import { useI18n } from 'vue-i18n'
import type { LootBoxType, RewardInfo, RewardType, SkinRarity } from '@/services/openapi'

import * as spine from '@esotericsoftware/spine-player'
import '@esotericsoftware/spine-player/dist/spine-player.min.css'

const { t } = useI18n()

const props = defineProps<{
  lootboxType: LootBoxType
  rewards: RewardInfo[]
}>()
const emit = defineEmits(['show-rewards'])

const lootBoxStore = useLootboxReward()

const TRANSITION_TIME = 2500
const SKIN_TRANSITION_TIME = 3000
const spinePlayer = ref<spine.SpinePlayer | null>(null);

const showLootboxRewards = (transitionTime: number) => {
  setTimeout(() => {
    const rewards: RewardInfo[] = lootBoxStore.rewards.map((item) => {
      return {
        multiplier: item.multiplier,
        type: item.type,
        value: item.value
      }
    })
    
    const uniqueRewards = Object.values(
      rewards.reduce((acc: Record<string, RewardInfo>, item) => {
        const { type, value, multiplier } = item;
        if (type === 'skin') {
          acc[value] = { type, value, multiplier };
        } else if (!acc[type] || type === 'fullLives') {
          acc[type] = { type, value, multiplier };
        } else {
          acc[type].value += value;
        }
        return acc;
      }, {} as Record<RewardType, RewardInfo>)
    );
    
    emit('show-rewards', uniqueRewards)
    spinePlayer.value?.dispose()
  }, transitionTime)
}

const BOX_TO_SPINE_SKIN: Record<LootBoxType, string> = {
  'rainbowLootBox': 'box/pink',
  'luckyLootBox': 'box/green',
  'magicLootBox': 'box/magic',
  'duckyLootBox': 'box/green',
  'fightLootBox': 'box/green',
}

const RARITY_TO_SPINE_SKIN: Record<SkinRarity, string> = {
  'common': 'uni/uniGreen',
  'rare': 'uni/uniBlue',
  'epic': 'uni/uniViolet',
}

onMounted(() => {
  if (props.lootboxType && props.rewards.length) {
    const isSkinReward = props.rewards.some((reward) => reward.type === 'skin')
    const skinRarity = 'common'
    nextTick(() => {
      new spine.SpinePlayer('player-container', {
        skeleton: '/spine/Lootbox/Box.json',
        atlas: '/spine/Lootbox/Box.atlas.txt',
        scale: 1,
        preserveDrawingBuffer: true,
        animations: ['action_gift', 'action_uni'],
        skin: BOX_TO_SPINE_SKIN[props.lootboxType],
        skins: [BOX_TO_SPINE_SKIN[props.lootboxType], RARITY_TO_SPINE_SKIN[skinRarity] ?? ''],
        showControls: false,
        interactive: false,
        viewport: {
          x: -450,
          y: -50,
          width: 1000,
          height: 1000,
        },
        backgroundColor: '#00000000',
        alpha: true,
        showLoading: false,
        premultipliedAlpha: false,
        success: function (player) {
          spinePlayer.value = player
          if (isSkinReward) {
            const skeleton = player.skeleton
            if (!skeleton) return
            const data = skeleton.data
            const combinedSkin = new spine.Skin("combined")
            
            const boxSkin = data.findSkin(BOX_TO_SPINE_SKIN[props.lootboxType])
            const uniSkin = data.findSkin(RARITY_TO_SPINE_SKIN[skinRarity])
            if (!boxSkin || !uniSkin) return

            combinedSkin.addSkin(boxSkin)
            combinedSkin.addSkin(uniSkin)

            skeleton.setSkin(combinedSkin)
            skeleton.setSlotsToSetupPose()
          }

          player.setAnimation(isSkinReward ? 'action_uni' : 'action_gift', false);
          showLootboxRewards(isSkinReward ? SKIN_TRANSITION_TIME : TRANSITION_TIME)
        },
        error: function (_, reason) {
          alert(reason);
        }
      })
    })
  }
})
</script>

<template>
  <div
    id="player-container"
    class="absolute top-0 left-0 w-full h-full"
  ></div>
</template>

<style lang="scss">
.lootBox-reward {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .lootbox-rewards-grid__item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 90px;

    &-shine {
      width: 150px;
    }

    &-image {
      width: 60px;
    }
  }

  &__box {
    @keyframes box-hide-animation {
      0% {
        transform: scale(1);
      }
      8% {
        transform: scale(1);
      }
      100% {
        transform: scale(0);
        opacity: 0;
      }
    }

    &_hide {
      animation: box-hide-animation 1s forwards;
    }
  }

  &__explosion {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 50%;
    top: 50%;
    width: 100px;
    transform: translate(-50%, -50%) scale(0);
    z-index: 1000;

    @keyframes explosion-animation {
      0% {
        transform: translate(-50%, -50%) scale(0);
      }
      60% {
        opacity: 1;
      }
      70% {
        transform: translate(-50%, -50%) scale(7);
      }
      100% {
        transform: translate(-50%, -50%) scale(5);
        opacity: 0;
      }
    }

    &_active {
      animation: explosion-animation 2s forwards;
    }
  }
}
</style>

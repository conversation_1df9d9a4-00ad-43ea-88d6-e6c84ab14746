<script setup lang="ts">
import { onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import {
  isLootboxReward,
  isNumeralReward,
  isSimpleReward,
  isTimeReward,
  type Reward
} from '@/types'
import RewardItem from '@/components/RewardItem.vue';
import { useIconImage } from '@/composables/useIconImage';
import starsBg from '@/assets/images/temp/stars-bg.png'

defineProps<{
  rewards: Reward[]
  image: string
}>()

const emit = defineEmits(['collect'])

const { t } = useI18n();

const { getImage } = useIconImage()

const TIME_TO_COLLECT = 3000

onMounted(() => {
  setTimeout(() => emit('collect'), TIME_TO_COLLECT)
})
</script>

<template>
  <div class="relative">
    <template v-if="image">
      <div class="text-[32px] leading-[46px] text-shadow text-center">
        You Won
      </div>
      <img :src="image" class="w-[50%] mx-auto" />
      <img :src="starsBg" class="absolute -top-[25px] left-0 w-[100%] z-[-1]" />
    </template>
  </div>

  <div class="flex justify-evenly items-center flex-wrap w-full max-w-[375px] gap-y-6">
    <RewardItem
      v-for="reward, index in rewards"
      class="multiple-rewards__item"
      value-class="translate-y-2"
      :id="`${reward.type}-balance-reward-item`"
      :style="{
        '--reward-index': index
      }"
      :key="index"
      :type="reward.type"
      :image="getImage(reward.type)"
      :amount="
        isNumeralReward(reward) || isLootboxReward(reward)
          ? reward.value
          : isTimeReward(reward)
            ? reward.duration
            : 0
      "
      :show-amount="!isSimpleReward(reward)"
      :show-background="false"
      dontGetCurrencyRealAmount
    />
  </div>

  <div class="space-y-3">
    <p class="text-[24px] font-extrabold text-shadow text-shadow_black text-shadow_thin tracking-normal">
      {{ t('actions.tapToCollect') }}
    </p>
    <div class="reward-screen__timer" :style="{ '--reward-timer-duration': `${TIME_TO_COLLECT}ms` }">
      <div class="reward-screen__timer-bar"></div>
    </div>
  </div>
</template>

<style lang="scss">
.multiple-rewards {
  &__item {
    width: 30%;
    height: 80px;

    --reward-index: 0;
    animation: reward-bounce 0.5s ease-in-out;
    opacity: 1;
    animation-delay: calc(var(--reward-index) * 0.1s);
  }
}
</style>

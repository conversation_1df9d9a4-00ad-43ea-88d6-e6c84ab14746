<script setup lang="ts">
import AchievementAvatarItem from '@/components/UI/AchievementAvatarItem.vue'
import {
  ACHIEVEMENTS_IMAGES,
  ACHIEVEMENTS_LEVELS_FRAMES,
  type AchievementName
} from '@/constants/achievements.ts'
import { useAchievementRewardStore } from '@/stores/rewardStore'
import { watch } from 'vue'
import { useI18n } from 'vue-i18n'
import VOverlay from '../VOverlay.vue'
import BalanceItem from '@/components/UI/BalanceItem.vue'
import { coinCollectingAnimation } from '@/utils/coinCollectingAnimation'

const TIME_TO_COLLECT = 3000

const { t } = useI18n()

const achievementStore = useAchievementRewardStore()

const collect = () => {
  if (!achievementStore.isOpen) return
  achievementStore.closeReward()
  coinCollectingAnimation(
    ['#multiplier-balance-header'],
    'multiplier',
    '#multiplier-balance-reward-item'
  )
}

watch(() => achievementStore.isOpen, value => {
  if (!value || !achievementStore.plusMultiplier) return
  setTimeout(collect, TIME_TO_COLLECT)
})
</script>

<template>
  <VOverlay
    :isOpen="achievementStore.isOpen"
    class="reward-screen !justify-around"
    @click="collect"
  >
    <div class="relative z-10 text-center tracking-normal space-y-3">
      <p class="text-[32px] leading-[46x] text-shadow">
        {{ t('achievementRewards.newAchievement') }}
      </p>
      <p
        v-if="achievementStore.achievementId"
        class="text-[40px] leading-[55x] text-[#FFE657] text-shadow"
      >
        {{
          t(
            `achievements.list.${achievementStore.achievementId}.${achievementStore.achievementLevel}.name`
          )
        }}
      </p>
    </div>

    <AchievementAvatarItem
      class="advanced-reward__item z-10 mx-auto relative"
      :class="{ 'advanced-reward__item_active': achievementStore.isOpen }"
      size="140"
      :frame-src="ACHIEVEMENTS_LEVELS_FRAMES[achievementStore.achievementLevel - 1] ?? ''"
      :src="ACHIEVEMENTS_IMAGES[achievementStore.achievementId as AchievementName] ?? ''"
    />

    <div class="space-y-[50%]">
      <BalanceItem
        id="multiplier-balance-reward-item"
        class="w-fit mx-auto"
        image-class="-top-[12px] !w-[42px] !h-[42px]"
        bar-class="!h-[28px] !pl-[30px] !pr-[15px]"
        balance-class="!text-[22px] font-black text-[#FF9205]"
        iconName="multiplier-tickets-bg"
      >
        {{ achievementStore.plusMultiplier }}
      </BalanceItem>
      <div class="space-y-3">
        <p class="text-[24px] font-extrabold text-shadow text-shadow_black text-shadow_thin tracking-normal">
          {{ t('actions.tapToCollect') }}
        </p>
        <div class="reward-screen__timer" :style="{ '--reward-timer-duration': `${TIME_TO_COLLECT}ms` }">
          <div class="reward-screen__timer-bar"></div>
        </div>
      </div>
    </div>
  </VOverlay>
</template>

<style lang="scss">
</style>

<script setup lang="ts">
import {
  useLootboxReward,
  useRewardStore,
} from '@/stores/rewardStore';
import VOverlay from '@/components/VOverlay.vue';
import MultipleRewardScreen from '@/components/rewards/simple-rewards/MultipleRewardScreen.vue';
import { coinCollectingAnimation } from '@/utils/coinCollectingAnimation'
import LootBoxRewardScreen from '@/components/rewards/LootBoxRewardScreen.vue';
import { useReward } from '@/composables/useReward';

const rewardStore = useRewardStore()
const lootBoxStore = useLootboxReward()
const { showRewards } = useReward()

const collect = () => {
  if (rewardStore.isOpen && rewardStore.rewards) {
    rewardStore.rewards.forEach(reward => {
      coinCollectingAnimation(
        [`#${reward.type}-balance-header`, `#${reward.type}-balance-home`, `#${reward.type}-balance-reward`],
        reward.type,
        `#${reward.type}-balance-reward-item`
      )
    })
    rewardStore.closeReward()
  }
}

const nextLootboxReward = () => {
  lootBoxStore.nextLootBox()
}
</script>

<template>
  <VOverlay
    :isOpen="rewardStore.isOpen || lootBoxStore.isOpen"
    class="reward-screen"
    @click="collect"
  >
    <MultipleRewardScreen
      v-if="rewardStore.isOpen"
      :rewards="rewardStore.rewards"
      :image="rewardStore.titleImage"
      @collect="collect"
    />
    <LootBoxRewardScreen
      v-else-if="lootBoxStore.isOpen"
      :lootboxType="lootBoxStore.lootboxType!"
      :rewards="lootBoxStore.rewards"
      @show-rewards="(rewards) => showRewards(rewards).then(nextLootboxReward)"
    />
  </VOverlay>
</template>

<style lang="scss">
</style>

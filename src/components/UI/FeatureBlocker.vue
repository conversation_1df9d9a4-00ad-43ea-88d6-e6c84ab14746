<script setup lang="ts">
import { ref } from 'vue';
import { useFloating, offset } from '@floating-ui/vue';

import lockImage from '@/assets/images/temp/locks/lock.png'
import chainSmallImage from '@/assets/images/temp/locks/chain-small.png'
import chainMediumImage from '@/assets/images/temp/locks/chain-medium.png'
import chainBigImage from '@/assets/images/temp/locks/chain-big.png'
import type { LeagueFeature } from '@/services/openapi';

type BlockedFeature = LeagueFeature | 'withdraw'

export type SharedProps = {
  placement: 'top' | 'bottom' | 'left' | 'right',
  class?: string
}

export type Props = SharedProps & {
  isFeatureBlocked: boolean,
  feature: BlockedFeature
}

const LOCK_CLASS: Record<BlockedFeature, string> = {
  'dailyReward': 'w-[14px] bottom-0',
  'onePercentEvent': 'w-[14px] bottom-0',
  'hotRecordEvent': 'w-[14px] bottom-0',
  'tonMiningEvent': 'w-[14px] bottom-0',
  'offers': 'w-[14px] bottom-0',
  'customCoinEvent': 'w-[14px] bottom-0',
  'battleEvent': 'w-[14px] bottom-0',
  'clanEvent': 'w-[14px] bottom-0',
  'dynamicCoins': 'w-[14px] bottom-0',
  'puzzleCoins': 'w-[14px] bottom-0',
  'farming': 'w-[21px] -bottom-[14px]',
  'withdraw': 'w-[19px] -bottom-[18px]',
  'lives': '',
}

const CHAIN_CLASS: Record<BlockedFeature, string> = {
  'dailyReward': 'w-[33px] -bottom-[4px]',
  'onePercentEvent': 'w-[33px] -bottom-[4px]',
  'hotRecordEvent': 'w-[33px] -bottom-[4px]',
  'tonMiningEvent': 'w-[33px] -bottom-[4px]',
  'offers': 'w-[33px] -bottom-[4px]',
  'customCoinEvent': 'w-[33px] -bottom-[4px]',
  'battleEvent': 'w-[33px] -bottom-[4px]',
  'clanEvent': 'w-[33px] -bottom-[4px]',
  'dynamicCoins': 'w-[33px] -bottom-[4px]',
  'puzzleCoins': 'w-[33px] -bottom-[4px]',
  'farming': 'w-[60px] -bottom-[16px]',
  'withdraw': 'w-[87px] -bottom-[29px]',
  'lives': '',
}

const CHAINS: Record<BlockedFeature, string> = {
  'dailyReward': chainSmallImage,
  'onePercentEvent': chainSmallImage,
  'hotRecordEvent': chainSmallImage,
  'tonMiningEvent': chainSmallImage,
  'offers': chainSmallImage,
  'customCoinEvent': chainSmallImage,
  'battleEvent': chainSmallImage,
  'clanEvent': chainSmallImage,
  'dynamicCoins': chainSmallImage,
  'puzzleCoins': chainSmallImage,
  'farming': chainMediumImage,
  'withdraw': chainBigImage,
  'lives': '',
}

const props = withDefaults(defineProps<Props>(), {
  class: ''
})

const emit = defineEmits(['click'])

const isOpen = ref(false);

const reference = ref<null | HTMLDivElement>(null);
const floating = ref(null);
const { floatingStyles } = useFloating(reference, floating, {
  placement: props.placement,
  middleware: [offset(10)],
});

const checkLeague = (e: Event) => {
  if (props.isFeatureBlocked) {
    e.stopPropagation();
    e.preventDefault();
    return;
  } else {
    emit('click');
  }
}

const showTootip = () => {
  if (props.isFeatureBlocked) {
    isOpen.value = true;
  }
}

const hideTootip = () => {
  isOpen.value = false;
}
</script>

<template>
  <div
    class="feature-blocker"
    :class="{
      'feature-blocker_block': isFeatureBlocked,
      [props.class]: true
    }"
    ref="reference"
    @click="checkLeague"
    @focus="showTootip"
    @blur="hideTootip"
    tabindex="0"
  >
    <slot></slot>
    <template v-if="isFeatureBlocked">
      <img
        class="feature-blocker__locks absolute left-1/2 -translate-x-[95%]"
        :class="CHAIN_CLASS[feature]"
        :src="CHAINS[feature]"
      />
      <img
        class="feature-blocker__locks absolute right-1/2 translate-x-[95%] -scale-x-100"
        :class="CHAIN_CLASS[feature]"
        :src="CHAINS[feature]"
      />
      <img
        class="feature-blocker__locks absolute left-1/2 -translate-x-1/2"
        :class="LOCK_CLASS[feature]"
        :src="lockImage"
      />
    </template>
  </div>
  <div v-if="isOpen" ref="floating" class="bg-white rounded-[15px] py-[14px] px-2" :style="floatingStyles">
    <slot name="tooltip-content"></slot>
    <!-- <div
      ref="floatingArrow"
      :style="{
        position: 'absolute',
        left:
          middlewareData.arrow?.x != null
            ? `${middlewareData.arrow.x}px`
            : '',
        top:
          middlewareData.arrow?.y != null
            ? `${middlewareData.arrow.y}px`
            : '',
      }"
    ></div> -->
  </div>
</template>

<style lang="scss" scoped>
.feature-blocker {
  position: relative;

  &_block > * {
    pointer-events: none;
    filter: brightness(0.5) contrast(130%);
  }
  
  &__locks {
    filter: brightness(1) contrast(100%);
  }
}
</style>

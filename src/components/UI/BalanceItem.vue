<script setup lang="ts">
import plusButton from '@/assets/images/temp/plus-button.png';

const props = defineProps<{
  addButton?: boolean;
  imageClass?: string;
  iconName?: string;
  barClass?: string;
  balanceClass?: string;
  gold?: boolean
}>();

const emit = defineEmits(['add']);

</script>

<template>
  <div class="balance-item">
    <div class="balance-item__image" :class="imageClass">
      <div class="icon-bg !h-full !w-full" :class="iconName">
        <slot name="count"></slot> <!-- for text inside image -->
      </div>
    </div>
    <div class="balance-item__bar" :class="[barClass, gold ? 'balance-item__bar_gold' : '']">
      <slot name="bar"></slot> <!-- for progress bar -->
      <div class="balance-item__balance" :class="[balanceClass, gold ? 'balance-item__balance_gold' : '']">
        <slot></slot>
      </div>
    </div>
    <img
      v-if="props.addButton"
      class="balance-item__add"
      :src="plusButton"
      @click="() => emit('add')"
    />
  </div>
</template>

<script setup lang="ts">
import multiplierIcon from '@/assets/images/temp/x_tickets.png';
import { formatNumberWithSeparator } from '@/utils/number';

const props = defineProps<{
  value: number
}>();
</script>

<template>
  <div class="multiplier-balance-item-wrapper">
    <div class="multiplier-balance-item">
      <img class="multiplier-balance-item__icon" :src="multiplierIcon" />
      {{ formatNumberWithSeparator(props.value) }}
    </div>
  </div>
</template>

<style lang="scss">
.multiplier-balance-item-wrapper {
  height: 21px;
  min-width: 80px;
  padding: 0 8px 0 5px;
  background-color: #FFE02F;
  border-radius: 0 0 5px 5px;
  box-shadow: 0px -4px 0px 0px #FFC636 inset,
    0px 2px 0px 0px #FFF08E inset,
    0px 1px 0px 0px #00000040;

  color: #C44F00;
  line-height: 21px;
  font-size: 19px;

  transform: perspective(10px) rotateX(-3deg);
}

.multiplier-balance-item {
  display: flex;
  align-items: center;
  justify-content: center;
  column-gap: 5px;
  transform: perspective(10px) rotateX(3deg);

  &__icon {
    position: relative;
    top: -1px;
    height: 19px;
  }
}
</style>

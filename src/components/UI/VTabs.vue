<script setup lang="ts">
import { TabGroup, Tab<PERSON>ist, Tab, TabPanels, TabPanel } from '@headlessui/vue'
import { useI18n } from 'vue-i18n'
import RedDotBadge from '@/components/UI/RedDotBadge.vue'
import { onMounted, onUnmounted } from 'vue'

const { t } = useI18n()

export type TabProp = {
  id: string
  name: string
  notification?: boolean
  disabled?: boolean
}

const props = defineProps<{
  tabs: TabProp[]
  isShadow?: boolean
  panelsClass?: string
}>()

onMounted(() => {
  document.documentElement.classList.add('footer-high-offset')
})
onUnmounted(() => {
  setTimeout(() => {
    document.documentElement.classList.remove('footer-high-offset')
  }, 500)
})
</script>

<template>
  <div class="tabs-group">
    <TabGroup>
      <div class="tabs-group__panels-wrapper">
        <TabPanels as="div" class="tabs-group__panels" :class="props.panelsClass">
          <TabPanel v-for="tab in props.tabs" :key="tab.id" as="template">
            <slot :name="tab.id"></slot>
          </TabPanel>
        </TabPanels>
      </div>
      <TabList class="tabs-group__tab-list">
        <div v-if="props.isShadow" class="tabs-group__tab-list-shadow-gradient" />
        <Tab
          v-for="tab in props.tabs"
          :key="tab.id"
          as="template"
          v-slot="{ selected }"
          :disabled="tab.disabled"
        >
          <button
            :class="{
              'tabs-group__tab': true,
              'tabs-group__tab_active': selected,
              'tabs-group__tab_disabled': tab.disabled,
            }"
          >
            <RedDotBadge v-if="tab.notification" class="top-[0px] right-[1px]" />
            <div class="text-shadow" :class="{ 'text-shadow_black': tab.disabled }">
              {{ t(tab.name) }}
            </div>
          </button>
        </Tab>
      </TabList>
    </TabGroup>
  </div>
</template>

<script setup lang="ts">
import FeatureBlocker, { type SharedProps } from '@/components/UI/FeatureBlocker.vue';
import { usePlayerState } from '@/services/client/usePlayerState';
import leaguesService from '@/services/local/leagues';
import type { LeagueFeature } from '@/services/openapi';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n()

const props = defineProps<SharedProps & { userLeague: number, feature: LeagueFeature }>()
const requiredLeague = leaguesService.getRequiredLeague(props.feature);
const isFeatureBlocked = computed(() => props.userLeague < requiredLeague);

const { playerState } = usePlayerState()
const progressiveOfferId = computed(() => playerState.value?.progressiveOffers.find(offer => offer.usageDynamicCoins)?.id ?? 10000)
const getFeatureText = (feature: LeagueFeature) => {
  if (feature === 'dynamicCoins') {
    return progressiveOfferId.value === 10000 ? t('features.dynamicCoins_1') : t('features.dynamicCoins_2')
  }
  return t(`features.${feature}`)
}
</script>

<template>
  <FeatureBlocker
    :is-feature-blocked="isFeatureBlocked"
    :feature="feature"
    :placement="props.placement"
    :class="props.class"
  >
    <template #default>
      <slot></slot>
    </template>
    <template #tooltip-content>
      <i18n-t
        class="text-[13px] leading-[17px] text-[#1E4073] text-center whitespace-pre"
        tag="p"
        keypath="blockers.league"
      >
        <template v-slot:league>
          <span class="text-[#FF8C00]">
            {{ t('leagues.league', { league: requiredLeague }) }}
          </span>
        </template>
        <template v-slot:feature>
          <span class="text-[#FF8C00]">
            {{ getFeatureText(feature) }}
          </span>
        </template>
      </i18n-t>
    </template>
  </FeatureBlocker>
</template>

<style lang="scss" scoped>

</style>

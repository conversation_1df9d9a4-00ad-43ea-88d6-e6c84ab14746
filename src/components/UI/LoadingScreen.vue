<script setup lang="ts">
import LoaderText from '../LoaderText.vue';
import VOverlay from '../VOverlay.vue';

const props = defineProps<{
  isOpen: boolean;
}>()
const emit = defineEmits(['close'])

</script>

<template>
  <VOverlay
    :isOpen="props.isOpen"
    class="modal-window-overlay flex items-center justify-center"
    @click="() => emit('close')"
  >
    <LoaderText class="text-[24px] text-[#6DB0ED]" is-loading />
  </VOverlay>
</template>

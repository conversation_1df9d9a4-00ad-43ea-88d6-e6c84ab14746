<script setup lang="ts">
import frameImage from '@/assets/images/temp/achievements/level-frames/achivments-level-frame-1.png'
import achievementImage from '@/assets/images/temp/achievements/unlock-skins.png'
import AvatarItem from '@/components/UI/AvatarItem.vue'
import { computed } from 'vue'

const props = defineProps<{
  src: string | null
  frameSrc: string | null
  size: string
}>()

const image = computed(() => props.src || achievementImage)

const frame = computed(() => props.frameSrc || frameImage)
</script>

<template>
  <div class="achievement-avatar relative">
    <div
      class="achievement-avatar-frame"
      :style="{
        backgroundImage: `url(${frame})`,
        height: props.size + 'px',
        width: props.size + 'px'
      }"
      alt="Level frame"
    >
      <AvatarItem :size="size" :src="image" />
    </div>
  </div>
</template>

<style lang="scss">
.achievement-avatar {
  display: flex;
  align-items: center;
  justify-content: center;

  &-frame {
    display: flex;
    align-items: center;
    justify-content: center;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 4px;
    left: 25%;
    transform: translateY(40%);
    width: 50%;
    height: 15%;
    border-radius: 50%;
    background: radial-gradient(
      50% 50% at 50% 50%,
      var(--skin-item-shadow-color-1) 17.5%,
      var(--skin-item-shadow-color-2) 100%
    );
    transition: transform 0.3s;
    z-index: -1;
  }
}
</style>

<script setup lang="ts">
import { hapticsService } from '@/shared/haptics/hapticsService.ts'
import LoaderText from '../LoaderText.vue';
import VOverlay from '../VOverlay.vue';

const props = defineProps<{
  isOpen: boolean;
  title?: string;
  class?: string;
  overlayClass?: string;
  hideClose?: boolean;
  isLoading?: boolean;
  short?: boolean;
}>()
const emit = defineEmits(['close'])

const close = () => {
  hapticsService.triggerImpactHapticEvent('light')
  emit('close')
}
</script>

<template>
  <!-- <Teleport to="#app-view"> -->
    <VOverlay
      :isOpen="isOpen"
      class="modal-window-overlay flex items-center justify-center"
      :class="props.overlayClass || ''"
      @click-self="() => emit('close')"
      :short="short"
    >
      <div
        class="modal-window"
        :class="[props.class, props.isLoading ? 'modal-window_hidden' : '']"
      >
        <div v-if="!hideClose" class="close-button absolute top-[12px] right-[13px]" @click="close"></div>
        <h1
          v-if="props.title"
          class="modal-window__title text-center text-[20px] leading-[24px] text-shadow text-shadow_black mb-[13px]"
        >
          {{ props.title }}
        </h1>
        <slot></slot>
        <div class="absolute w-full top-full left-1/2 -translate-x-1/2">
          <slot name="bottom"></slot>
        </div>
      </div>
      <LoaderText
        class="modal-window__loading"
        :class="{
          'modal-window__loading_active': props.isLoading
        }"
        :is-loading="props.isLoading"
      />
    </VOverlay>
  <!-- </Teleport> -->
</template>

<style lang="scss">
.modal-window-overlay {
  padding: 0 16px;
}

.modal-window {
  --modal-window-bg: linear-gradient(180deg, #009BE0 0%, #0074C6 100%);
  --modal-window-inner-shadow: #6AC1E9;;
  position: relative;
  width: 100%;
  padding: 10px 11px 12px;

  background: var(--modal-window-bg);
  border-radius: 12px;
  box-shadow: 0 4px #00000040, inset 0 2px var(--modal-window-inner-shadow);

  opacity: 1;
  transition: opacity 0.3s;

  &_hidden {
    opacity: 0;
  }

  .close-button {
    z-index: 1;
    --close-btn-background-color: #275a9d;
  }

  &__loading {
    position: absolute;
    pointer-events: none;

    font-size: 24px;
    color: #6DB0ED;
    transition: opacity 0.3s;
    transition-delay: 1s;
    opacity: 0;

    &_active {
      opacity: 1;
    }
  }
}
</style>

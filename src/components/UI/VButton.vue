<script setup lang="ts">
import { computed } from 'vue';
import { hapticsService } from '@/shared/haptics/hapticsService.ts'

export type ButtonType = 'default' | 'success' | 'accent' | 'danger' | 'transparent'
export type ButtonSize = 'xxsmall' | 'xsmall' | 'small' | 'medium' | 'large'

export type ButtonProps = {
  text?: string | number
  type?: ButtonType
  size?: ButtonSize
  image?: string
  imageClass?: string
  disabled?: boolean
  onClick?: () => void
  haptic?: boolean
  stopPropagation?: boolean
}

const props = withDefaults(defineProps<ButtonProps>(), {
  text: '',
  type: "default",
  size: 'large',
  image: '',
  disabled: false,
  onClick: () => {},
  haptic: true,
  stopPropagation: true,
});

const buttonStyle = computed(() => `button_${props.type}`)
const buttonSize = computed(() => `button_size_${props.size}`)
const isIconOnly = computed(() => !!props.image && props.text === '')
const buttonClass = computed(() => {
  const result = `button ${buttonStyle.value} ${buttonSize.value}`
  return isIconOnly.value ? result + ' ' + 'button_icon-only' : result
})

const handleClick = (e: Event) => {
  if (props.stopPropagation) e.stopPropagation()
  if (props.haptic) hapticsService.triggerImpactHapticEvent('light')
  props.onClick()
}
</script>

<template>
  <button :class="buttonClass" :disabled="disabled" @click="handleClick">
    <div class="button__content">
      <slot name="pre-content"></slot>
      <div
        v-if="imageClass"
        class="icon-bg button__image"
        :class="[
          imageClass,
          imageClass === 'dollar-bg'
            ? 'text-shadow text-shadow_black'
            : '',
          imageClass === 'dollar-bg' && (size === 'xsmall' || size === 'small') && 'text-shadow_thin'
        ]"
      ></div>
      <img v-else-if="image" :src="image" class="w-6" />

      <span
        v-if="text"
        class="text-shadow text-shadow_black"
        :class="{
          'text-shadow_thin': size === 'xsmall' || size === 'small',
        }"
      >
        {{ text }}
      </span>
      <slot name="content"></slot>
    </div>
    <slot></slot>
  </button>
</template>

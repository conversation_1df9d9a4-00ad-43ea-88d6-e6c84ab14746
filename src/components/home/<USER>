<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';
import CountdownTimer from '@/components/UI/CountdownTimer.vue';
import VButton from '../UI/VButton.vue';
import NumberCounterAnimation from './NumberCounterAnimation.vue';
import { useStartFarming, useClaimFarming } from '@/services/client/useFarming.ts';
import { usePlayerState } from '@/services/client/usePlayerState';
import dayjs from 'dayjs';
import { useI18n } from 'vue-i18n';
import LeagueBlocker from '../UI/LeagueBlocker.vue';
import { useReward } from '@/composables/useReward';

const { t } = useI18n()

const BACKEND_TICKETS_DIVIDER = 1000

const { startFarming } = useStartFarming()
const { claimFarming, isPending: isClaiming } = useClaimFarming()
const { playerState } = usePlayerState()

const userLeagueNumber = computed(() => playerState.value!.leagueLevel ?? 1)
const isFarmingActive = computed(() => playerState.value?.farming)
const isFarmingEnded = ref(false)
const now = ref(Math.floor(dayjs().valueOf() / 1000)) // backend timestamps are in seconds

const farmingData = computed(() => {
  if (!playerState.value?.farming) {
    return {
      timeLeftInSeconds: 0,
      ticketsFarmed: 0,
      ticketsToFarm: 0
    }
  }
  const endsAt = playerState.value.farming.endsAt
  const staredAt = playerState.value.farming?.farmedTicketsLastUpdate ?? playerState.value.farming.startedAt
  const nowChecked = Math.max(staredAt, now.value) // sometimes now is less than startedAt when you press the farm button
  const changeRate = playerState.value.farming.changeRate || 0;

  const timeLeftInSeconds = Math.max(0, endsAt - nowChecked)
  const continueAt = timeLeftInSeconds === 0 ? endsAt : nowChecked
  const timePassed = continueAt - staredAt

  const farmedTicketsBeforeUpdate = playerState.value.farming.farmedTickets ?? 0
  const farmedTicketsAfterUpdate = timePassed * changeRate
  const farmedTicketsTotal = farmedTicketsBeforeUpdate + farmedTicketsAfterUpdate

  const ticketsFarmed = Math.floor(farmedTicketsTotal / BACKEND_TICKETS_DIVIDER); // backend tickets are in thousands
  const ticketsToFarm = Math.floor(timeLeftInSeconds * changeRate / BACKEND_TICKETS_DIVIDER); // backend tickets are in thousands

  return {
    timeLeftInSeconds: timeLeftInSeconds,
    ticketsFarmed: ticketsFarmed,
    ticketsToFarm: ticketsToFarm
  }
})

const stopFarming = () => {
  isFarmingEnded.value = true
}

const { showReward } = useReward()

const handleClaimFarming = () => {
  claimFarming().then((data) => {
    isFarmingEnded.value = false
    showReward({
      type: 'tickets',
      value: data.farmedTickets
    })
  })
}

const updateNowTime = () => {
  if (isFarmingActive.value) {
    now.value = Math.floor(dayjs().valueOf() / 1000)
  }
}

onMounted(() => {
  window.addEventListener('visibilitychange', updateNowTime)
})

onUnmounted(() => {
  window.removeEventListener('visibilitychange', updateNowTime)
})
</script>

<template>
  <div class="farming-container" :class="{ 'farming-container__active': isFarmingActive }">
    <template v-if="!isFarmingActive">
      <LeagueBlocker
        :userLeague="userLeagueNumber"
        feature="farming"
        placement="bottom"
      >
        <VButton
          class="!w-[109px]"
          type="success"
          size="small"
          :text="t('actions.startFarming')"
          @click="startFarming"
        />
      </LeagueBlocker>
    </template>
    <template v-else>
      <div class="text-[18px] leading-[25px] text-[#FFEE55] text-shadow text-shadow_black">
        <NumberCounterAnimation
          :secondsToFarm="farmingData.timeLeftInSeconds"
          :ticketsFarmed="farmingData.ticketsFarmed"
          :ticketsToFarm="farmingData.ticketsToFarm"
        />
      </div>
      <CountdownTimer
        v-if="!isFarmingEnded && farmingData.timeLeftInSeconds > 0"
        timer-id="farming"
        class="text-[14px] text-[#0065A6]"
        :totalSeconds="farmingData.timeLeftInSeconds"
        @countdown-finished="stopFarming"
      />
      <VButton
        class="!w-[109px]"
        v-else
        type="accent"
        size="small"
        :text="t('actions.claim')"
        :disabled="isClaiming"
        @click="handleClaimFarming"
      />
    </template>
  </div>
</template>

<style lang="scss">
.farming-container {
  min-width: 127px;
  padding: 18px 9px 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  row-gap: 4px;
  border-radius: 9px 9px;

  &__active {
    background-color: #FFFFFF73;
  }
}
</style>

<script setup lang="ts">
import { useShopItems } from '@/services/client/useShopItems'
import type { ProgressiveOfferItem } from '@/services/openapi/types.gen'
import { formatNumberWithSeparator } from '@/utils/number'
import { computed, useTemplateRef, watch } from 'vue'
import LoaderText from '../../LoaderText.vue'
import VButton from '../../UI/VButton.vue'

import chainMediumImage from '@/assets/images/temp/locks/chain-big.png'
import lockImage from '@/assets/images/temp/locks/lock.png'
import moonBannerImage from '@/assets/images/temp/scrolling-offer/banner-moon.png'
import oceanBannerImage from '@/assets/images/temp/scrolling-offer/banner-ocean.png'

import { REWARD_TO_IMAGE } from '@/composables/useIconImage'

import RewardItem from '@/components/RewardItem.vue'
import VOverlay from '@/components/VOverlay.vue'
import { CURRENCY_TO_IMAGE_CLASS } from '@/composables/useIconImage'
import { usePurchase } from '@/composables/usePurchase'
import { useToast } from '@/stores/toastStore'
import CountdownTimerManual from '../../UI/CountdownTimerManual.vue'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const REWARD_CLAIMED_CLASS = 'scrolling-offer__step_available'

const EVENT_ID_TO_BANNER_IMAGE: Record<number, string> = {
  10002: moonBannerImage,
  10003: oceanBannerImage
}

const EVENT_ID_TO_CARD_CLASS: Record<number, string> = {
  10002: 'scrolling-offer__step_moon',
  10003: 'scrolling-offer__step_ocean'
}

defineProps<{
  days?: number
  hours?: number
  minutes: number
  seconds: number
}>()

const emit = defineEmits(['close'])

const closeBanner = () => {
  emit('close')
}

const { showToast } = useToast()

const { shopItems, isLoading } = useShopItems()
const progressiveOffer = computed(() =>
  shopItems.value?.progressiveOffers.find(
    offer => (offer.id === 10002 || offer.id === 10003) && !offer.isCompleted
  )
)
const steps = computed(() => progressiveOffer.value?.items ?? [])

const rewardRefs = useTemplateRef('rewardRefs')

const scrollToReward = () => {
  setTimeout(() => {
    if (!rewardRefs.value) return
    const rewardEl = rewardRefs.value!.find(el => el.classList.contains(REWARD_CLAIMED_CLASS))
    if (rewardEl) {
      rewardEl.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    }
  }, 200)
}

watch(
  rewardRefs,
  () => {
    scrollToReward()
  },
  { immediate: true }
)

const { purchaseProgressive, isPendingPurchaseProgressive } = usePurchase()

const purhcase = (item: ProgressiveOfferItem) => {
  purchaseProgressive(progressiveOffer.value!.id, item.price!, item.rewards)
    .then(scrollToReward)
    .catch(reason => {
      if (reason?.message === 'NOT_ENOUGH_FUNDS') {
        showToast('Not enough funds', 'warning')
      }
    })
}
</script>

<template>
  <div class="close-button-wrapper">
    <div class="close-button !relative" @click="closeBanner"></div>
  </div>
  <div class="scrolling-offer" v-if="progressiveOffer">
    <div class="scrolling-offer__top">
      <img
        class="scrolling-offer__banner"
        :src="EVENT_ID_TO_BANNER_IMAGE[progressiveOffer.id]"
        alt="event banner"
      />
      <div class="scrolling-offer__timer">
        <span v-text="t('endsIn')"></span>
        <CountdownTimerManual
          :days="days"
          :hours="hours"
          :minutes="minutes"
          :seconds="seconds"
          digital
        />
      </div>
    </div>
    <div class="scrolling-offer__bottom-wrapper">
      <div class="scrolling-offer__bottom">
        <div
          v-for="step in steps"
          :key="step.idx"
          class="scrolling-offer__step-wrapper"
          :class="{
            [REWARD_CLAIMED_CLASS]: step.isAvailable
          }"
          ref="rewardRefs"
        >
          <div
            class="scrolling-offer__step"
            :class="{
              'scrolling-offer__step_locked': !step.isAvailable && !step.isPurchased,
              [EVENT_ID_TO_CARD_CLASS[progressiveOffer.id]]: true
            }"
          >
            <div class="flex items-center justify-center w-full gap-x-3 h-[50%] px-2">
              <RewardItem
                v-for="reward in step.rewards"
                :key="reward.type"
                class="w-[22%] h-full"
                value-class="translate-y-[9px]"
                :type="reward.type"
                :amount="reward.value"
                :image="REWARD_TO_IMAGE[reward.type] ?? ''"
              />
            </div>
            <VButton
              v-if="!step.isPurchased"
              class="!w-[40%] pointer-events-auto"
              :class="{
                '!pointer-events-none': !step.isAvailable
              }"
              :image-class="step.price?.amount ? CURRENCY_TO_IMAGE_CLASS[step.price!.currency] : ''"
              :text="
                step.price?.amount
                  ? formatNumberWithSeparator(step.price.amount)
                  : t('free')
              "
              :type="step.price?.amount > 0 ? 'success' : 'accent'"
              size="medium"
              :disabled="isPendingPurchaseProgressive"
              @click="() => step.isAvailable && purhcase(step)"
            >
            </VButton>
            <div v-else class="h-[42px] flex items-center">
              <div class="check-icon purchased-mark"></div>
            </div>
          </div>
          <div v-if="!step.isAvailable && !step.isPurchased" class="scrolling-offer__step-lock">
            <div class="scrolling-offer__step-lock-inner">
              <img
                class="w-[140px] -bottom-[18px] absolute left-1/2 -translate-x-[95%]"
                :src="chainMediumImage"
                alt="chain"
              />
              <img
                class="w-[140px] -bottom-[18px] absolute right-1/2 translate-x-[95%] -scale-x-100"
                :src="chainMediumImage"
                alt="chain"
              />
              <img
                class="w-[34px] bottom-[0] absolute left-1/2 -translate-x-1/2"
                :src="lockImage"
                alt="lock"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <VOverlay
      :is-open="isLoading || isPendingPurchaseProgressive"
      class="flex items-center justify-center"
    >
      <LoaderText is-loading class="text-[24px] text-[#6db0ed]" />
    </VOverlay>
  </div>
</template>

<style lang="scss">
.close-button-wrapper {
  position: absolute;
  top: calc(var(--inset-top) + 11px);
  right: 11px;
  z-index: 10000;
  background: linear-gradient(180deg, #fc7100 0%, #ff7a21 100%);
  border-radius: 50%;
  padding: 4px;
  border: 3px solid #a62603;
  box-shadow: 0px -4px 0px 0px #ec4e32 inset;

  .close-button {
    --close-btn-background-color: #a62603;
  }
}

.scrolling-offer {
  position: relative;

  display: flex;
  flex-direction: column;

  width: 95%;
  height: 85%;

  &__top {
    position: relative;
    flex: 0 0 130px;
  }

  &__bottom-wrapper {
    flex: 1;
    overflow: hidden;
  }

  &__bottom {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    pointer-events: none;
  }

  &__step-lock {
    position: absolute;
    height: 1px;
    z-index: 1;
    bottom: 10px;
    right: 70px;
    transform: translateX(-50%);

    &-inner {
      transform: rotate(-30deg);
    }
  }

  &__step-wrapper {
    position: relative;
    height: 33.3333%;
    width: 100%;
    padding-bottom: 13px;

    &:last-child {
      overflow: hidden;
    }

    &:nth-child(4n + 1) {
      .scrolling-offer__step {
        &_moon {
          background-image: url('@/assets/images/temp/scrolling-offer/moon-card-1.png');
        }

        &_ocean {
          background-image: url('@/assets/images/temp/scrolling-offer/ocean-card-1.png');
        }
      }
    }

    &:nth-child(4n + 2) {
      .scrolling-offer__step {
        &_moon {
          background-image: url('@/assets/images/temp/scrolling-offer/moon-card-2.png');
        }

        &_ocean {
          background-image: url('@/assets/images/temp/scrolling-offer/ocean-card-2.png');
        }
      }
    }

    &:nth-child(4n + 3) {
      .scrolling-offer__step {
        &_moon {
          background-image: url('@/assets/images/temp/scrolling-offer/moon-card-3.png');
        }

        &_ocean {
          background-image: url('@/assets/images/temp/scrolling-offer/ocean-card-3.png');
        }
      }
    }

    &:nth-child(4n) {
      .scrolling-offer__step {
        &_moon {
          background-image: url('@/assets/images/temp/scrolling-offer/moon-card-4.png');
        }

        &_ocean {
          background-image: url('@/assets/images/temp/scrolling-offer/ocean-card-4.png');
        }
      }
    }
  }

  &__step {
    width: 100%;
    height: 100%;
    border: 5px solid #ffd634;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    border-radius: 18px;

    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;

    &_locked {
      filter: brightness(0.5) contrast(130%);
    }
  }

  &__timer {
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 5px;
    background-color: #07070773;
    padding: 5px 10px;
    font-size: 14px;
    z-index: 2;
  }

  &__banner {
    position: absolute;
    top: -35%;
    left: 50%;
    transform: translateX(-50%);
    pointer-events: none;
    width: 110%;
    z-index: 1;
  }
}
</style>

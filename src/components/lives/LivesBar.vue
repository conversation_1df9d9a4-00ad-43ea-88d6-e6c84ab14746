<script setup lang="ts">
import BalanceItem from '@/components/UI/BalanceItem.vue'
import CountdownTimerManual from '@/components/UI/CountdownTimerManual.vue'
import { useLifesStore } from '@/stores/livesStore'
import { storeToRefs } from 'pinia'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

type LifesBarSize = 'small' | 'medium'

const { t } = useI18n()

withDefaults(
  defineProps<{
    showTimer?: boolean
    size?: LifesBarSize
  }>(),
  {
    showTimer: false,
    size: 'medium'
  }
)

const emit = defineEmits(['add'])

const lifesStore = useLifesStore()
const { lives, maxLives, isUnlimitedLives, days, hours, minutes, seconds } = storeToRefs(lifesStore)

const canAddMoreLives = computed(() => {
  return !isUnlimitedLives.value && lives.value < maxLives.value
})
</script>

<template>
  <div class="relative flex flex-col items-center">
    <BalanceItem
      class="lives-item"
      :iconName="isUnlimitedLives ? 'unlimited-heart-bg' : 'heart-bg'"
      :barClass="`
        lives-item__bar
        lives-item__bar_${size}
      `"
      :balanceClass="`
        lives-item__balance tracking-normal
        lives-item__balance_${size}
      `"
      :imageClass="`lives-item__image_${size}`"
      :add-button="canAddMoreLives"
      @add="() => emit('add')"
    >
      <template #count>
        <p
          v-if="!isUnlimitedLives"
          class="text-[16px] text-shadow text-shadow_black text-shadow_thin"
        >
          {{ lives }}
        </p>
      </template>
      <CountdownTimerManual
        v-if="isUnlimitedLives || lives < maxLives"
        class="text-shadow text-shadow_shadow-only"
        :days="days"
        :hours="hours"
        :minutes="minutes"
        :seconds="seconds"
        digital
      />
      <p v-else class="text-[14px] text-shadow text-shadow_shadow-only">
        {{ t('lives.full') }}
      </p>
    </BalanceItem>
  </div>
</template>

<style lang="scss">
.lives-item {
  &__bar {
    padding: 0 25px;

    &_small {
      height: 20px;
      padding: 0 20px;

      &::after {
        background-color: #0826739c;
      }
    }
  }

  &__balance {
    &_small {
      position: relative;
      top: 0.5px;
      font-size: 13px;
      line-height: 13px;
    }
  }

  &__image {
    &_small {
      width: 30px;
      height: 30px;
    }
  }
}

.lifes-timer {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: max-content;

  &_small {
    bottom: -14px;
    color: #0065a6;
    font-size: 10px;
    line-height: 14px;
  }

  &_large {
    bottom: -32px;
    color: #1e4073;
    font-size: 20px;
    line-height: 27px;
  }
}
</style>

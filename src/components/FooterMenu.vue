<script setup lang="ts">
import { sendAnalyticsEvent } from '@/utils/analytics'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

import airdorpImage from '@/assets/images/temp/airdrop.png'
import boostImage from '@/assets/images/temp/boost.png'
import earnImage from '@/assets/images/temp/chest.png'
import friendsImage from '@/assets/images/temp/friends.png'
import storeImage from '@/assets/images/temp/store.png'

import MenuButton from '@/components/MenuButton.vue'
import NewBadge from '@/components/UI/NewBadge.vue'
import RedDotBadge from '@/components/UI/RedDotBadge.vue'
import { usePlayerState } from '@/services/client/usePlayerState'
import {
  useHasUserSeenShop,
  useHasUserSeenSkins,
  useHasUserSeenTasks
} from '@/stores/hasUserSeenStore'

const { playerState } = usePlayerState()
const hasUserSeenSkins = useHasUserSeenSkins()
const hasUserSeenTasks = useHasUserSeenTasks()
const hasUserSeenShop = useHasUserSeenShop()

const ticketsUnclaimed = computed(() => playerState.value?.ticketsUnclaimed ?? 0)
const tasksUnclaimed = computed(() => {
  return !!playerState.value?.hasUnclaimedTasks || !!playerState.value?.hasUnclaimedDailyTasks
})
const achievementsUnclaimed = computed(() => {
  return !!playerState.value?.hasUnclaimedAchievements
})

const { t } = useI18n()

const router = useRouter()
</script>

<template>
  <div class="footer-menu" @click.stop>
    <MenuButton :text="t('friends.title')" @click="() => router.push('/menu/friends')">
      <img class="w-[51px] -top-[26px]" :src="friendsImage" alt="friends image" />
      <RedDotBadge v-if="ticketsUnclaimed > 0" class="top-[0px] -right-[6px]" />
    </MenuButton>
    <MenuButton :text="t('me.skins')" @click="() => router.push('/menu/me')">
      <img class="w-[46px] -top-[33px]" :src="boostImage" alt="boost image" />
      <NewBadge v-if="!hasUserSeenSkins.hasSeenNewSkins" class="!-bottom-[8px]" />
    </MenuButton>
    <MenuButton
      :text="t('earn.title')"
      @click="() => (sendAnalyticsEvent('quest_viewed'), router.push('/menu/earn'))"
    >
      <img class="w-[51px] -top-[26px]" :src="earnImage" alt="earn image" />
      <RedDotBadge v-if="tasksUnclaimed || achievementsUnclaimed" class="top-[0px] -right-[6px]" />
      <NewBadge v-if="!hasUserSeenTasks.hasSeenNewTasks" class="!-bottom-[8px]" />
    </MenuButton>
    <MenuButton
      :text="t('shop.title')"
      @click="() => (hasUserSeenShop.markShopAsSeen(), router.push('/menu/shop'))"
    >
      <img class="w-[46px] -top-[26px]" :src="storeImage" alt="shop image" />
      <!-- <NewBadge class="!-bottom-[8px]" /> -->
    </MenuButton>
    <MenuButton :text="t('airdrop.button')" @click="() => router.push('/menu/drop')">
      <img class="w-[46px] -top-[26px]" :src="airdorpImage" alt="airdrop image" />
    </MenuButton>
  </div>
</template>

<style lang="scss" scoped>
.footer-menu {
  width: 100%;
  padding: 26px 10px max(var(--inset-bottom), 26px);

  display: flex;
  justify-content: center;
  align-items: center;
  column-gap: 2px;
}

.soon-tag {
  padding: 0 4px;
  border: 2px solid #b50000;
  border-radius: 12px 12px 16px 16px;
  background: radial-gradient(circle at top, #ffcaca 0%, #e10000 60%);
  box-shadow:
    #640000 0 3px,
    #ffffff40 0 2px 1px -2px;

  font-size: 11px;
  font-weight: 700;
}
</style>

<script setup lang="ts">
import LoaderText from '@/components/LoaderText.vue';
import SkinCollectionWindow from '@/components/skins/SkinCollectionWindow.vue';
import BalanceItem from '@/components/UI/BalanceItem.vue';
import ProgressBar from '@/components/UI/ProgressBar.vue';
import { useIconImage } from '@/composables/useIconImage';
import { getCurrencyRealAmount } from '@/constants/currency';
import { useSkinCollections } from '@/services/client/useSkinCollections';
import { formatNumberToShortString } from '@/utils/number';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { COLLECTION_ID_TO_IMAGE } from '@/constants/collections'

const NULL_COLLECTION_ID = -1

const { t } = useI18n()
const { getImageClass } = useIconImage()
const { isLoading, collections } = useSkinCollections()

const collectionInfoId = ref<number>(NULL_COLLECTION_ID)

const collectionInfo = computed(() => {
  if (collectionInfoId.value === NULL_COLLECTION_ID) return null
  return collections.value.find(collection => collection.id === collectionInfoId.value) ?? null
})
</script>

<template>
  <div class="skin-collections">
    <div class="w-full h-full flex justify-center items-center" v-if="isLoading">
      <LoaderText isLoading />
    </div>
    <div class="skin-collections__list">
      <div
        class="skin-collection"
        v-for="collection in collections"
        :key="collection.id"
        @click="collectionInfoId = collection.id"
      >
        <div class="skin-collection__name text-shadow text-shadow_shadow-only">
          {{ t(`skins.collections.${collection.id}`) }}
        </div>
        <div class="skin-collection__image">
          <img class="w-full" :src="COLLECTION_ID_TO_IMAGE[collection.id]" />
        </div>
        <div class="skin-collection__info">
          <template v-if="!collection.isClaimed">
            <ProgressBar
              class="skin-collection__progress"
              inner-wrapper-class="skin-collection__progress-inner-wrapper"
              :progress="collection.ownedSkinsCount"
              :goal="collection.totalSkinsCount"
            >
              <p class="text-shadow text-shadow_black text-shadow_thin">
                {{ collection.ownedSkinsCount }}/{{ collection.totalSkinsCount }}
              </p>
            </ProgressBar>
            <div
              class="skin-collection__reward-container !left-auto"
            >
              <BalanceItem
                class="skin-collection__reward"
                image-class="skin-collection__reward-image"
                :iconName="getImageClass(collection.reward.type)"
                barClass="skin-collection__reward-bar"
                balanceClass="skin-collection__reward-balance"
              >
                {{
                  formatNumberToShortString(
                    getCurrencyRealAmount(collection.reward.value, collection.reward.type)
                  )
                }}
              </BalanceItem>
            </div>
          </template>
          <div v-else class="text-[14px] leading-[22px] text-[#0DFF00] text-shadow text-shadow_shadow-only">
            {{ t('completed') }}
          </div>
        </div>
      </div>
    </div>
    <SkinCollectionWindow
      :collection="collectionInfo"
      @close="collectionInfoId = NULL_COLLECTION_ID"
    />
  </div>
</template>

<style lang="scss">
.skin-collections {
  min-height: 100%;
  width: inherit;
  padding: 22px 20px;

  &__list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }

  .skin-collection {
    position: relative;
    background-color: #D9D9D926;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    row-gap: 5px;
    padding: 0 3px 3px;

    &__name {
      white-space: nowrap;
      font-size: 16px;
      line-height: 22px;
      text-align: center;
    }

    &__image {
      width: 95%;
      aspect-ratio: 59 / 56;
    }

    &__info {
      height: 22px;
      width: 90%;
      display: flex;
      justify-content: center;
    }

    &__progress {
      flex: 1;
      height: 22px;

      &-inner-wrapper {
        padding: 2px;
      }
    }

    &__reward {
      &-image {
        width: 28px;
        height: 28px;

        &-heart {
          left: -3px;
        }
      }

      &-bar {
        height: 22px;
        padding-left: 17px;
        padding-right: 8px;
      }

      &-balance {
        font-size: 15px;
      }
    }
  }
}
</style>

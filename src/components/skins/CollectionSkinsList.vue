<script setup lang="ts">
import { useSkinCollection } from '@/services/client/useSkinCollections';
import SkinsList from './SkinsList.vue'

const props =defineProps<{
  collectionId: number
}>()

const { isLoading, skins } = useSkinCollection(props.collectionId)
</script>

<template>
  <SkinsList
    class="collection-skins-list"
    :skins="skins"
    :columns="3"
    :is-loading="isLoading"
    show-name
    hide-not-owned
  />
</template>

<style lang="scss">
.collection-skins-list {
  aspect-ratio: 1 / 1;
}
</style>

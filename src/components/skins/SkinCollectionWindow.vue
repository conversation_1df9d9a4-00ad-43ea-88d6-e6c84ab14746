<script setup lang="ts">
import CollectionSkinsList from '@/components/skins/CollectionSkinsList.vue';
import BalanceItem from '@/components/UI/BalanceItem.vue';
import ModalWindow from '@/components/UI/ModalWindow.vue';
import VButton from '@/components/UI/VButton.vue';
import { useIconImage } from '@/composables/useIconImage';
import { getCurrencyRealAmount } from '@/constants/currency';
import type { RewardInfo, SkinsCollection } from '@/services/openapi';
import { formatNumberToShortString } from '@/utils/number';
import { useI18n } from 'vue-i18n';
import { sendAnalyticsEvent } from '@/utils/analytics'
import { useRouter } from 'vue-router'
import { useReward } from '@/composables/useReward';
import { useClaimCollectionReward } from '@/services/client/useClaimCollectionReward';

const router = useRouter()
const { t } = useI18n()
const { getImageClass } = useIconImage()
const { showReward } = useReward()
const { claimCollectionReward } = useClaimCollectionReward()

defineProps<{
  collection: SkinsCollection | null
}>()

const emits = defineEmits(['close'])

const goToShop = () => {
  sendAnalyticsEvent('go_to_shop', { from: 'skin-store' })
  router.push({ name: 'shop', query: { scrollTo: 'lootbox' } })
}

const claimReward = (id: number, reward: RewardInfo) => {
  claimCollectionReward(id).then(() => {
    showReward(reward)
  })
}
</script>

<template>
  <ModalWindow
    class="skin-collection-window"
    :title="t(`skins.collections.${collection?.id}`)"
    :is-open="collection !== null"
    @close="() => emits('close')"
  >
    <template v-if="collection">
      <BalanceItem
        class="skin-collection-window__reward mx-auto mb-4"
        image-class="skin-collection-window__reward-image"
        barClass="skin-collection-window__reward-bar"
        balanceClass="skin-collection-window__reward-balance"
        :iconName="getImageClass(collection.reward.type)"
      >
        {{
          formatNumberToShortString(
            getCurrencyRealAmount(collection.reward.value, collection.reward.type)
          )
        }}
      </BalanceItem>
      
      <CollectionSkinsList class="mb-4" v-if="collection" :collection-id="collection.id" />
      <VButton
        v-if="collection.ownedSkinsCount < collection.totalSkinsCount"
        class="mx-auto min-w-[245px]"
        type="success"
        :text="t('skins.requirenments.box')"
        @click="goToShop"
      />
      <VButton
        v-else-if="!collection.isClaimed"
        class="mx-auto min-w-[245px]"
        type="success"
        :text="t('actions.claim')"
        @click="() => claimReward(collection!.id, collection!.reward)"
      />
      <div v-else class="text-[24px] leading-[51px] text-center">
        {{ t('claimed') }}
      </div>
    </template>
  </ModalWindow>
</template>

<style lang="scss">
.skin-collection-window {
  max-height: 90%;
  margin-top: 20px;
  display: flex;
  flex-direction: column;

  .modal-window__title {
    font-size: 32px;
    line-height: 38px;
  }

  &__reward {
    &-image {
      width: 36px;
      height: 36px;

      &-heart {
        left: -3px;
      }
    }

    &-bar {
      height: 28px;
      padding-left: 24px;
    }

    &-balance {
      font-size: 20px;
    }
  }
}
</style>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'

import { usePlayerState } from '@/services/client/usePlayerState'

import eventBanner from '@/assets/images/temp/battle-event/banner.png'
import eventImage from '@/assets/images/temp/battle-event/image.png'
import type { ButtonProps } from '@/components/UI/VButton.vue'
import EventBanner from '@/components/events/EventBanner.vue'

import CountdownTimerManual from '@/components/UI/CountdownTimerManual.vue'
import { useEventWelcomeCheck } from '@/composables/useEventWelcomeCheck.ts'
import { useWindowQueue } from '@/composables/useWindowQueue.ts'
import { getCurrencyRealAmount } from '@/constants/currency.ts'
import { usePlayerReceivedEventReward } from '@/services/client/usePlayerFlag.ts'
import { usePurchaseSkin } from '@/services/client/useSkins.ts'
import leaguesService from '@/services/local/leagues.ts'
import { useCommunityBattleStore } from '@/stores/communityBattleStore.ts'
import { sendAnalyticsEvent } from '@/utils/analytics.ts'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import type { Skin } from '@/services/openapi'
import { useReward } from '@/composables/useReward'

import TeamSelect from '@/components/events/battle-event/TeamSelect.vue'
import EventResults from '@/components/events/battle-event/EventResults.vue'

const HAS_SEEN_EVENT_BANNER_KEY = 'hasSeenCommunityBattleBanner'

const { t } = useI18n()
const router = useRouter()
const {
  isOpen: isOpenBanner,
  openWindowInQueue: openEventWindowInQueue,
  closeWindowInQueue: closeEventWindowInQueue
} = useWindowQueue('community-battle-window')

defineProps<{
  isOpen: boolean
}>()
const emit = defineEmits(['close'])

const communityBattleStore = useCommunityBattleStore()
const { showReward } = useReward()
const { checkWelcomeBanner } = useEventWelcomeCheck()

const { playerState, refetchPlayerState } = usePlayerState()
const { onPlayerRecievedCommunityBattleReward } = usePlayerReceivedEventReward()

const isReward = computed(() => playerState.value?.battleEventReward?.reward?.amount !== undefined)
watch(
  isReward,
  async (newVal) => {
    if (!newVal) return
    openEventWindowInQueue()
  }
)

const close = () => {
  emit('close')
}

const closeBanner = () => {
  if (isReward.value) {
    const coinsCollected = playerState.value?.battleEventReward?.coinsCollected ?? 0
    const reward = playerState.value!.battleEventReward?.reward?.amount ?? 0
    const currency = playerState.value!.battleEventReward?.reward?.currency ?? 'hard'

    showReward({
      type: currency,
      value: reward
    }, { isAlreadyOnPlayerState: true })
    sendAnalyticsEvent('event_end', {
      event: 'community_battle',
      total_points: coinsCollected,
      reward_type: currency,
      reward_amount: getCurrencyRealAmount(reward, currency)
    })
    onPlayerRecievedCommunityBattleReward()
    closeEventWindowInQueue()
  } else {
    closeEventWindowInQueue()
  }
}

onMounted(async () => {
  const isOpenRewardBanner = playerState.value?.battleEventReward?.reward?.amount !== undefined
  if (isOpenRewardBanner) {
    openEventWindowInQueue()
  } else {
    const isOpenWelcomeBanner = await checkWelcomeBanner(
      HAS_SEEN_EVENT_BANNER_KEY,
      communityBattleStore.isCommunityBattleActive,
      leaguesService.hasAccess(playerState.value!.leagueLevel ?? 1, 'battleEvent')
    )
    if (isOpenWelcomeBanner) {
      if (playerState.value?.battleEvent?.hasMetRequirement) {
        router.push({
          name: 'battle-event'
        })
      } else {
        openEventWindowInQueue()
      }
    }
  }
})

const { purchaseSkin: purchaseSkinMutation, isPending: isSkinPurchasePending } = usePurchaseSkin()
const selectedSkin = ref<Skin | null>(null)

const purchaseSkin = (skin: Skin) => {
  purchaseSkinMutation(skin.id, skin.multiplier).then(() => {
    showReward({
      type: 'skin',
      value: skin.id,
      multiplier: skin.multiplier
    }, { isAlreadyOnPlayerState: true })
    // should update event info
    refetchPlayerState()
    closeEventWindowInQueue()
    close()
    router.push({
      name: 'battle-event'
    })
  })
}

const eventButtons = computed<Array<ButtonProps>>(() => {
  return isReward.value
    ? [{ text: t('actions.claim'), type: 'accent', onClick: closeBanner }]
    : [{ text: t('actions.lets_go'), disabled: selectedSkin.value === null, type: 'accent', onClick: () => purchaseSkin(selectedSkin.value!) }]
})
</script>

<template>
  <EventBanner
    class="community-battle-banner w-full h-full"
    :isOpen="isOpen || isOpenBanner"
    :buttons="eventButtons"
    :banner="eventBanner"
    :image="eventImage"
    :instruction-type="'battle-event-instruction'"
    :is-loading="isSkinPurchasePending"
    @close="isOpen ? close() : closeBanner()"
    showCloseButton
  >
    <template #details>
      <!-- Event Timer -->
      <div v-if="communityBattleStore.isCommunityBattleActive || isReward" class="community-battle-banner__timer">
        <h1
          class="text-white text-center text-[12px] leading-[17px]"
        >
          <template v-if="isReward">
            {{ t('event_ended') }}
          </template>
          <template v-else>
            <i18n-t
              keypath="event_ends_at"
            >
              <template v-slot:time>
                <CountdownTimerManual
                  class="text-[12px] leading-[16px] text-[#FFFFFF]"
                  :days="communityBattleStore.days"
                  :hours="communityBattleStore.hours"
                  :minutes="communityBattleStore.minutes"
                  :seconds="communityBattleStore.seconds"
                  digital
                />
              </template>
            </i18n-t>
          </template>
        </h1>
      </div>
    </template>

    <!-- Reward Banner -->
    <template v-if="isReward">
      <EventResults :selectedSkin="selectedSkin" />
    </template>

    <!-- Requirement Banner -->
    <template v-else>
      <TeamSelect
        :selectedSkin="selectedSkin"
        @select-skin="(skin: Skin) => selectedSkin = skin"
      />
    </template>
  </EventBanner>
</template>

<style lang="scss">
.community-battle-banner {
  top: 0;
  --event-background: linear-gradient(180deg, #23D0F9 13%, #0074C6 100%);

  .event-banner {
    transform: translateY(20px);
    // border: 6px solid #ffd634;
    border: 6px solid #1A5FA1;
    width: auto;
    max-width: 360px;
    height: 80%;
    max-height: 600px;

    .close-button {
      --close-btn-background-color: #2666A4;
    }

    &__banner {
      width: 85%;
      top: -15px;
    }

    &__content {
      min-height: 305px;
      flex: 1 1 auto;
      display: flex;
      flex-direction: column;
      row-gap: 12px;
    }

    &__description {
      flex: 1 1 auto;
      display: flex;
      flex-direction: column;
      row-gap: 8px;
      color: white;
    }
  }

  &__box {
    position: relative;
    width: 100%;
    background: #00EEFFB2;
    border-radius: 8px;
  }

  &__timer {
    padding: 3px 8px 3px 10px;
    position: relative;
    background: #003579;
    border-radius: 5px;
  }

  &__bar {
    height: 32px;
    padding-left: 28px;

    &::after {
      background: #00EEFFB2;
    }
  }

  &__balance {
    font-size: 24px;
  }
}
</style>

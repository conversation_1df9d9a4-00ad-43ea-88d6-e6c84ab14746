<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import type { JackpotReward, RewardType } from '@/services/openapi';
import ModalWindow from '@/components/UI/ModalWindow.vue';
import RewardItem from '@/components/RewardItem.vue';
import tonImage from '@/assets/images/temp/big-icons/ton-l.png'
import wheelSpins from '@/assets/images/temp/big-icons/wheel-spin-l.png'
import titleImage from '@/assets/images/temp/jackpot/rewards-title.png'

const REWARD_TO_IMAGE: Partial<Record<RewardType, string>> = {
  ton: tonImage,
  wheelSpins: wheelSpins,
}

const { t } = useI18n()

defineProps<{
  isOpen: boolean,
  jackpotRewards: JackpotReward[],
}>()

const emit = defineEmits(['close'])
</script>

<template>
  <ModalWindow
    class="jackpot-rewards-window"
    :is-open="isOpen"
    @close="() => emit('close')"
  >
    <img :src="titleImage" class="max-w-[263px] w-[80%] mx-auto" />
    <p class="text-base text-center font-extrabold mb-1">
      {{ t('jackpot.rewards_distribution') }}
    </p>
    <div class="space-y-2">
      <div
        v-for="jackpotReward, index in jackpotRewards.slice().sort((a, b) => a.amount - b.amount)"
        :key="index"
        class="flex justify-between items-center bg-[#0F45894D] rounded-[8px] px-7 h-[71px]"
      >
        <p class="text-[24px]">{{ t('jackpot.player', { amount: jackpotReward.amount }, jackpotReward.amount) }}</p>
        <RewardItem
          class="w-[94px] h-[90%]"
          :type="jackpotReward.reward.currency"
          :amount="jackpotReward.reward.amount"
          :image="REWARD_TO_IMAGE[jackpotReward.reward.currency] ?? ''"
          value-class="text-[16px]"
        />
      </div>
    </div>
  </ModalWindow>
</template>

<style lang="scss">
.jackpot-rewards-window {
  --modal-window-bg: linear-gradient(180deg, #F27FFD 0%, #951CD4 100%);
  --modal-window-inner-shadow: #F599FF;

  .modal-window__title {
    font-size: 32px;
    margin-bottom: 16px;
  }
}
</style>

<script setup lang="ts">
import { ref } from 'vue';
import { useIntersectionObserver } from '@vueuse/core';
import { useI18n } from 'vue-i18n';
import LeaderboardItem from '@/components/events/LeaderboardItem.vue';
import LoaderText from '@/components/LoaderText.vue';
import { useJackpotLeaders } from '@/services/client/useJackpotLeaders'
import { usePlayerProfileStore } from '@/stores/playerProfileStore';
import { useUser } from '@/composables/useUser';
import titleImage from '@/assets/images/temp/jackpot/prev-results.png'

const { t } = useI18n()

const profileStore = usePlayerProfileStore()

const {
  jackpotLeaders,
  userInfo,
  isUserInLeaders,
  isFetching: isFetchingJackpotLeaders,
  hasNextPage,
  fetchNextPage
} = useJackpotLeaders()

const { getUser } = useUser()
const userId = getUser().getId()

const nextPageTarget = ref(null)
useIntersectionObserver(
  nextPageTarget,
  ([{ isIntersecting }]) => {
    if (isIntersecting) {
      fetchNextPage()
    }
  },
)
</script>

<template>
  <div class="flex flex-col items-center justify-center">
    <img :src="titleImage" class="w-[281px] h-[34px] mb-1" />
    <div class="text-[15px] leading-[27px] text-center font-extrabold mb-1">
      {{ t('jackpot.results_description') }}
    </div>
    <div
      class="relative bg-[#0F45894D] rounded-[8px] flex-1 space-y-[6px] w-full overflow-y-auto px-[11px] py-[15px]"
    >
      <template v-if="jackpotLeaders.length">
        <LeaderboardItem
          v-for="winner, index in jackpotLeaders"
          :key="index"
          :username="winner.name"
          :rank-index="index"
          :league="winner.leagueLevel"
          :balance="winner.reward?.amount"
          :balance-type="winner.reward?.currency"
          :active="winner.id === userId"
          hideRank
          @click="() => profileStore.openProfile(winner.id)"
        />
        <LeaderboardItem
          v-if="!isUserInLeaders && userInfo"
          :username="userInfo.name"
          :rank-index="jackpotLeaders.length + 1"
          :league="userInfo.leagueLevel"
          :balance="userInfo.reward?.amount"
          :balance-type="userInfo.reward?.currency"
          active
          hideRank
          @click="() => profileStore.openProfile(userInfo!.id)"
        />
      </template>
      <div
        v-else-if="!isFetchingJackpotLeaders"
        class="absolute-center font-extrabold text-[16px] text-center"
      >
        {{ t('comingSoon') }}
      </div>
      <div v-if="hasNextPage && !isFetchingJackpotLeaders" ref="nextPageTarget" class="w-full h-[16px] mt-[5px]"></div>
      <LoaderText
        class="font-extrabold text-[16px] py-4 text-center"
        :class="{ 'absolute-center': !jackpotLeaders.length }"
        :isLoading="isFetchingJackpotLeaders"
      />
    </div>
  </div>
</template>

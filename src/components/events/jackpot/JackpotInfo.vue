<script setup lang="ts">
import { computed } from 'vue'
import { usePlayerState } from '@/services/client/usePlayerState'
import type { EventRewardCurrency, JackpotReward, RewardType } from '@/services/openapi'
import RewardItem from '@/components/RewardItem.vue'
import tonImage from '@/assets/images/temp/big-icons/ton-l.png'
import wheelSpins from '@/assets/images/temp/big-icons/wheel-spin-l.png'
import CountdownTimerManual from '@/components/UI/CountdownTimerManual.vue'
import { useJackpotStore } from '@/stores/jackpotStore'
import { usePurchase } from '@/composables/usePurchase'
import BalanceItem from '@/components/UI/BalanceItem.vue'
import MissionItem from '@/components/missions/MissionItem.vue'
import titleImage from '@/assets/images/temp/jackpot/title.png'
import couponImage from '@/assets/images/temp/jackpot/coupons.png'
import clockImage from '@/assets/images/temp/jackpot/clock.png'
import infoButtonImage from '@/assets/images/temp/exclamation-mark.png'
import { ref } from 'vue'
import JackpotRewardsWindow from '@/components/events/jackpot/JackpotRewardsWindow.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const REWARD_TO_IMAGE: Partial<Record<RewardType, string>> = {
  ton: tonImage,
  wheelSpins: wheelSpins,
}

const { playerState } = usePlayerState()
const { purchaseJackpotCoupon, isPendingPurchaseJackpotCoupon } = usePurchase()
const jackpotInfo = computed(() => playerState.value?.jackpotEvent)
const jackpotStore = useJackpotStore()
const isOpenRewardsWindow = ref(false)

const jackpotRewards = (() => {
  const rewards = jackpotInfo.value?.rewards.slice().reverse()
  const result = new Map<EventRewardCurrency, JackpotReward>()
  if (!rewards) return result

  for (const reward of rewards) {
    const existing = result.get(reward.reward.currency)
    const newReward = existing ? existing.reward.amount + reward.reward.amount : reward.reward.amount
    result.set(reward.reward.currency, {
      ...reward,
      reward: {
        ...reward.reward,
        amount: newReward
      }
    })
  }

  return result
})()

const couponsBalance = computed(() => {
  if (!jackpotInfo.value) return 0
  return jackpotInfo.value.couponTiers.reduce((acc, tier) => tier.isPurchased ? acc + tier.coupons : acc, 0)
})

const couponsMax = computed(() => {
  if (!jackpotInfo.value) return 0
  return jackpotInfo.value.couponTiers.reduce((acc, tier) => acc + tier.coupons, 0)
})

const sortedCouponTiers = computed(() => {
  if (!jackpotInfo.value) return []
  return jackpotInfo.value.couponTiers.slice().sort((a, b) => {
    let result = Number(a.isPurchased) - Number(b.isPurchased)
    if (result === 0) result = a.coupons - b.coupons
    if (result === 0) result = a.price.amount - b.price.amount
    return result
  })
})
</script>

<template>
  <div class="flex flex-col items-center">
    <img :src="titleImage" class="w-[257px]" />
    <template v-if="jackpotStore.isJackpotActive">
      <div class="jackpot-info__rewards">
        <RewardItem
          v-for="value in jackpotRewards.values()"
          :key="value.reward.currency"
          class="w-[25%] h-[70%]"
          :type="value.reward.currency"
          :amount="value.reward.amount"
          :image="REWARD_TO_IMAGE[value.reward.currency] ?? ''"
          value-class="text-[16px]"
        />
        <img
          :src="infoButtonImage"
          class="absolute top-[20px] right-[10px] w-[28px] h-[28px]"
          @click="isOpenRewardsWindow = true"
        />
      </div>
      <div class="flex items-center justify-evenly w-full mb-2">
        <div class="flex items-center gap-x-1">
          <img class="w-[20px]" :src="clockImage" alt="clock" />
          <CountdownTimerManual
            class="text-[24px] text-shadow text-shadow_black"
            :days="jackpotStore.daysCoupons"
            :hours="jackpotStore.hoursCoupons"
            :minutes="jackpotStore.minutesCoupons"
            :seconds="jackpotStore.secondsCoupons"
            digital
          />
        </div>
        <BalanceItem
          iconName="coupon-bg"
          class="jackpot-info__coupons"
          imageClass="jackpot-info__coupons-image"
          barClass="jackpot-info__coupons-bar"
          balanceClass="jackpot-info__coupons-balance text-gradient text-shadow text-shadow_black"
        >
          {{ couponsBalance }}/{{ couponsMax }}
        </BalanceItem>
      </div>
      <div v-if="jackpotInfo" class="bg-[#0F45894D] flex-1 overflow-y-auto space-y-2 w-full rounded-[8px] p-3">
        <div class="text-[14px] text-center font-extrabold">
          {{ t('jackpot.coupons_description') }}
        </div>
        <div
          class="relative w-full"
          v-for="offer in sortedCouponTiers"
          :key="offer.tier"
        >
          <MissionItem
            class="jackpot-info__coupon-tier"
            :id="offer.tier"
            :image="couponImage"
            :is-done="offer.isPurchased"
            :is-collected="offer.isPurchased"
            :description="t('jackpot.coupons', { amount: offer.coupons }, offer.coupons)"
            :price="offer.price"
            :is-purchasing="isPendingPurchaseJackpotCoupon"
            :is-price-disabled="!jackpotStore.canPurchaseCoupons"
            @purchase="(tier, price) => purchaseJackpotCoupon(tier, price)"
          />
          <div class="text-[20px] text-gradient text-shadow text-shadow_black font-black absolute left-[32px] bottom-[5px] -translate-x-1/2">
            +{{ offer.coupons }}
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="relative bg-[#0F45894D] rounded-[8px] flex-1 w-full">
        <div class="absolute-center font-extrabold text-[16px] text-center">
          {{ t('comingSoon') }}
        </div>
      </div>
    </template>
    <JackpotRewardsWindow
      v-if="jackpotInfo"
      :is-open="isOpenRewardsWindow"
      :jackpot-rewards="jackpotInfo.rewards"
      @close="isOpenRewardsWindow = false"
    />
  </div>
</template>

<style lang="scss">
.jackpot-info {
  &__rewards {
    position: relative;
    width: 308px;
    height: 115px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    margin-bottom: 4px;
    border-radius: 9px;

    background-image: url('@/assets/images/temp/jackpot/rewards-frame.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  &__coupons {
    &-image {
      width: 46px;
      height: 46px;
    }

    &-bar {
      height: 32px;
      padding-left: 34px;
      padding-right: 18px;
    }

    &-balance {
      font-size: 24px;
      font-weight: 900;
    }
  }

  &__coupon-tier {
    opacity: 1 !important;
  }
}
</style>

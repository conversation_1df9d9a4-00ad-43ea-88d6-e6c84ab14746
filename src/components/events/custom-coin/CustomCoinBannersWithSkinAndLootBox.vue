<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'

import { usePlayerState } from '@/services/client/usePlayerState'

import eventBanner from '@/assets/images/temp/custom-coin-event/banner.png'
import eventImage from '@/assets/images/temp/custom-coin-event/image.png'
import type { ButtonProps } from '../../UI/VButton.vue'
import SkinItem from '../../skins/SkinItem.vue'
import EventBanner from '../EventBanner.vue'

import convertIcon from '@/assets/images/temp/convert-icon.png'
import coinIcon from '@/assets/images/temp/custom-coin-event/coin.png'
import { formatNumberToShortString, formatNumberWithSeparator } from '@/utils/number'
import BalanceItem from '../../UI/BalanceItem.vue'

import boxImage from '@/assets/images/temp/big-icons/duckyBox.png'
import starImage from '@/assets/images/temp/currency/hard-coin.png'
import CountdownTimerManual from '@/components/UI/CountdownTimerManual.vue'
import LeaderboardItem from '@/components/events/LeaderboardItem.vue'
import { useEventPromoCheck, useEventWelcomeCheck } from '@/composables/useEventWelcomeCheck.ts'
import { useIconImage } from '@/composables/useIconImage'
import { usePurchase } from '@/composables/usePurchase.ts'
import { useUser } from '@/composables/useUser.ts'
import { useWindowQueue } from '@/composables/useWindowQueue.ts'
import { getCurrencyRealAmount } from '@/constants/currency.ts'
import { SKIN_ID_TO_IMAGE } from '@/constants/skins.ts'
import { useCustomCoinUserInfo } from '@/services/client/useGameEvent.ts'
import { usePlayerReceivedEventReward } from '@/services/client/usePlayerFlag.ts'
import { usePurchaseSkin, useSkinsList } from '@/services/client/useSkins.ts'
import leaguesService from '@/services/local/leagues.ts'
import { useCustomCoinStore } from '@/stores/customCoinStore.ts'
import { useToast } from '@/stores/toastStore.ts'
import { sendAnalyticsEvent } from '@/utils/analytics.ts'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import type { Skin } from '@/services/openapi'
import { useReward } from '@/composables/useReward'

const HAS_SEEN_EVENT_PROMO_BANNER_KEY = 'hasSeenCustomCoinEventPromoBanner'
const HAS_SEEN_EVENT_BANNER_KEY = 'hasSeenCustomCoinEventBanner'
const REQUIRED_SKIN_ID = 2160

const { getImageClass } = useIconImage()

const { t } = useI18n()
const { showToast } = useToast()
const router = useRouter()
const {
  isOpen: isOpenRewardBanner,
  openWindowInQueue: openRewardWindowInQueue,
  closeWindowInQueue: closeRewardWindowInQueue
} = useWindowQueue('custom-coin-reward-window')
const {
  isOpen: isOpenPromoBanner,
  openWindowInQueue: openPromoWindowInQueue,
  closeWindowInQueue: closePromoWindowInQueue
} = useWindowQueue('custom-coin-promo-window')

const props = defineProps<{
  isOpen: boolean
}>()
const emit = defineEmits(['close'])

const customCoinStore = useCustomCoinStore()
const { showReward } = useReward()

const { checkWelcomeBanner } = useEventWelcomeCheck()
const { checkPromoBanner } = useEventPromoCheck()

const { playerState, refetchPlayerState } = usePlayerState()
const { userInfo, refetchUserInfo } = useCustomCoinUserInfo(false)
const { getUser } = useUser()
const userName = getUser().getName()
const { onPlayerRecievedCustomCoinReward } = usePlayerReceivedEventReward()

const close = () => {
  emit('close')
}

const closeBanner = () => {
  if (isOpenRewardBanner.value) {
    const coinsCollected = playerState.value?.customCoinEventReward?.coinsCollected ?? 0
    const reward = playerState.value!.customCoinEventReward?.reward?.amount ?? 0
    const currency = playerState.value!.customCoinEventReward?.reward?.currency ?? 'hard'

    showReward({
      type: currency,
      value: reward
    }, { isAlreadyOnPlayerState: true })

    sendAnalyticsEvent('event_end', {
      event: 'custom_coin',
      total_points: coinsCollected,
      reward_type: currency,
      reward_amount: getCurrencyRealAmount(reward, currency)
    })
    onPlayerRecievedCustomCoinReward()
    closeRewardWindowInQueue()
  } else if (isOpenPromoBanner.value) {
    closePromoWindowInQueue()
  } else {
    close()
  }
}

watch(
  () => playerState.value?.customCoinEventReward?.reward?.amount,
  async () => {
    if (!playerState.value?.customCoinEventReward) return
    const isOpenRewardBanner = playerState.value?.customCoinEventReward?.reward?.amount ?? false
    if (isOpenRewardBanner) {
      await refetchUserInfo()
      openRewardWindowInQueue()
    }
  }
)

watch(
  () => customCoinStore.isCustomCoinPromoTimeOver,
  () => {
    if (!customCoinStore.isCustomCoinPromoTimeOver || !customCoinStore.isCustomCoinActive) return

    if (playerState.value?.customCoinEvent?.hasMetRequirement) {
      router.push({
        name: 'custom-coin-event'
      })
    } else {
      openPromoWindowInQueue()
    }
  }
)

onMounted(async () => {
  const isOpenRewardBanner = playerState.value?.customCoinEventReward?.reward?.amount ?? false
  if (isOpenRewardBanner) {
    openRewardWindowInQueue()
  } else {
    const isOpenWelcomeBanner = await checkWelcomeBanner(
      HAS_SEEN_EVENT_BANNER_KEY,
      customCoinStore.isCustomCoinActive && customCoinStore.isCustomCoinPromoTimeOver,
      leaguesService.hasAccess(playerState.value!.leagueLevel ?? 1, 'customCoinEvent')
    )
    if (isOpenWelcomeBanner && playerState.value?.customCoinEvent?.hasMetRequirement) {
      if (playerState.value?.customCoinEvent && customCoinStore.isCustomCoinPromoTimeOver) {
        if (playerState.value?.customCoinEvent?.hasMetRequirement) {
          router.push({
            name: 'custom-coin-event'
          })
        } else {
          openPromoWindowInQueue()
        }
      }
    } else {
      const isOpenPromoBanner = await checkPromoBanner(
        HAS_SEEN_EVENT_PROMO_BANNER_KEY,
        customCoinStore.isCustomCoinActive && !customCoinStore.isCustomCoinPromoTimeOver,
        leaguesService.hasAccess(playerState.value!.leagueLevel ?? 1, 'customCoinEvent')
      )
      if (isOpenPromoBanner) {
        openPromoWindowInQueue()
      }
    }
  }
})

const eventButtons = computed<Array<ButtonProps>>(() => {
  return isOpenRewardBanner.value ? [{ text: 'Claim!', type: 'accent', onClick: closeBanner }] : []
})

const userData = computed(() => {
  const reward = userInfo.value?.reward || playerState.value!.customCoinEventReward?.reward
  return {
    rank: (userInfo.value?.rank || playerState.value?.customCoinEventReward?.playerRank) ?? 0,
    score:
      (userInfo.value?.coinsCollected ||
        playerState.value?.customCoinEventReward?.coinsCollected) ??
      0,
    balance: reward ? getCurrencyRealAmount(reward.amount, reward.currency) : 0,
    currency: reward?.currency ?? 'hard',
    league: (userInfo.value?.leagueLevel || playerState.value?.leagueLevel) ?? 1
  }
})

const { skinsList } = useSkinsList()
const { purchaseSkin: purchaseSkinMutation, isPending: isSkinPurchasePending } = usePurchaseSkin()
const requiredSkin = computed(() => skinsList.value.find(s => s.id === REQUIRED_SKIN_ID))

const purchaseSkin = (skin: Skin) => {
  purchaseSkinMutation(skin.id, skin.multiplier).then(() => {
    showReward({
      type: 'skin',
      value: skin.id,
      multiplier: skin.multiplier
    }, { isAlreadyOnPlayerState: true })
    refetchPlayerState()
  })
}

const { purchase, isPendingPurchase } = usePurchase()
const isPendingOpenLootbox = ref(false)
const purchaseLootBox = () => {
  isPendingOpenLootbox.value = true
  purchase('duckyLootBox', 560, 1, { amount: 49, currency: 'hard' })
    .catch(reason => {
      if (reason?.message === 'NOT_ENOUGH_FUNDS') {
        showToast('Not enough funds', 'warning')
        router.push({ name: 'shop', query: { scrollTo: 'hard' } })
        props.isOpen ? close() : closeBanner()
      }
    })
    .finally(() => {
      isPendingOpenLootbox.value = false
    })
}
</script>

<template>
  <EventBanner
    class="custom-coin-banner w-full h-full"
    :isOpen="props.isOpen || isOpenRewardBanner || isOpenPromoBanner"
    :buttons="eventButtons"
    :banner="eventBanner"
    :image="eventImage"
    :is-loading="isSkinPurchasePending || isPendingOpenLootbox || isPendingPurchase"
    @close="isOpen ? close() : closeBanner()"
    showCloseButton
  >
    <template #details>
      <!-- Event Timer -->
      <div v-if="customCoinStore.isCustomCoinActive" class="custom-coin-banner__timer mt-2">
        <h1
          class="text-white text-center text-[12px] leading-[17px] text-shadow text-shadow_black text-shadow_thin"
        >
          {{ customCoinStore.isCustomCoinPromoTimeOver ? 'Event Ends at:' : 'Event Starts in:' }}
          <CountdownTimerManual
            class="text-[12px] leading-[16px] text-[#FFFFFF]"
            :days="customCoinStore.days"
            :hours="customCoinStore.hours"
            :minutes="customCoinStore.minutes"
            :seconds="customCoinStore.seconds"
            digital
          />
        </h1>
      </div>
    </template>

    <!-- Reward Banner -->
    <template v-if="isOpenRewardBanner">
      <div
        class="custom-coin-banner__box flex flex-col justify-center items-center w-full p-3 mb-3 pt-16"
      >
        <LeaderboardItem
          class="!absolute w-full"
          :username="userName"
          :score="formatNumberToShortString(userData.score)"
          :rank-index="userData.rank - 1"
          :balance="userData.balance"
          :league="userData.league"
          :balanceType="userData.currency"
          :scoreTypeImage="coinIcon"
          scoreClass="text-shadow text-shadow_black"
          active
        />
        <BalanceItem
          class="mb-2"
          iconName="custom-coin-bg"
          bar-class="custom-coin-banner__bar"
          balance-class="custom-coin-banner__balance text-shadow text-shadow_black text-white"
          image-class="custom-coin-banner__image !w-[35px] !h-[35px]"
        >
          {{ formatNumberWithSeparator(userData.score ?? 0) }}
        </BalanceItem>
        <img :src="convertIcon" class="w-[37px]" alt="convert icon" />
        <BalanceItem
          class="mt-2"
          :iconName="getImageClass(userData.currency)"
          bar-class="custom-coin-banner__bar"
          balance-class="custom-coin-banner__balance text-shadow text-shadow_black text-white"
          image-class="custom-coin-banner__image !w-[42px] !h-[42px] !-left-[3px]"
        >
          {{ formatNumberToShortString(userData.balance ?? 0) }}
        </BalanceItem>
      </div>
    </template>

    <!-- Promo/Requirement Banner -->
    <template v-else>
      <!-- Event Description -->
      <div class="custom-coin-banner__box text-wrap px-2 py-3 mb-2 mt-3">
        <i18n-t
          class="text-white text-[13px] leading-[20px] text-shadow text-shadow_thin text-shadow_black"
          tag="p"
          keypath="customCoin.description"
        >
          <template v-slot:box>
            <span class="text-shadow_yellow">Ducky Box</span>
          </template>
          <template v-slot:x>
            <span class="text-shadow_yellow">50X</span>
          </template>
          <template v-slot:ton>
            <span class="text-shadow_yellow">30 TON</span>
          </template>
          <template v-slot:stars>
            <span class="text-shadow_yellow">20,000 Stars.</span>
          </template>
        </i18n-t>
      </div>

      <!-- Event Purchases -->
      <div class="flex w-full justify-between">
        <!-- Skin -->
        <div
          class="custom-coin-banner__purchase custom-coin-banner__purchase_cream"
          @click="() => requiredSkin && !requiredSkin.purchased && purchaseSkin(requiredSkin)"
        >
          <div class="custom-coin-banner__purchase_info">
            <p class="text-[15px] text-white text-shadow text-shadow_thin text-shadow_black">
              {{ requiredSkin ? t(`skins.list.${requiredSkin.id}`) : 'some_skin' }}
            </p>
            <div v-if="requiredSkin" class="h-[100px] w-[100px]">
              <SkinItem :src="SKIN_ID_TO_IMAGE[requiredSkin.id]" class="w-full" />
            </div>
          </div>
          <div class="custom-coin-banner__purchase_price">
            <p
              v-if="requiredSkin?.purchased"
              class="custom-coin-banner__purchase_price__claimed text-[20px]"
            >
              {{ t('claimed') }}
            </p>
            <div v-else class="flex items-center justify-center gap-1 pb-1">
              <p class="text-[20px] text-white text-shadow text-shadow_thin text-shadow_black">
                {{ t('actions.claim') }}
              </p>
            </div>
          </div>
        </div>

        <!-- Loot box -->
        <div
          class="custom-coin-banner__purchase custom-coin-banner__purchase_orange"
          @click="purchaseLootBox"
        >
          <div class="badge">
            <i18n-t
              class="text-[13px] text-white text-shadow text-shadow_thin text-shadow_black"
              tag="p"
              keypath="customCoin.boost"
            >
              <template v-slot:x>
                <span class="text-shadow_yellow">50X</span>
              </template>
            </i18n-t>
          </div>
          <div class="custom-coin-banner__purchase_info">
            <p class="text-[15px] text-white text-shadow text-shadow_thin text-shadow_black">
              Ducky Box
            </p>
            <img class="w-[90px]" :src="boxImage" alt="box" />
          </div>
          <div class="custom-coin-banner__purchase_price">
            <img class="w-[20px] mr-1" :src="starImage" alt="start image" />
            <p class="text-[20px] text-white text-shadow text-shadow_thin text-shadow_black">49</p>
          </div>
        </div>
      </div>
    </template>
  </EventBanner>
</template>

<style lang="scss">
.custom-coin-banner {
  top: 0;
  --event-background: linear-gradient(360deg, #6348F2 0%, #6348F2 64%);

  .event-banner {
    transform: translateY(20px);
    max-width: 360px;
    border: 6px solid #ffd634;

    .close-button {
      --close-btn-background-color: white;
    }

    &__banner {
      width: 90%;
      top: -30px;
    }
  }

  &__purchase {
    --skin-item-background: linear-gradient(360deg, #67ffd9 0%, #b2ffe5 92.65%);
    --skin-item-price-background-color: #1CCFABC7;
    --skin-item-inner-shadow-color-top: #e1f6ff;
    --skin-item-inner-shadow-color-bottom: #2ea28f;
    --skin-item-shadow-color: #00000040;

    height: 180px;
    width: 150px;
    position: relative;
    border-radius: 9px;
    background: var(--skin-item-background);
    box-shadow: 0 2px var(--skin-item-shadow-color);

    &:active {
        --skin-item-background: #043870;
    }

    &_info {
      position: relative;
      padding: 2px 10px 5px 10px;
      height: 150px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      border-radius: 9px 9px 0 0;
      box-shadow: inset 0 2px var(--skin-item-inner-shadow-color-top);
    }

    &_price {
      position: absolute;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 30px;
      border-radius: 0 0 9px 9px;
      width: 100%;
      background: var(--skin-item-price-background-color);
      box-shadow: inset 0 -4px var(--skin-item-inner-shadow-color-bottom);

      &__claimed {
        // color: #a1754a;
        color: gray;
      }
    }

    &_orange {
      --skin-item-background: linear-gradient(360deg, #ffaa00 0%, #fffb00 92.65%);
      --skin-item-price-background-color: #f19204;
      --skin-item-inner-shadow-color-top: #fffacb;
      --skin-item-inner-shadow-color-bottom: #d86800;

      &:active {
        --skin-item-background: #ffaa00;
      }

      &::after {
        content: '';
        position: absolute;
        top: 1px;
        left: 1px;
        width: 10px;
        height: 6px;
        background-color: white;
        border-radius: 50%;
        transform: rotate(-26deg);
      }
    }

    &_blue {
      --skin-item-background: linear-gradient(360deg, #1eadea 0%, #98e0ff 92.65%);
      --skin-item-price-background-color: #0084e8c7;
      --skin-item-inner-shadow-color-top: #e1f6ff;
      --skin-item-inner-shadow-color-bottom: #006bb5;

      &:active {
        --skin-item-background: #1eadea;
      }

      &::after {
        content: '';
        position: absolute;
        top: 1px;
        left: 1px;
        width: 10px;
        height: 6px;
        background-color: white;
        border-radius: 50%;
        transform: rotate(-26deg);
      }
    }

    &_pink {
      --skin-item-background: linear-gradient(360deg, #e027fe 0%, #ff9838 92.65%);
      --skin-item-price-background-color: #ad20d4;
      --skin-item-inner-shadow-color-top: #ffcc7b;
      --skin-item-inner-shadow-color-bottom: #8602ad;

      &:active {
        --skin-item-background: #e027fe;
      }

      &::after {
        content: '';
        position: absolute;
        top: 1px;
        left: 1px;
        width: 10px;
        height: 6px;
        background-color: white;
        border-radius: 50%;
        transform: rotate(-26deg);
      }
    }

    &_cream {
      --skin-item-background: linear-gradient(360deg, #fbf0c4 0%, #fef8e0 92.65%);
      --skin-item-price-background-color: #efbf90;
      --skin-item-inner-shadow-color-top: #ffffff;
      --skin-item-inner-shadow-color-bottom: #c6905b;

      &:active {
        --skin-item-background: #fbf0c4;
      }

      &::after {
        content: '';
        position: absolute;
        top: 1px;
        left: 1px;
        width: 10px;
        height: 6px;
        background-color: white;
        border-radius: 50%;
        transform: rotate(-26deg);
      }
    }

    .badge {
      position: absolute;
      width: calc(100% + 8px);
      transform: translateX(-4px);
      height: 20px;
      border-radius: 5px;
      top: 25px;
      background: linear-gradient(360deg, #a105e4 0%, #e228ff 92.65%);
      box-shadow: 0 2px #7d00a0;
      border: none;
    }
  }

  &__box {
    position: relative;
    width: 100%;
    background: #9772FF;
    border-radius: 9px;
  }

  &__timer {
    min-width: 210px;
    padding: 3px 8px 3px 10px;
    position: relative;
    background: #9772FF;
    border-radius: 5px;
  }

  &__bar {
    height: 32px;
    padding-left: 28px;

    &::after {
      // background: #3C309CCC;
    }
  }

  &__balance {
    font-size: 24px;
  }
}
</style>

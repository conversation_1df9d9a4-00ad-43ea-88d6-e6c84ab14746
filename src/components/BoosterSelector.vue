<script setup lang="ts">
import ModalWindow from '@/components/UI/ModalWindow.vue'
import { computed, onMounted, ref } from 'vue'
import VButton from './UI/VButton.vue'
import { useI18n } from 'vue-i18n'
import type { StackableBoosterType } from '@/services/openapi'
import { usePlayerState } from '@/services/client/usePlayerState'
import CountdownTimerManual from './UI/CountdownTimerManual.vue'
import BoosterPurchaseSelector from './BoosterPurchaseSelector.vue'
import {
  useMagneticFieldStore,
  useJumperStore,
  useAimBotStore
} from '@/stores/boostersStore';
import leaguesService from '@/services/local/leagues'
import { useTonOnPlatformEventInfo } from '@/composables/useEventInfo'
import { REWARD_TO_IMAGE_CLASS } from '@/composables/useIconImage'

const { t } = useI18n()
const emit = defineEmits(['close', 'startGame'])

const magneticFieldStore = useMagneticFieldStore()
const jumperStore = useJumperStore()
const aimBotStore = useAimBotStore()

const BOOSTER_TYPE_TO_TIME_BOUND_STORE: Record<StackableBoosterType, any> = {
  stackableJumper: jumperStore,
  stackableAimbot: aimBotStore,
  stackableMagneticField: magneticFieldStore
}

const { playerState } = usePlayerState()
const playerLeague = computed(() => playerState.value!.leagueLevel ?? 1)

const {
  isEventActive: isTonEventActive,
} = useTonOnPlatformEventInfo(playerState)

const isOpen = ref(false)
const boosterToPurchase = ref<StackableBoosterType | ''>('')

const boosters = ref<{
  id: StackableBoosterType
  isActive: boolean
}[]>([
  {
    id: 'stackableJumper',
    isActive: false
  },
  {
    id: 'stackableAimbot',
    isActive: false
  },
  {
    id: 'stackableMagneticField',
    isActive: false
  }
])

const boosterList = computed(() => {
  return boosters.value.map(booster => {
    const amount = playerState.value!.boostersView?.[booster.id] ?? 0
    return {
      ...booster,
      amount,
      isActive: (amount > 0 && booster.isActive) || BOOSTER_TYPE_TO_TIME_BOUND_STORE[booster.id].isActive
    }
  })
})

const startGame = () => {
  const activeBoosters = boosterList.value
    .filter(booster => booster.isActive)
    .map(booster => booster.id)
  emit('startGame', activeBoosters)
  closeWindow()
}

const toggleBooster = (id: StackableBoosterType) => {
  const booster = boosters.value.find(booster => booster.id === id)
  const computedBooster = boosterList.value.find(booster => booster.id === id)
  if (!booster || !computedBooster || BOOSTER_TYPE_TO_TIME_BOUND_STORE[booster.id].isActive) return
  if (computedBooster.amount <= 0) {
    purchaseBooster(id)
  } else {
    booster.isActive = !booster.isActive
  }
}

const purchaseBooster = (id: StackableBoosterType) => {
  boosterToPurchase.value = id
}

const onPurchased = (boosterType: StackableBoosterType) => {
  boosterToPurchase.value = ''
  toggleBooster(boosterType)
}

onMounted(() => {
  setTimeout(() => {
    isOpen.value = true
  })
})

const closeWindow = () => {
  isOpen.value = false
  emit('close')
}
</script>

<template>
  <ModalWindow
    class="booster-selector-dialog"
    :title="t('boosters.endlessRun')"
    :is-open="isOpen"
    @close="closeWindow"
  >
    <div class="bg-[#2397D529] rounded-[9px] space-y-3 py-[10px] mb-4">
      <p class="text-center font-extrabold text-[16px] text-[#1E4073]">
        {{t('boosters.rewards')}}
      </p>
      <div class="h-[50px] flex items-center justify-center gap-x-4">
        <img class="h-full" src="@/assets/images/temp/big-icons/ticket.png" alt="reward" />
        <img
          v-if="!playerState!.isBeginnerTonExhausted || (isTonEventActive && leaguesService.hasAccess(playerLeague, 'tonMiningEvent'))"
          class="h-full" src="@/assets/images/temp/big-icons/ton-l.png" alt="reward"
        />
      </div>
    </div>
    <p class="text-center font-extrabold text-[16px] text-[#1E4073] mb-[6px]">
      {{t('boosters.select')}}
    </p>
    <div class="flex items-center justify-center gap-x-3 mb-[31px]">
      <div
        v-for="booster in boosterList"
        :key="booster.id"
        class="booster-item"
        :class="{
          'booster-item_active': booster.isActive
        }"
        @click="toggleBooster(booster.id)"
      >
        <p class="text-[14px] text-[#1E4073] text-center mt-1">
          {{ t('boosters.' + booster.id + '.name') }}
        </p>
        <div class="icon-bg booster-item__image" :class="[REWARD_TO_IMAGE_CLASS[booster.id]]"></div>
        <div v-if="!booster.isActive && booster.amount > 0" class="booster-item__badge text-shadow text-shadow_black">
          {{ booster.amount }}
        </div>
        <div v-else-if="!booster.isActive && booster.amount <= 0" class="booster-item__badge booster-item__badge_green text-shadow text-shadow_black">
          <img src="@/assets/images/temp/plus-icon-simple.svg" />
        </div>
        <div v-else-if="BOOSTER_TYPE_TO_TIME_BOUND_STORE[booster.id].isActive" class="booster-item__badge booster-item__badge_green ">
          <img src="@/assets/images/temp/infinite-icon.svg" />
        </div>
        <div v-else class="booster-item__badge booster-item__badge_green">
          <img class="text-shadow text-shadow_black" src="@/assets/images/temp/check-icon-simple.svg" />
        </div>
        <div v-if="BOOSTER_TYPE_TO_TIME_BOUND_STORE[booster.id].isActive" class="booster-item__timer">
          <CountdownTimerManual
            :seconds="BOOSTER_TYPE_TO_TIME_BOUND_STORE[booster.id].seconds"
            :minutes="BOOSTER_TYPE_TO_TIME_BOUND_STORE[booster.id].minutes"
            :hours="BOOSTER_TYPE_TO_TIME_BOUND_STORE[booster.id].hours"
            :days="BOOSTER_TYPE_TO_TIME_BOUND_STORE[booster.id].days"
          />
        </div>
      </div>
    </div>
    <VButton
      class="mx-auto min-w-[244px]"
      type="success"
      :text="t('actions.play')"
      @click="startGame"
    >
    </VButton>
  </ModalWindow>
  <BoosterPurchaseSelector
    v-if="boosterToPurchase"
    :boosterType="boosterToPurchase"
    @close="boosterToPurchase = ''"
    @purchased="onPurchased"
  />
</template>

<style lang="scss">
.booster-selector-dialog {
  padding: 11px;
  background: linear-gradient(360deg, #1EADEA 0%, #98E0FF 92.65%);

  .modal-window__title {
    font-size: 32px;
  }

  .booster-item {
    position: relative;
    width: 90px;
    height: 90px;

    background-color: #00EEFF66;
    border: 2px solid #26508280;
    border-radius: 9px;

    &_active {
      background-color: #6CE701;
      border-color: #02510080;
    }

    &__image {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -45%);
      width: 50px;
      height: 50px;
    }

    &__badge {
      position: absolute;
      right: -3px;
      bottom: -3px;
      width: 28px;
      height: 28px;
      z-index: 1;
      border-radius: 50%;
      font-size: 16px;

      display: flex;
      align-items: center;
      justify-content: center;

      background: linear-gradient(180deg, #FF9B30 0%, #FFD900 100%);
      border: 1px solid #9A4001;
      box-shadow: inset 0 -3px #F7781E;

      &_green {
        background: linear-gradient(180deg, #A7F856 0%, #04D400 100%);
        border-color: #02510080;
        box-shadow: inset 0 -3px #03AA00;
      }
    }

    &__timer {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 80%;
      height: 20px;

      display: flex;
      align-items: center;
      justify-content: center;

      text-align: center;
      background: #1E4073;
      color: #fff;
      font-size: 12px;
      border-radius: 4px;
    }
  }
}

.v-enter-active,
.v-leave-active {
  transition: opacity 0.2s linear;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}
</style>

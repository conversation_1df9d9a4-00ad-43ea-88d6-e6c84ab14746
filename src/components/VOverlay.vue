<script setup lang="ts">

const emit = defineEmits(['clickSelf', 'click'])
const props = defineProps<{
  isOpen: boolean
  short?: boolean
  class?: any
  style?: any
}>()
</script>

<template>
  <Teleport to="#app" :disabled="short">
    <Transition name="overlay">
      <div
        v-bind="$attrs"
        v-if="isOpen"
        class="overlay"
        :class="`${short ? 'overlay_short' : ''} ${props.class}`"
        :style="props.style"
        @click.stop.self="() => emit('clickSelf')"
        @click="() => emit('click')"
      >
        <slot></slot>
      </div>
    </Transition>
  </Teleport>
</template>

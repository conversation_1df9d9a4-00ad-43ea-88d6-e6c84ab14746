<script setup lang="ts">
import { formatNumberToShortString } from '@/utils/number'
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import AvatarItem from '../UI/AvatarItem.vue';
import BalanceItem from '../UI/BalanceItem.vue';
import NewBadge from '../UI/NewBadge.vue'
import ProgressBar from '../UI/ProgressBar.vue';
import RedDotBadge from '../UI/RedDotBadge.vue'
import { hapticsService } from '@/shared/haptics/hapticsService.ts'
import type { Price, RewardInfo } from '@/services/openapi';
import { isTimeRewardType } from '@/types'
import { CURRENCY_TO_IMAGE_CLASS, useIconImage } from '@/composables/useIconImage';
import VButton from '@/components/UI/VButton.vue';

const { t } = useI18n()

const { getImageClass } = useIconImage()

const props = defineProps<{
  id: number;
  image: string;
  description: string;
  reward?: RewardInfo;
  isDone: boolean;
  isCollected: boolean;
  isNew?: boolean;
  progress?: {
    current: number;
    goal: number;
  },
  price?: Price,
  isPriceDisabled?: boolean,
  isPurchasing?: boolean,
  stackableProgress?: boolean
}>()

const emit = defineEmits(['click', 'purchase'])

const isRewardLimitedByTime = computed(() => {
  return props.reward ? isTimeRewardType(props.reward.type) : false
})

const isFinalized = computed(() => {
  return props.isDone && props.isCollected
})

const hours = computed(() => isRewardLimitedByTime.value ? (Math.floor((props.reward!.value)  / 3600)) : 0)
const minutes = computed(() => isRewardLimitedByTime.value ? (Math.floor(((props.reward!.value) % 3600) / 60)) : 0)

const onClick = () => {
  if (!props.isCollected) {
    hapticsService.triggerImpactHapticEvent('light')
    emit('click', props.id)
  }
}
</script>

<template>
  <div
    class="mission-wrapper"
    :class="{ 'mission-wrapper_collected': props.isCollected }"
  >
    <div
      class="mission"
      @click="onClick"
    >
      <AvatarItem size="48" :src="image" />
      <div class="min-w-0 flex-1">
        <p class="mission__title tracking-normal truncate">
          {{ description }}
        </p>
        <div class="flex">
          <ProgressBar
            v-if="progress"
            class="mission__progress"
            inner-wrapper-class="mission__progress-inner-wrapper"
            :progress="progress.current"
            :goal="progress.goal"
            :stackable="stackableProgress"
          >
            <p v-if="!stackableProgress" class="text-shadow text-shadow_black text-shadow_thin">
              {{ formatNumberToShortString(progress.current) }}/{{ formatNumberToShortString(progress.goal) }}
            </p>
          </ProgressBar>
          <div
            class="mission__reward-container"
            :class="{ '!left-auto': progress }"
          >
            <BalanceItem
              v-if="reward"
              class="mission__reward"
              :image-class="isRewardLimitedByTime ? 'mission__reward-image-heart' : 'mission__reward-image'"
              :iconName="getImageClass(reward.type)"
              barClass="mission__reward-bar"
              balanceClass="mission__reward-balance"
            >
              <span v-if="isRewardLimitedByTime">
                +{{ hours || minutes }}{{ t(`${hours ? 'hours' : 'minutes'}`) }}
              </span>
              <span v-else> +{{ formatNumberToShortString(reward.value) }}</span>
            </BalanceItem>
          </div>
        </div>
      </div>
      <div
        class="mission__controls"
        :class="{ 'mission__controls_with-price': price && !isFinalized }"
      >
        <VButton
          v-if="price && !isFinalized"
          :class="{
            '!w-full': true,
            'self-end': !!reward,
          }"
          :text="price.amount ? formatNumberToShortString(price.amount) : 'Free'"
          :image-class="price.amount ? CURRENCY_TO_IMAGE_CLASS[price.currency] : ''"
          size="small"
          :type="price.amount ? 'success' : 'accent'"
          :disabled="isPurchasing || isPriceDisabled"
          stopPropagation
          @click="() => emit('purchase', id, price)"
        />
        <div v-else-if="!isFinalized" class="mission__chevron"></div>
      </div>
      <div class="mission__skew-lines space-x-[10px]">
        <div class="mission__skew-lines-block mission__skew-lines-block_gradient w-[25px]"></div>
        <div class="mission__skew-lines-block w-[45px]"></div>
      </div>
    </div>
    <div v-if="isFinalized" class="check-icon collected-mark"></div>
    <RedDotBadge v-else-if="props.isDone && !props.isCollected" class="-top-[4px] -right-[4px]"/>
    <NewBadge v-else-if="isNew" class="-top-[4px] -right-[4px]" />
  </div>
</template>

<style lang="scss">
.mission-wrapper {
  position: relative;

  &_collected {
    opacity: 0.5;
  }
}
.mission {
  width: 100%;
  position: relative;
  height: 68px;
  padding: 7px 10px 10px 8px;

  background: linear-gradient(360deg, #72C3E6 0%, #B7E9FF 92.65%);
  box-shadow: #00000033 0 2px, inset #FFFFFF 0 3px, inset #4E9DD7 0 -5px;
  border: 1px #032F70 solid;
  border-radius: 9px;

  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;

  &__title {
    font-size: 14px;
    line-height: 24px;
    font-weight: 800;
    color: #1E4073;
  }

  &__controls {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 0 0 35px;
    
    &_with-price {
      flex: 0 0 95px;
    }
  }  

  &__skew-lines {
    position: absolute;
    top: 1px;
    right: 60px;
    height: calc(100% - 7px);
    display: flex;

    &-block {
      height: 100%;
      background-color: #FFFFFF17;
      transform: skew(-15deg);

      &_gradient {
        background: linear-gradient(270deg, rgba(255, 255, 255, 0.1) 17.2%, rgba(255, 255, 255, 0) 122.58%);
      }
    }
  }

  &__progress {
    flex: 1;
    height: 22px;

    &-inner-wrapper {
      padding: 2px;
    }
  }

  &__reward-container {
    position: relative;
    left: 11px;
  }

  &__reward {
    &-image {
      width: 28px;
      height: 28px;

      &-heart {
        left: -3px;
      }
    }

    &-bar {
      height: 22px;

      &::after {
        background-color: #255FA3CC;
        box-shadow: inset #3561A2B2 0 -12px;
      }
    }

    &-balance {
      font-size: 15px;
    }
  }

  &__chevron {
    background: url('@/assets/images/temp/cevron-right-icon.svg');
    background-repeat: no-repeat;
    margin-right: 10px;
    width: 11px;
    height: 14px;
  }
}

.collected-mark {
  position: absolute;
  top: 50%;
  right: 14px;
  transform: translateY(-50%);
}
</style>

<script setup lang="ts">
import { hapticsService } from '@/shared/haptics/hapticsService.ts'

const onBackClick = () => {
  hapticsService.triggerImpactHapticEvent('light')
}
</script>

<template>
  <div class="home-button-container">
    <RouterLink class="home-button" to="/" @click="onBackClick">
      <img src="@/assets/images/temp/close.png" alt="home-button" />
    </RouterLink>
  </div>
</template>

<style lang="scss" scoped>
.home-button-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: calc(var(--footer-top-padding) + var(--footer-offset));
  padding-bottom: var(--footer-bottom-padding);

  background: url('@/assets/images/temp/background/footer-background.png') bottom center / cover no-repeat, linear-gradient(179.36deg, #66D0F9 14.46%, #2BA9DF 99.45%);
  overflow: hidden;
  z-index: 1;
  box-shadow: 0px 6px 4px 1px #CAF0FF inset, 0px 4px 20px 0px #2B1C3FA6;
  -webkit-box-shadow: 0px 6px 4px 1px #CAF0FF inset, 0px 4px 20px 0px #2B1C3FA6;
  -moz-box-shadow: 0px 6px 4px 1px #CAF0FF inset, 0px 4px 20px 0px #2B1C3FA6;
}

.home-button {
  width: var(--footer-button-size);
  height: var(--footer-button-size);

  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 50%;
    height: 50%;
  }
}
</style>

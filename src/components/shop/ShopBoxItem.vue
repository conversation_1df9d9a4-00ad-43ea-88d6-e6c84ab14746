<script setup lang="ts">
import { hapticsService } from '@/shared/haptics/hapticsService.ts'
import { useI18n } from 'vue-i18n';
import type { LootBoxOffer, LootBoxType, ShopItemPrice } from '@/services/openapi';

import { CURRENCY_TO_IMAGE_CLASS } from '@/composables/useIconImage'
import rainbowBg from '@/assets/images/temp/shop/rainbow_bg.png'
import luckyBg from '@/assets/images/temp/shop/lucky_bg.png'
import magicBg from '@/assets/images/temp/shop/magic_bg.png'
import rainbowBox from '@/assets/images/temp/big-icons/rainbowBox_stroke.png'
import luckyBox from '@/assets/images/temp/big-icons/luckyBox_stroke.png'
import magicBox from '@/assets/images/temp/big-icons/magicBox_stroke.png'
import rainbowTitle from '@/assets/images/temp/shop/rainbow_title.png'
import luckyTitle from '@/assets/images/temp/shop/lucky_title.png'
import magicTitle from '@/assets/images/temp/shop/magic_title.png'
import { computed } from 'vue';
import { formatNumberToShortString } from '@/utils/number';
import VButton from '@/components/UI/VButton.vue';

const BOX_TYPE_TO_BG: Partial<Record<LootBoxType, string>> = {
  'magicLootBox': magicBg,
  'luckyLootBox': luckyBg,
  'rainbowLootBox': rainbowBg,
}

const BOX_TYPE_TO_IMAGE: Partial<Record<LootBoxType, string>> = {
  'magicLootBox': magicBox,
  'luckyLootBox': luckyBox,
  'rainbowLootBox': rainbowBox,
}

const BOX_TYPE_TO_TITLE: Partial<Record<LootBoxType, string>> = {
  'magicLootBox': magicTitle,
  'luckyLootBox': luckyTitle,
  'rainbowLootBox': rainbowTitle,
}

const { t } = useI18n()

const {
  type,
  offers,
} = defineProps<{
  type: LootBoxType
  offers: Array<LootBoxOffer>
}>()

const emit = defineEmits(['purchase'])

const sortedOffers = computed(() => {
  return offers.slice().sort((a, b) => a.price.displayPrice.amount - b.price.displayPrice.amount)
})

const purchase = (id: number, value: number, price: ShopItemPrice) => {
  hapticsService.triggerImpactHapticEvent('light')
  emit('purchase', id, value, price)
}

const onClick = () => {
  hapticsService.triggerImpactHapticEvent('light')
}
</script>

<template>
  <article
    class="shop-box-item-container"
    :class="'shop-box-item_' + type"
    @click="onClick"
  >
    <img class="shop-box-item-container__bg" :src="BOX_TYPE_TO_BG[type]" alt="box bg">
    <div class="shop-box-item" :class="'shop-box-item__style_' + type">
      <img class="shop-box-item__image" :src="BOX_TYPE_TO_IMAGE[type]" alt="box image">
      <div class="shop-box-item__info">
        <img class="shop-box-item__info-title" :src="BOX_TYPE_TO_TITLE[type]" alt="box info title">
        <div class="w-full grid grid-cols-2 gap-2">
          <div
            v-for="item in sortedOffers"
            :key="item.id"
          >
            <VButton
              class="shop-box-item__button !w-full"
              :image-class="CURRENCY_TO_IMAGE_CLASS[item.price.displayPrice.currency]"
              :text="formatNumberToShortString(item.price.displayPrice.amount)"
              type="success"
              size="medium"
              stopPropagation
              @click="() => purchase(item.id, item.value, item.price)"
            >
              <template #pre-content>
                <p class="absolute left-[6px] text-[14px] text-[#014700]">x{{ item.value }}</p>
              </template>
            </VButton>
          </div>
        </div>
      </div>
    </div>
  </article>
</template>

<style lang="scss">
.shop-box-item-container {
  position: relative;
  width: 100%;
  aspect-ratio: 1057 / 387;

  &__bg {
    width: 100%;
  }
}

.shop-box-item {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  column-gap: 6px;
  padding: 0 12px 0 6px;

  &__style {
    &_rainbowLootBox {
      --shop-box-info-bg: #2643604D;
    }
    &_luckyLootBox {
      --shop-box-info-bg: #5324604D;
    }
    &_magicLootBox {
      --shop-box-info-bg: #9549134D;
    }
  }

  &__image {
    height: 100%;
  }

  &__info {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    padding: 5px 9px 9px;
    aspect-ratio: 69 / 29;

    background-color: var(--shop-box-info-bg);
    border-radius: 8px;
    
    &-title {
      width: 100%;
    }
  }

  &__button {
    padding: 0 9px 0 0;
    font-size: 17px;

    .button__content {
      justify-content: end;
      gap: 2px;
    }
  }
}
</style>

<script setup lang="ts">
import ModalWindow from '@/components/UI/ModalWindow.vue'
import VButton from '@/components/UI/VButton.vue'
import BoxRewardItem from '@/components/shop/BoxRewardItem.vue'
import { CURRENCY_TO_IMAGE_CLASS } from '@/composables/useIconImage'
import {
  LUCKY_LOOTBOX_REWARD_ID_TO_CHANCE,
  RAINBOW_LOOTBOX_REWARD_ID_TO_CHANCE,
  MAGIC_LOOTBOX_REWARD_ID_TO_CHANCE,
} from '@/constants/lootboxes.ts'
import type { LootBoxOffer, LootBoxType, RewardType, ShopItemPrice } from '@/services/openapi'
import { hapticsService } from '@/shared/haptics/hapticsService.ts'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { formatNumberToShortString } from '@/utils/number'

import threeBoostersImage from '@/assets/images/temp/big-icons/magnet-aim-jumper.png'
import tickets from '@/assets/images/temp/big-icons/ticket-3.png'
import wheelSpins from '@/assets/images/temp/big-icons/wheel-spin-l.png'
import secretSkinImage from '@/assets/images/temp/big-icons/secret.png'
import { isBoosterType } from '@/types'

const REWARD_TO_IMAGE: Partial<Record<RewardType, string>> = {
  tickets: tickets,
  wheelSpins: wheelSpins
}

const { t } = useI18n()

const LOOTBOX_TYPE_TO_REWARDS: Partial<Record<LootBoxType, Record<string, number>>> = {
  luckyLootBox: LUCKY_LOOTBOX_REWARD_ID_TO_CHANCE,
  rainbowLootBox: RAINBOW_LOOTBOX_REWARD_ID_TO_CHANCE,
  magicLootBox: MAGIC_LOOTBOX_REWARD_ID_TO_CHANCE
}

const ORDERED_REWARDS = ['tickets', 'boosters', 'wheelSpins']

const {
  isOpen = false,
  lootboxType,
  image = '',
  commonSkins = 0,
  rareSkins = 0,
  epicSkins = 0,
  otherRewards = [],
  shineImage = '',
  offers = [],
  isLoading
} = defineProps<{
  isOpen: boolean
  lootboxType: LootBoxType
  image: string
  shineImage?: string
  commonSkins: number
  rareSkins: number
  epicSkins: number
  otherRewards: RewardType[]
  offers: LootBoxOffer[]
  isLoading: boolean
}>()

const emit = defineEmits(['close', 'purchase'])

const sortedOffers = computed(() => {
  return offers.slice().sort((a, b) => a.price.displayPrice.amount - b.price.displayPrice.amount)
})

const purchase = (id: number, value: number, price: ShopItemPrice) => {
  hapticsService.triggerImpactHapticEvent('light')
  emit('purchase', id, value, price)
}

const getChance = (id: string) => {
  return LOOTBOX_TYPE_TO_REWARDS[lootboxType]?.[id] ?? 0
}

const getRewardImage = (rewards: RewardType[]): string => {
  if (!rewards || rewards.length <= 1) {
    return REWARD_TO_IMAGE[rewards[0]] ?? ''
  }

  return threeBoostersImage
}

const otherRewardsMap = computed<{ id: string, description: string, image: string, rarity: 'common' | 'legendary' | 'rare' | 'epic' }[]>(() => {
  const ticketsReward = otherRewards.filter(reward => reward === 'tickets')
  const wheelSpinsReward = otherRewards.filter(reward => reward === 'wheelSpins')
  const boostersReward = otherRewards.filter(reward => isBoosterType(reward) || reward === 'magicHorns')
  return [ticketsReward, wheelSpinsReward, boostersReward].map(rewards => {
    return {
      id: rewards[0] === 'tickets' ? 'tickets' : rewards[0] === 'wheelSpins' ? 'wheelSpins' : 'boosters',
      description: rewards.length > 1 ? t('reward.boosters') : t(`reward.${rewards[0]}`, {} , 2),
      image: getRewardImage(rewards),
      rarity: rewards[0] === 'tickets' ? 'common' : rewards[0] === 'wheelSpins' ? 'legendary' : 'rare'
    }
  })
})
</script>

<template>
  <ModalWindow
    :is-open="isOpen"
    :title="t(`lootboxes.${lootboxType}`)"
    class="box-details"
    short
    @close="() => emit('close')"
  >
    <div class="box-details__box-image-wrapper">
      <img class="h-[100%] z-10" :src="image" alt="box" />
      <div class="shine-rotate-animation h-[70%] z-0">
        <img class="shine-pulse-animation h-[80%]" :src="shineImage" alt="shineImage" />
      </div>
    </div>

    <div class="box-details__rewards-block">
      <div class="box-details__rewards-list box-details__rewards-list_bg">
        <p class="box-details__rewards-list__description">
          {{ t('shop.skinBoxDescription') }}
        </p>
        <BoxRewardItem
          rarity="common"
          :description="'x' + commonSkins"
          :src="secretSkinImage"
          :chance="getChance('common')"
        />
        <BoxRewardItem
          rarity="rare"
          :description="'x' + rareSkins"
          :src="secretSkinImage"
          :chance="getChance('rare')"
        />
        <BoxRewardItem
          rarity="epic"
          :description="'x' + epicSkins"
          :src="secretSkinImage"
          :chance="getChance('epic')"
        />
      </div>
      <div class="box-details__rewards-list">
        <p class="box-details__rewards-list__description">
          {{ t('shop.other') }}
        </p>
        <BoxRewardItem
          v-for="reward, index in otherRewardsMap.slice().sort((a, b) => ORDERED_REWARDS.indexOf(a.id) - ORDERED_REWARDS.indexOf(b.id))"
          :key="index"
          :rarity="reward.rarity"
          :description="reward.description"
          :src="reward.image"
          :chance="getChance(reward.id)"
        />
      </div>
    </div>

    <div class="w-full grid grid-cols-2 gap-2">
      <VButton
        v-for="item in sortedOffers"
        :key="item.id"
        class="box-details__button"
        :image-class="CURRENCY_TO_IMAGE_CLASS[item.price.displayPrice.currency]"
        :text="formatNumberToShortString(item.price.displayPrice.amount)"
        type="success"
        size="medium"
        :disabled="isLoading"
        @click="() => purchase(item.id, item.value, item.price)"
      >
        <template #pre-content>
          <p class="absolute left-4 text-[14px] text-[#014700]">x{{ item.value }}</p>
        </template>
      </VButton>
    </div>
  </ModalWindow>
</template>

<style lang="scss">
.box-details {
  max-height: 90%;
  max-width: 390px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  text-align: center;
  overflow: hidden;

  .modal-window__title {
    font-size: 36px;
    z-index: 1;
    margin-bottom: 6px;
  }

  &__box-image-wrapper {
    height: 138px;
    width: 138px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
  }

  &__rewards-block {
    position: relative;
    width: 100%;
    height: 50%;
    overflow-y: auto;
    margin-bottom: 16px;
  }
  
  &__rewards-list {
    overflow: visible;
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    grid-template-rows: min-content;
    column-gap: 13px;
    row-gap: 4px;
    padding: 2px 6px 0;
  
    &__description {
      grid-column: span 3;
      font-size: 16px;
      line-height: 22px;
      font-weight: 900;
    }
  
    &_bg {
      border-radius: 8px;
      background: #0F458966;
      padding-bottom: 9px;
    }
  }

  &__button {
    width: 100% !important;
    padding: 0 16px 0 0;

    .button__content {
      justify-content: end;
    }
  }
}
</style>

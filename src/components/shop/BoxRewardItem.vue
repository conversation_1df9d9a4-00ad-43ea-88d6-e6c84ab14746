<script lang="ts" setup>
import { formatNumberWithSeparator } from '@/utils/number.ts'

const { src, description, chance } = defineProps<{
  src: string
  description: string
  chance: number
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
}>()
</script>

<template>
  <div
    class="box-reward-wrapper"
    :class="`box-reward-wrapper_style-${rarity}`"
  >
    <div class="box-reward">
      <div class="box-reward__image-wrapper">
        <img class="box-reward__image" :src="src" alt="box reward" />
        <div class="box-reward__percentage">
          % {{ formatNumberWithSeparator(chance) }}.0
        </div>
      </div>
      <div class="box-reward__description">
        {{ description }}
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.box-reward {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  &__image-wrapper {
    position: relative;
    width: 100%;
    flex: 1 1 auto;
  }

  &__image {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    height: 80%;
  }

  &__percentage {
    position: absolute;
    right: 8px;
    bottom: 4px;
    font-size: 10px;
  }

  &__description {
    flex: 0 0 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    border-radius: 0 0 4px 4px;
    background: var(--box-reward-description-bg);
    font-size: 20px;
    line-height: 20px;
    white-space: nowrap;

    @media (max-width: 370px) {
      font-size: 16px;
    }
  }
}

.box-reward-wrapper {
  position: relative;
  background: var(--box-reward-bg);
  border: 4px solid var(--box-reward-border-color);
  border-radius: 8px;
  aspect-ratio: 1/1;

  &_style {
    &-common {
      --box-reward-bg: linear-gradient(180deg, #5fdc382e 0%, #5fdc3899 100%);
      --box-reward-border-color: #91ff70;
      --box-reward-decorative-color: #ccff7f;
      --box-reward-description-bg: #5ad23d;
    }

    &-rare {
      --box-reward-bg: linear-gradient(180deg, #0ecef62e 0%, #00a6ff99 100%);
      --box-reward-border-color: #7bd3ff;
      --box-reward-decorative-color: #9ff9ff;
      --box-reward-description-bg: #01a4fa;
    }

    &-epic {
      --box-reward-bg: linear-gradient(180deg, #ff2ce32e 0%, #ff2ce399 100%);
      --box-reward-border-color: #ff7ef3;
      --box-reward-decorative-color: #ffb9e7;
      --box-reward-description-bg: #f52ddf;
    }

    &-legendary {
      --box-reward-bg: linear-gradient(180deg, rgba(255, 234, 0, 0.3) 0%, #FFB800 100%);
      --box-reward-border-color: #FFE800;
      --box-reward-decorative-color: #FFB9E7;
      --box-reward-description-bg: #A28000;
    }
  }

  &::before {
    content: '';
    position: absolute;
    width: 36px;
    height: 4px;
    top: -4px;
    right: 20%;
    background: linear-gradient(to right, var(--box-reward-decorative-color) 60%, white 60%);
    z-index: 2;
  }

  &::after {
    content: '';
    position: absolute;
    width: 36px;
    height: 4px;
    bottom: -4px;
    left: 20%;
    background: linear-gradient(to right, white 40%, var(--box-reward-decorative-color) 40%);
    z-index: 2;
  }
}
</style>

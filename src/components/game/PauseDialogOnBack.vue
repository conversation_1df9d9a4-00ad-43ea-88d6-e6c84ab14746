<script setup lang="ts">
import VButton from '../UI/VButton.vue';
import { useI18n } from 'vue-i18n';
import VOverlay from '../VOverlay.vue';

const { t } = useI18n()

const props = defineProps<{
  open: boolean
}>()

const emit = defineEmits(['continue', 'end'])

</script>

<template>
  <VOverlay
    :isOpen="props.open"
    class="overlay_gradient pause-on-back-window"
  >
    <div class="text-center space-y-[6px] px-4">
      <p class="text-[26px] leading-[35px] text-shadow">
        {{ t('pausedOnBack') }}
      </p>
    </div>
    <div class="flex gap-x-3 px-5">
      <VButton class="flex-1" :text="t('actions.leave')" @click="() => emit('end')" />
      <VButton class="flex-1" :text="t('actions.continue')" type="success" @click="() => emit('continue')" />
    </div>
  </VOverlay>
</template>

<style lang="scss">
.pause-on-back-window {
  display: flex;
  flex-direction: column;
  justify-content: center;
  row-gap: 32px;
  z-index: 1001;
  padding: 11px;
}
</style>

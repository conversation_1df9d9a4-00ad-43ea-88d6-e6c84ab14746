<script setup lang="ts">
import swipeImage from '@/assets/images/temp/tutorial/swipe-tutorial.png'
import shootImage from '@/assets/images/temp/tutorial/shoot-tutorial.png'
import { sendAnalyticsEvent } from '@/utils/analytics'
import { watch } from 'vue'
import { useI18n } from 'vue-i18n'
import VOverlay from '../VOverlay.vue'
import { useControlMethodStore } from '@/stores/controlMethodStore'

const { t } = useI18n()

const props = defineProps<{
  openMove?: boolean
  openShoot?: boolean
}>()
const controlStore = useControlMethodStore()

watch([() => props.openMove, () => props.openShoot], ([openMove, openShoot]) => {
  if (openMove) {
    sendAnalyticsEvent('new_user', { step: 'first_tutorial' })
    controlStore.selectMethod(false)
  }
  if (openShoot) {
    sendAnalyticsEvent('new_user', { step: 'second_tutorial' })
  }
})

const emit = defineEmits(['continue'])
</script>

<template>
  <VOverlay
    :isOpen="!!openMove || !!openShoot"
    class="tutorial-window"
    @click="() => emit('continue')"
  >
    <div class="tutorial-window__central">
      <div v-if="openMove" class="absolute w-full text-center top-0 -translate-y-full">
        <div class="tutorial-window__text !font-normal whitespace-break-spaces">
          {{ t('swipeScreen') }}
        </div>
      </div>
      <div
        class="tutorial-window__images"
        :class="{
          'tutorial-window__images_swipe': openMove,
          'tutorial-window__images_pulse': openShoot,
        }"
      >
        <img v-if="openMove" class="w-[130px] absolute" :src="swipeImage" alt="swipe image" />
        <img v-else class="w-[130px] absolute" :src="shootImage" alt="arrow image" />
      </div>
    </div>
    <div class="tutorial-window__text w-full absolute bottom-[10%]">
      {{ openMove ? t('actions.tapToPlay') : t('actions.tapToShoot') }}
    </div>
  </VOverlay>
</template>

<style lang="scss">
.tutorial-window {
  display: flex;
  flex-direction: column;
  justify-content: center;
  z-index: 1001;
  background: bottom / cover no-repeat url('@/assets/images/temp/tutorial/tutorial-bg.png'), linear-gradient(#2794fc, #84d5ff);

  &__central {
    position: relative;
    transform: translateY(calc(var(--inset-top) * -1)); 
  }

  &__images {
    min-height: 180px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 34px;

    &_swipe {
      animation: tutorial-swipe 1s infinite;
    }

    &_pulse {
      animation: tutorial-pulse 1s infinite reverse;
    }
  }

  &__text {
    color: #fbf3dd;
    text-align: center;
    font-size: 36px;
    font-weight: 800;
    line-height: 50px;
    animation: tutorial-pulse 1s infinite;
  }

  @keyframes tutorial-swipe {
    0% {
      transform: translateX(10%);
    }
    50% {
      transform: translateX(-10%);
    }
    100% {
      transform: translateX(10%);
    }
  }

  @keyframes tutorial-pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }
}

.v-leave-active {
  transition: opacity 0.3s linear 0.4s;
}

.v-leave-to {
  opacity: 0;
}
</style>

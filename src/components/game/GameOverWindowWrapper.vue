<script setup lang="ts">
import { ref } from 'vue';
import GameOverDialog, { type Props } from '@/components/game/GameOverDialog.vue'
import PreGameOverWindow from './PreGameOverWindow.vue';
import BoosterSelector from '../BoosterSelector.vue';
import type { StackableBoosterType } from '@/services/openapi';

const props = defineProps<Props>()
const emit = defineEmits(['restart', 'go-to-main-menu', 'open-game-over'])
const isOpenBoosterSelector = ref(false)

const isPreGameOver = ref(true)
const isGameOver = ref(false)
const openGameOver = () => {
  isPreGameOver.value = false
  isGameOver.value = true
  emit('open-game-over')
}

const openBoostersSelector = () => {
  isOpenBoosterSelector.value = true
}
</script>

<template>
  <div
    class="absolute inset-0"
  >
    <PreGameOverWindow
      v-if="isPreGameOver"
      :isSessionUpdated="props.isSessionUpdated"
      @close="openGameOver"
    />
    <GameOverDialog
      v-if="isGameOver"
      v-bind="props"
      @restart="openBoostersSelector"
      @restart-tutorial="() => emit('restart')"
      @go-to-main-menu="() => emit('go-to-main-menu')"
    />
    <BoosterSelector
      v-if="isOpenBoosterSelector"
      @close="isOpenBoosterSelector = false"
      @start-game="(activeBoosters: StackableBoosterType[] = []) => emit('restart', activeBoosters)"
    />
  </div>
</template>

<style lang="scss">
</style>

<script setup lang="ts">
import VButton from '@/components/UI/VButton.vue';
import { useI18n } from 'vue-i18n'
import { useClanInfo, useStartClanEvent } from '@/services/client/useClans'
import AvatarItem from '@/components/UI/AvatarItem.vue';
import BalanceItem from '@/components/UI/BalanceItem.vue';
import { formatNumberToShortString } from '@/utils/number';
import
  LeaderboardList,
  { type LeaderboardCurrency } from '@/components/events/LeaderboardList.vue';
import { computed, ref, watch } from 'vue';
import { openTelegramLink } from '@telegram-apps/sdk';
import clanImage from '@/assets/images/temp/clan-badge.png'
import { cloudStorageService } from '@/shared/storage/cloudStorageService';
import ClanRewardScreen from '@/components/rewards/ClanRewardScreen.vue';
import { useToast } from '@/stores/toastStore';
import { useClanEventStore } from '@/stores/clanEventStore.ts'
import CountdownTimerManual from '@/components/UI/CountdownTimerManual.vue';
import InstructionButton from '@/components/UI/InstructionButton.vue';

const { t } = useI18n()

const props = defineProps<{
  userClan: number
  class?: string
}>()

const { isClanEventActive, days, hours, minutes, seconds } = useClanEventStore()
const { showToast } = useToast()
const { clanInfo } = useClanInfo(props.userClan)
const { startClanEvent } = useStartClanEvent()

const mappedUsersList = computed(() => {
  if (!clanInfo.value) return []
  return clanInfo.value.list.map(user => {
    return {
      id: user.id,
      name: (user.firstName + ' ' + user.lastName).trim(),
      league: user.leagueLevel,
      balance: user.rating,
      currency: 'tickets' as LeaderboardCurrency,
      isLeader: !!user.isClanLeader
    }
  })
})

const startEvent = () => {
  startClanEvent().then(() => {
    showToast('Clan Event has been started', 'info')
  })
}

const isOpenClanRewardScreen = ref(false)
const CURRENT_USER_CLAN_KEY = 'currentUserClan'
const showClanRewardScreen = (id: number) => {
  isOpenClanRewardScreen.value = true
  cloudStorageService.save(CURRENT_USER_CLAN_KEY, id)
  setTimeout(() => isOpenClanRewardScreen.value = false, 3000)
}
watch(clanInfo, (newValue) => {
  if (newValue?.clanId) {
    cloudStorageService.load(CURRENT_USER_CLAN_KEY).then(res => {
      if (!res || res !== newValue.clanId) {
        showClanRewardScreen(newValue.clanId)
      }
    })
  }
}, { immediate: true })
</script>

<template>
  <div v-if="clanInfo" class="flex flex-col" :class="props.class">
    <div class="flex-0 bg-[#00EEFF66] rounded-[11px] px-2 py-[10px] space-y-2 mb-3">
      <div class="flex items-center gap-x-3">
        <AvatarItem :src="clanImage" size="63" />
        <div class="space-y-3">
          <div class="text-[20px] text-shadow text-shadow_black">
            {{ clanInfo.clanName }}
          </div>
          <div class="flex gap-x-5">
            <BalanceItem
              icon-name="ticket-bg"
            >
              {{ formatNumberToShortString(clanInfo.rating) }}
            </BalanceItem>
            <BalanceItem
              icon-name="ref-bg"
            >
              {{ formatNumberToShortString(clanInfo.membersCount) }}
            </BalanceItem>
          </div>
        </div>
      </div>
      <div class="flex gap-x-2">
        <VButton
          v-if="clanInfo?.clanLink"
          class="!w-[70%] max-w-[250px] mx-auto"
          :text="t('actions.openChat')"
          type="success"
          size="medium"
          @click="() => clanInfo?.clanLink && openTelegramLink(clanInfo.clanLink)"
        />
      </div>
    </div>
    <div class="relative flex items-center justify-between gap-x-5 bg-[#C0C0C0] rounded-[11px] p-5 mb-3">
      <div class="flex-1">
        <p class="text-[20px] leading-[26px] text-shadow text-shadow_black text-shadow_thin">
          {{ t('clans.event.name') }}
        </p>
        <i18n-t
          class="text-[12px] text-shadow text-shadow_black text-shadow_thin text-white whitespace-pre"
          tag="p"
          keypath="clans.event.total_prize"
        >
          <template v-slot:ton>
            <span class="text-[26px] leading-[30px] text-[#FFD000]">100 TON</span>
          </template>
        </i18n-t>
      </div>
      <div class="flex-1">
        <CountdownTimerManual
          v-if="isClanEventActive"
          class="bg-[#07070773] rounded-[5px] px-[10px] py-[5px] text-[14px]"
          :days="days"
          :hours="hours"
          :minutes="minutes"
          :seconds="seconds"
        />
        <VButton
          v-else-if="clanInfo.isEventPossible"
          type="success"
          size="small"
          :text="t('actions.startEvent')"
          @click="startEvent"
        />
        <p class="text-[14px] leading-[18px] text-[#1D3161] whitespace-pre">
          {{ t('clans.event.requirenment') }}
        </p>
      </div>
      <InstructionButton
        class="absolute top-[5px] right-[5px]"
        :instruction-type="'clan-event-instruction'"
      />
    </div>
    <div class="flex-1 overflow-y-auto px-3 bg-[#003579] rounded-[11px]">
      <div class="clans-view__shadow-gradient"></div>
      <LeaderboardList
        :leaderboard="mappedUsersList"
        name-class="text-white text-shadow text-shadow_black"
      />
      <div class="clans-view__shadow-gradient"></div>
    </div>
  </div>
  <ClanRewardScreen
    :is-open="isOpenClanRewardScreen"
    :clan-name="clanInfo?.clanName ?? ''"
    @close="isOpenClanRewardScreen = false"
  />
</template>

<style lang="scss" scoped></style>

<script setup lang="ts">
import ConnectionDialog from '@/components/ConnectionDialog.vue'
import HeaderMenu from '@/components/HeaderMenu.vue'
import LivesBar from '@/components/lives/LivesBar.vue'
import LivesModal from '@/components/lives/LivesModal.vue'
import PlayerProfileModal from '@/components/profile/PlayerProfileModal.vue'
import LeagueRewardAutomatic from '@/components/rewards/LeagueRewardAutomatic.vue'
import BalanceItem from '@/components/UI/BalanceItem.vue'
import MultiplierBalanceItem from '@/components/UI/MultiplierBalanceItem.vue'
import { getCurrencyRealAmount } from '@/constants/currency.ts'
import { usePlayerState } from '@/services/client/usePlayerState'
import { sendAnalyticsEvent } from '@/utils/analytics'
import { formatNumberToShortString } from '@/utils/number'
import { customTruncate, formatNumberWithSeparator } from '@/utils/number.ts'
import { computed, ref } from 'vue'
import { RouterView, useRoute, useRouter } from 'vue-router'

const { isLoading, playerState } = usePlayerState()

const route = useRoute()
const router = useRouter()

const isWheelSpinPage = computed(() => route.path.includes('/wheel-spin'))

const isOpenLivesModal = ref(false)

const goToShopForHard = () => {
  sendAnalyticsEvent('go_to_shop', { from: 'header-hard' })
  router.push({ name: 'shop', query: { scrollTo: 'hard' } })
}
</script>

<template>
  <div
    class="main-view"
  >
    <RouterView v-slot="{ Component }">
      <Transition :name="'route-animation'" mode="out-in">
        <component :is="Component" />
      </Transition>
    </RouterView>
    <HeaderMenu class="!px-5">
      <div v-if="!isLoading && playerState" class="relative w-full h-full flex justify-between items-center">
        <div class="absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-full">
          <MultiplierBalanceItem
            id="multiplier-balance-header"
            :value="playerState.multiplier ?? 1"
          />
        </div>
        <BalanceItem
          id="hard-balance-header"
          iconName="hard-coin-bg"
          balance-class="!text-[14px] text-shadow text-shadow_shadow-only"
          add-button
          @add="goToShopForHard"
        >
          {{ formatNumberToShortString(playerState.hard ?? 0) }}
        </BalanceItem>
        <div class="absolute left-1/2 -translate-x-[50%]">
          <BalanceItem
            id="tickets-balance-header"
            iconName="ticket-bg"
            balance-class="!text-[14px] text-shadow text-shadow_shadow-only"
          >
            {{ formatNumberWithSeparator(playerState.tickets ?? 0) }}
          </BalanceItem>
        </div>
        <LivesBar
          v-if="!isWheelSpinPage"
          size="small"
          show-timer
          @add="isOpenLivesModal = true"
        />
        <BalanceItem
          v-else
          id="ton-balance-header"
          iconName="ton-bg"
          balance-class="!text-[14px] text-shadow text-shadow_shadow-only"
        >
          {{
            formatNumberWithSeparator(
              customTruncate(getCurrencyRealAmount(playerState.ton ?? 0, 'ton'))
            )
          }}
        </BalanceItem>
      </div>
    </HeaderMenu>
  </div>
  <ConnectionDialog />
  <PlayerProfileModal />
  <LivesModal :isOpen="isOpenLivesModal" @close="() => (isOpenLivesModal = false)" />
  <LeagueRewardAutomatic />
</template>

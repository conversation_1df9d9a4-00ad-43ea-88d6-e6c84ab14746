<script setup lang="ts">
import FooterHome from '@/components/FooterHome.vue';
import { RouterView } from 'vue-router'

</script>

<template>
  <div class="menu-container footer-home-wrapper" @click.stop>
    <div class="menu-view">
      <RouterView />
    </div>
    <div class="absolute bottom-0 left-0 w-full z-0">
      <FooterHome />
    </div>
  </div>
</template>

<style lang="scss">
.menu-container {
  width: 100%;
  height: 100%;
}

.menu-view {
  width: inherit;
  height: calc(100% - var(--footer-height));
  position: relative;
  z-index: 1;
}

// dont show the shadow when the route is animating
// showing the shadow during the animation causes a bug with shadow positioning
.route-animation-enter-active,
.route-animation-leave-active {
  .menu-view::before {
    display: none;
  }
}
</style>

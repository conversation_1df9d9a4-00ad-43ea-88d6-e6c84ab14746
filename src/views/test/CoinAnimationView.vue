<script lang="ts" setup>
import HeaderMenu from '@/components/HeaderMenu.vue';
import BalanceItem from '@/components/UI/BalanceItem.vue';
import { getCurrencyRealAmount } from '@/constants/currency';
import { customTruncate, formatNumberToShortString, formatNumberWithSeparator } from '@/utils/number';
import { usePlayerState } from '@/services/client/usePlayerState';
import VButton from '@/components/UI/VButton.vue';
import { coinCollectingAnimation } from '@/utils/coinCollectingAnimation'

const { playerState } = usePlayerState()
</script>

<template>
  <div class="w-full h-full flex flex-col items-center justify-between">
    <HeaderMenu>
      <template #left>
        <div v-if="playerState" class="relative flex gap-x-6 pl-4">
          <BalanceItem
            id="ton-balance-header"
            iconName="ton-bg"
            balance-class="text-shadow text-shadow_black text-shadow_thin"
          >
            {{
              formatNumberWithSeparator(
                customTruncate(getCurrencyRealAmount(playerState.ton ?? 0, 'ton'))
              )
            }}
          </BalanceItem>
          <BalanceItem
            id="soft-balance-header"
            iconName="soft-coin-bg"
            balance-class="text-shadow text-shadow_black text-shadow_thin"
          >
            {{ formatNumberToShortString(playerState.soft ?? 0) }}
          </BalanceItem>
          <BalanceItem
            id="hard-balance-header"
            iconName="hard-coin-bg"
            balance-class="text-shadow text-shadow_black text-shadow_thin"
          >
            {{ formatNumberToShortString(playerState.hard ?? 0) }}
          </BalanceItem>
        </div>
      </template>
    </HeaderMenu>
    <div></div>
    <div class="flex items-center relative -top-[100px]">
      <BalanceItem
        v-if="playerState"
        id="tickets-balance-header"
        iconName="ticket-bg"
        balance-class="text-shadow text-shadow_black text-shadow_thin"
      >
        {{ formatNumberToShortString(playerState.tickets ?? 0) }}
      </BalanceItem>
      <BalanceItem
        class="home-view__multiplier z-10"
        image-class="home-view__multiplier-image"
        balance-class="home-view__multiplier-balance"
        bar-class="home-view__multiplier-bar"
        iconName="multiplier-bg"
        gold
      >
        {{ formatNumberToShortString(playerState!.multiplier ?? 1) }}
      </BalanceItem>
    </div>
    <div class="grid grid-cols-2 grid-rows-2 gap-4 pb-4">
      <VButton
        type="accent"
        :text="'ton'"
        class="mx-auto min-w-[150px]"
        @click="() => coinCollectingAnimation(['#ton-balance-header'], 'ton')"
        size="medium"
      />
      <VButton
        type="accent"
        :text="'hard'"
        class="mx-auto min-w-[150px]"
        @click="() => coinCollectingAnimation(['#hard-balance-header'], 'hard')"
        size="medium"
      />
      <VButton
        type="accent"
        :text="'soft'"
        class="mx-auto min-w-[150px]"
        @click="() => coinCollectingAnimation(['#soft-balance-header'], 'soft')"
        size="medium"
      />
      <VButton
        type="accent"
        :text="'tickets'"
        class="mx-auto min-w-[150px]"
        @click="() => coinCollectingAnimation(['#tickets-balance-header'], 'tickets')"
        size="medium"
      />
    </div>
  </div>
</template>

<style lang="scss">
</style>

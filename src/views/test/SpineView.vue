<script lang="ts" setup>
import * as spine from '@esotericsoftware/spine-player'
import '@esotericsoftware/spine-player/dist/spine-player.min.css'
import { onMounted, ref } from 'vue';

const spinePlayer = ref<spine.SpinePlayer | null>(null);
const currentAnimation = ref('action_gift');
const skins = ['box/green', 'box/magic', 'box/pink', 'uni/uniBlue', 'uni/uniGreen', 'uni/uniViolet', 'uni/uniYellow']

onMounted(() => {
  new spine.SpinePlayer('player-container', {
    skeleton: '/spine/Lootbox/Box.json',
    atlas: '/spine/Lootbox/Box.atlas.txt',
    scale: 1,
    preserveDrawingBuffer: true,
    animation: 'action_uni',
    animations: ['action_gift', 'action_uni'],
    skin: 'uni/uniBlue',
    skins: skins,
    // showControls: false,
    // interactive: false,
    viewport: {
      x: -450,
      y: -50,
      width: 1000,
      height: 1000,
    },
    backgroundColor: '#00000000',
    alpha: true,
    showLoading: false,
    premultipliedAlpha: false,
    success: function (player) {
      spinePlayer.value = player
      // Тепер можна запускати анімацію
      player.setAnimation("action_uni", false)
    },
    error: function (player, reason) {
      alert(reason);
    }
  })
})

const onClick = () => {
  if (spinePlayer.value) {
    console.log('CLICK')
    currentAnimation.value = currentAnimation.value === 'action_gift' ? 'action_uni' : 'action_gift';
    spinePlayer.value.setAnimation(currentAnimation.value, true);
  }
}
</script>

<template>
  <div
    id="player-container"
    class="w-full h-full"
    @click="onClick"
  ></div>
</template>

<style lang="scss">
</style>

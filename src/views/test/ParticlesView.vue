<script lang="ts" setup>
import { type Container } from '@tsparticles/engine'

const particlesLoaded = async (container: Container) => {
  console.log("Particles container loaded", container);
};
</script>

<template>
  <vue-particles
    id="tsparticles"
    @particles-loaded="particlesLoaded"
    :options="{
      autoPlay: true,
      clear: true,
      fullScreen: {
        enable: true,
        zIndex: 0
      },
      detectRetina: true,
      fpsLimit: 60,
      particles: {
        bounce: {
          horizontal: {
            value: 1
          },
          vertical: {
            value: 1
          }
        },
        color: {
          value: '#00ff00',
          animation: {
            h: {
              count: 0,
              enable: true,
              speed: 50,
              decay: 0,
              delay: 0,
              sync: false,
              offset: 0
            },
          }
        },
        move: {
          angle: {
            offset: 0,
            value: 90
          },
          center: {
            x: 50,
            y: 50,
            mode: 'percent',
            radius: 0
          },
          decay: 0.1,
          distance: {},
          drift: 0,
          enable: true,
          gravity: {
            acceleration: 10,
            enable: true,
            inverse: false,
            maxSpeed: 50
          },
          outModes: {
            default: 'destroy',
            bottom: 'destroy',
            left: 'destroy',
            right: 'destroy',
            top: 'none'
          },
          random: false,
          size: false,
          speed: {
            min: 10,
            max: 20
          },
          straight: false,
          vibrate: false,
          warp: false
        },
        opacity: {
          value: {
            min: 0,
            max: 1
          },
          animation: {
            count: 0,
            enable: true,
            speed: 2,
            decay: 0,
            delay: 0,
            sync: false,
            mode: 'auto',
            startValue: 'max',
            destroy: 'min'
          }
        },
        reduceDuplicates: false,
        shape: {
          close: true,
          fill: true,
          options: {},
          type: 'square'
        },
        size: {
          value: {
            min: 2,
            max: 4
          },
        },
        stroke: {
          width: 0
        },
        zIndex: {
          value: 0,
          opacityRate: 1,
          sizeRate: 1,
          velocityRate: 1
        },
        destroy: {
          bounds: {},
          mode: 'none',
          split: {
            count: 1,
            factor: {
              value: 3
            },
            rate: {
              value: {
                min: 4,
                max: 9
              }
            },
            sizeOffset: true,
            particles: {}
          }
        },
        roll: {
          darken: {
            enable: true,
            value: 25
          },
          enable: true,
          mode: 'vertical',
          speed: {
            min: 15,
            max: 25
          }
        },
        tilt: {
          value: {
            min: 0,
            max: 360
          },
          animation: {
            enable: true,
            speed: 60,
            decay: 0,
            sync: false
          },
          direction: 'random',
          enable: true
        },
        wobble: {
          distance: 30,
          enable: true,
          speed: {
            angle: {
              min: -15,
              max: 15
            },
            move: 10
          }
        },
        life: {
          count: 1,
          delay: {
            value: 0,
            sync: false
          },
          duration: {
            value: 5,
            sync: true
          }
        },
        rotate: {
          value: {
            min: 0,
            max: 360
          },
          animation: {
            enable: true,
            speed: 60,
            decay: 0,
            sync: false
          },
          direction: 'random',
          path: false
        },
      },
      pauseOnBlur: true,
      pauseOnOutsideViewport: true,
      zLayers: 100,
      emitters: [
        {
          autoPlay: true,
          fill: true,
          delay: 10,
          life: {
            wait: true,
            count: 1,
            delay: 1,
            duration: 0.1
          },
          rate: {
            quantity: 150,
            delay: 0.1
          },
          startCount: 0,
          size: {
            mode: 'percent',
            height: 0,
            width: 0
          },
          particles: {
            move: {
              direction: 'none',
            }
          },
          position: {
            x: 0,
            y: 30
          }
        },
        {
          autoPlay: true,
          fill: true,
          delay: 10,
          life: {
            wait: true,
            count: 1,
            delay: 1,
            duration: 0.1
          },
          rate: {
            quantity: 150,
            delay: 0.1
          },
          startCount: 0,
          size: {
            mode: 'percent',
            height: 0,
            width: 0
          },
          particles: {
            move: {
              direction: 'none',
            }
          },
          position: {
            x: 100,
            y: 30
          }
        },
        {
          autoPlay: true,
          fill: true,
          life: {
            wait: true,
            count: 0,
            delay: 1,
            duration: 0.1
          },
          rate: {
            quantity: 100,
          },
          startCount: 0,
          size: {
            mode: 'percent',
            height: 0,
            width: 100
          },
          particles: {
            move: {
              direction: 'none',
            },
            life: {
              count: 1,
              delay: {
                value: 1,
                sync: false
              },
              duration: {
                value: 4,
                sync: false
              }
            },
            size: {
              value: {
                min: 5,
                max: 8
              },
            },
            shape: {
              close: true,
              fill: true,
              options: {},
              type: 'square'
            },
          },
          position: {
            x: 50,
            y: -20
          }
        },
        {
          autoPlay: true,
          fill: true,
          life: {
            wait: false,
            count: 1,
            delay: 0.1,
            duration: 0.1
          },
          rate: {
            quantity: 150,
            delay: 0.1
          },
          startCount: 0,
          size: {
            mode: 'percent',
            height: 0,
            width: 0
          },
          particles: {
            move: {
              direction: 'none',
            }
          },
          position: {
            x: 50,
            y: 30
          }
        },
      ]
    }"
  />
</template>

<style lang="scss">
</style>

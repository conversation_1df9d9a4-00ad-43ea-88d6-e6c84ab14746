<script setup lang="ts">
import VTabs from '@/components/UI/VTabs.vue'
import AirdropView from './AirdropView.vue'
import WalletView from './WalletView.vue'
</script>

<template>
  <div class="view-container">
    <VTabs
      :tabs="[
        { name: 'airdrop.title', id: 'airdrop' },
        { name: 'wallet.title', id: 'wallet' },
      ]"
    >
      <template #airdrop><AirdropView /></template>
      <template #wallet><WalletView /></template>
    </VTabs>
  </div>
</template>

<style lang="scss" scoped>
</style>

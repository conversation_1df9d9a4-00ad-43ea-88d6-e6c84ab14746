<script setup lang="ts">
import SkinsStore from '@/components/skins/SkinsStore.vue'
import SkinCollections from '@/components/skins/SkinCollections.vue'
import VTabs from '@/components/UI/VTabs.vue';

</script>

<template>
  <div class="view-container me-view bottom-gradient">
    <VTabs
      :tabs="[
        { name: 'me.skins', id: 'skins' },
        { name: 'me.collections', id: 'collections' },
      ]"
    >
      <template #skins><SkinsStore /></template>
      <template #collections><SkinCollections /></template>
    </VTabs>
  </div>
</template>

<style lang="scss">
.me-view {
  --bottom-position: 36px;
}
</style>

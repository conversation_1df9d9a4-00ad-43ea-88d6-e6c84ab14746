<script setup lang="ts">
import VTabs from '@/components/UI/VTabs.vue'
import JackpotInfo from '@/components/events/jackpot/JackpotInfo.vue'
import JackpotResults from '@/components/events/jackpot/JackpotResults.vue'

</script>

<template>
  <div class="view-container !pb-0">
    <VTabs
      class="jackpot-tabs"
      :tabs="[
        { name: 'jackpot.title', id: 'info' },
        { name: 'jackpot.results', id: 'results' }
      ]"
    >
      <template #info><JackpotInfo class="jackpot-view" /></template>
      <template #results><JackpotResults class="jackpot-view" /></template>
    </VTabs>
  </div>
</template>

<style lang="scss">
.jackpot-view {
  height: 100%;
  width: inherit;
  overflow: visible;
  position: relative;
  padding: 22px 7px 10px;
}

.jackpot-tabs {
  .tabs-group__panels-wrapper {
    background-image: url('@/assets/images/temp/jackpot/background.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
  
  .tabs-group__tab {
    background: linear-gradient(180.96deg, #437BC7 -119.77%, #83FBFF 91.41%);
    box-shadow: 0px -4px 0px 0px #D8FDFF inset;

    &_active {
      background: linear-gradient(180deg, #45349D 0%, #4B38A5 106.86%);
      box-shadow: 0px -4px 0px 0px #7DC3FF52 inset;
    }
  }
}
</style>

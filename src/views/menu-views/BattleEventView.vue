<script setup lang="ts">
import { computed } from 'vue'

import { useWindowQueue } from '@/composables/useWindowQueue'
import { getCurrencyRealAmount } from '@/constants/currency'
import { useBattleEventUserInfo, useBattleEventLeaders } from '@/services/client/useGameEvent'
import { useToast } from '@/stores/toastStore'
import { formatNumberToShortString } from '@/utils/number'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import ModalWindowTabs from '@/components/UI/ModalWindowTabs.vue'
import { BATTLE_EVENT_TEAMS } from '@/constants/events'
import BalanceItem from '@/components/UI/BalanceItem.vue'
import AvatarItem from '@/components/UI/AvatarItem.vue'
import CountdownTimerManual from '@/components/UI/CountdownTimerManual.vue'
import { useCommunityBattleStore } from '@/stores/communityBattleStore'
import LeaderboardList from '@/components/events/LeaderboardList.vue'
import coinIcon from '@/assets/images/temp/battle-event/coin.png'
import LoaderText from '@/components/LoaderText.vue'
import SkinItem from '@/components/skins/SkinItem.vue'
import { SKIN_ID_TO_IMAGE } from '@/constants/skins'
import { useBattleEventBoosters } from '@/services/client/useBattleEventBoosters'
import MissionItem from '@/components/missions/MissionItem.vue'
import type { Price, RewardInfo } from '@/services/openapi'
import { usePurchase } from '@/composables/usePurchase'
import { useClaimEventBooster } from '@/services/client/useClaimEventBooster'

import jumpingImage from '@/assets/images/temp/battle-event/jumping.png'
import partnersImage from '@/assets/images/temp/battle-event/partners.png'
import luckImage from '@/assets/images/temp/battle-event/luck.png'
import powerImage from '@/assets/images/temp/battle-event/power.png'
import magicImage from '@/assets/images/temp/battle-event/magic.png'
import eventBanner from '@/assets/images/temp/battle-event/banner.png'
import InstructionButton from '@/components/UI/InstructionButton.vue'
import { hapticsService } from '@/shared/haptics/hapticsService'

const BOOSTER_NAME_TO_IMAGE: Record<string, string> = {
  jumping: jumpingImage,
  luck: luckImage,
  power: powerImage,
  magic: magicImage,
  partners: partnersImage
}

const { t } = useI18n()

const { showToast } = useToast()
const router = useRouter()
const communityBattleStore = useCommunityBattleStore()

const {
  leaderboard,
  score,
  teams,
  isLoading: isLoadingLeaderboard,
} = useBattleEventLeaders(() => {
  showToast('Event has ended', 'warning')
  close()
})

const {
  userInfo,
  isLoading: isLoadingUserInfo,
  isFetching: isFetchingUserInfo
} = useBattleEventUserInfo()

const {
  boosters: boostersData,
  isLoading: isLoadingBoosters,
  isFetching: isFetchingBoosters,
} = useBattleEventBoosters()
const { purchaseEventBoost: purchaseEventBoostMutation, isPendingPurchaseEventBoost } = usePurchase()
const { claim } = useClaimEventBooster()

const { closeWindowInQueue } = useWindowQueue('community-battle-window')

const currentMultiplier = computed(() => {
  if (!boostersData.value) return 0
  const paidMultiplier = boostersData.value.paidBoosters.reduce((acc, booster) => acc + booster.currentMultiplier, 0)
  const taskMultiplier = boostersData.value.taskBoosters.reduce((acc, booster) => booster.isClaimed ? acc + booster.multiplier : acc, 0)
  return paidMultiplier + taskMultiplier
})
const maxMultiplier = computed(() => {
  if (!boostersData.value) return 0
  const paidMultiplier = boostersData.value.paidBoosters.reduce((acc, booster) => acc + booster.totalMultiplier, 0)
  const taskMultiplier = boostersData.value.taskBoosters.reduce((acc, booster) => acc + booster.multiplier, 0)
  return paidMultiplier + taskMultiplier
})

const mappedLeaderboard = computed(() => {
  return leaderboard.value?.map(user => {
    return {
      id: user.id,
      name: user.name,
      league: user.leagueLevel,
      balance: getCurrencyRealAmount(user.reward?.amount ?? 0, user.reward?.currency ?? 'hard'),
      currency: user.reward?.currency ?? 'hard',
      value: formatNumberToShortString(user.value ?? 0)
    }
  })
})

const userData = computed(() => {
  return {
    rank: userInfo.value?.rank ?? undefined,
    score: (userInfo.value?.totalScore ?? 0).toString(),
    balance: userInfo.value?.reward?.amount ?? 0,
    currency: userInfo.value?.reward?.currency ?? 'hard',
    league: userInfo.value?.leagueLevel ?? 0,
  }
})

const purchaseEventBoost = (id: number, price: Price) => {
  purchaseEventBoostMutation(id, 'battle', price)
    .then(() => {
      hapticsService.triggerImpactHapticEvent('heavy')
    })
    .catch(reason => {
      if (reason?.message === 'NOT_ENOUGH_FUNDS') {
        router.push({ name: 'shop', query: { scrollTo: price.currency } })
      }
    })
}

const goToPartners = () => {
  router.push({ name: 'earn', query: { scrollTo: 'partners' } })
}

const close = () => {
  closeWindowInQueue()
  router.back()
}
</script>

<template>
  <ModalWindowTabs
    class="battle-event-window"
    :is-open="true"
    :tabs="[
      { name: 'battle_event.team_score', id: 'team_score' },
      { name: 'battle_event.boost.title', id: 'boost' },
    ]"
    @close="close"
  >
    <template #default>
      <img :src="eventBanner" class="absolute top-[30px] left-1/2 -translate-x-1/2 -translate-y-full w-[80%] z-10" />

      <InstructionButton
        class="absolute top-[10px] left-[10px]"
        instruction-type="battle-event-instruction"
        :instruction-check="false"
      />
    </template>
    <template #team_score>
      <div v-if="isLoadingLeaderboard || isLoadingUserInfo || isFetchingUserInfo" class="flex-1 flex items-center justify-center">
        <LoaderText isLoading />
      </div>
      <div v-else class="flex-1 flex flex-col min-h-0">
        <div
          class="community-battle-banner__box w-full px-[6px] py-3 mb-1"
        >
          <div class="community-battle-banner__timer w-fit !absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2">
            <h1
              class="text-white text-center text-[12px] leading-[17px]"
            >
              <CountdownTimerManual
                class="text-[12px] leading-[16px] text-[#FFFFFF]"
                :days="communityBattleStore.days"
                :hours="communityBattleStore.hours"
                :minutes="communityBattleStore.minutes"
                :seconds="communityBattleStore.seconds"
                digital
              />
            </h1>
          </div>

          <div class="flex justify-between items-center gap-x-2 w-full mb-3">
            <div class="flex-1 flex items-center gap-x-1 min-w-0">
              <div class="flex-0">
                <AvatarItem class="flex-0 -scale-x-100" size="40" :src="BATTLE_EVENT_TEAMS[teams.teamSkin].avatar" />
              </div>
              <div class="space-y-1 min-w-0">
                <div class="text-[14px] text-shadow text-shadow_black text-shadow_thin truncate px-[2px]">
                  {{ BATTLE_EVENT_TEAMS[teams.teamSkin].name }}
                </div>
                <div class="flex gap-x-5 pl-2">
                  <BalanceItem
                    icon-name="battle-coin-bg"
                    image-class="!w-[23px] !h-[23px]"
                  >
                    <p class="text-shadow text-shadow_black text-shadow_thin">{{ formatNumberToShortString(score.teamScore) }}</p>
                  </BalanceItem>
                </div>
              </div>
            </div>
            <p class="text-[32px] text-shadow text-shadow_black text-shadow_thin">VS</p>
            <div class="flex-1 flex items-center gap-x-1 -scale-x-100 min-w-0">
              <div class="flex-0">
                <AvatarItem class="flex-0 -scale-x-100" size="40" :src="BATTLE_EVENT_TEAMS[teams.enemyTeamSkin].avatar" />
              </div>
              <div class="space-y-1 min-w-0">
                <div class="text-[14px] text-shadow text-shadow_black text-shadow_thin truncate px-[2px] -scale-x-100">
                  {{ BATTLE_EVENT_TEAMS[teams.enemyTeamSkin].name }}
                </div>
                <div class="flex gap-x-5 pl-2">
                  <BalanceItem
                    icon-name="battle-coin-bg"
                    image-class="!w-[23px] !h-[23px] battle-coin-mirrored"
                  >
                    <p class="text-shadow text-shadow_black text-shadow_thin -scale-x-100">{{ formatNumberToShortString(score.enemyTeamScore) }}</p>
                  </BalanceItem>
                </div>
              </div>
            </div>
          </div>
          <div class="community-battle-banner__progress-bar flex justify-between items-center gap-x-px w-full">
            <div
              :style="{ '--team-percent': score.teamPercent }"
              class="community-battle-banner__progress-bar__item"
            >
              {{ score.teamPercent }}%
            </div>
            <div
              class="community-battle-banner__progress-bar__item"
            >
              {{ score.enemyTeamPercent }}%
            </div>
          </div>
        </div>
        <div class="flex flex-col flex-1 overflow-hidden px-3 rounded-[11px]">
          <p class="text-[14px] text-center text-shadow text-shadow_thin my-1">
            {{ BATTLE_EVENT_TEAMS[teams.teamSkin].name }}
          </p>
          <div class="flex-1 overflow-y-auto">
            <LeaderboardList
              class="battle-event-window__leaderboard"
              :leaderboard="mappedLeaderboard"
              :scoreTypeImage="coinIcon"
              :user="userData"
            />
          </div>
        </div>
      </div>
    </template>
    <template #boost>
      <div v-if="isLoadingBoosters" class="flex-1 flex items-center justify-center">
        <LoaderText isLoading />
      </div>
      <div v-else class="flex-1 flex flex-col min-h-0">
        <div class="relative flex items-center justify-between grow-0 shrink-0 basis-[170px]">
          <div class="community-battle-banner__timer w-fit !absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2">
            <h1
              class="text-white text-center text-[12px] leading-[17px]"
            >
              <CountdownTimerManual
                class="text-[12px] leading-[16px] text-[#FFFFFF]"
                :days="communityBattleStore.days"
                :hours="communityBattleStore.hours"
                :minutes="communityBattleStore.minutes"
                :seconds="communityBattleStore.seconds"
                digital
              />
            </h1>
          </div>
          <div class="flex-1 aspect-square flex items-center justify-center">
            <SkinItem
              :src="SKIN_ID_TO_IMAGE[userInfo?.teamSkin ?? 0]"
              class="w-[120px]"
              animated
            />
          </div>
          <div class="flex-1 community-battle-banner__box rounded-[8px] p-3">
            <p class="text-[12px] text-center font-extrabold leading-[20px] mb-2">
              {{ t('battle_event.boost.instruction') }}
            </p>
            <BalanceItem
              class="mx-auto"
              icon-name="multiplier-bg"
              image-class="!w-[38px] !h-[38px]"
              bar-class="!h-[32px]"
              balance-class="!text-[28px]"
            >
              <p class="text-shadow text-shadow_black text-shadow_thin">
                {{ currentMultiplier }}/{{ maxMultiplier }}
              </p>
            </BalanceItem>
          </div>
        </div>
        <div class="flex flex-col flex-1 overflow-hidden bg-[#003579B2] rounded-[11px]">
          <p class="text-[14px] font-extrabold text-center my-1">
            {{ t('battle_event.boost.description') }}
          </p>
          <div class="flex-1 overflow-y-auto space-y-2 px-1 pb-1">
            <MissionItem
              v-for="booster in boostersData?.paidBoosters"
              class="battle-event-window__mission"
              :key="booster.id"
              :id="booster.id"
              :image="BOOSTER_NAME_TO_IMAGE[booster.name]"
              :description="t(`battle_event.missions.${booster.name}`)"
              :reward="booster.nextLevel?.multiplier
                ? ({
                  type: 'multiplier',
                  value: booster.nextLevel.multiplier
                } as unknown as RewardInfo)
                : ({
                    type: 'multiplier',
                    value: booster.currentMultiplier
                  } as unknown as RewardInfo)
                "
              :is-done="booster.maxLevel === booster.currentLevel"
              :is-collected="booster.maxLevel === booster.currentLevel"
              :progress="{
                current: booster.currentLevel,
                goal: booster.maxLevel
              }"
              :price="booster.nextLevel?.price ?? undefined"
              :isPurchasing="isPendingPurchaseEventBoost || isFetchingBoosters"
              stackable-progress
              @purchase="purchaseEventBoost"
            />
            <MissionItem
              v-for="booster in boostersData?.taskBoosters"
              class="battle-event-window__mission battle-event-window__mission_partners"
              :key="booster.id"
              :id="booster.id"
              :image="BOOSTER_NAME_TO_IMAGE[booster.name]"
              :description="t(`battle_event.missions.${booster.name}`)"
              :reward="({
                type: 'multiplier',
                value: booster.multiplier
              } as unknown as RewardInfo)"
              :is-done="booster.maxLevel === booster.currentLevel"
              :is-collected="booster.isClaimed"
              :progress="{
                current: booster.currentLevel,
                goal: booster.maxLevel
              }"
              :isPurchasing="isPendingPurchaseEventBoost || isFetchingBoosters"
              stackable-progress
              @click="(id) => {
                booster.maxLevel === booster.currentLevel ? claim(id) : goToPartners()
              }"
            />
          </div>
        </div>
      </div>
    </template>
  </ModalWindowTabs>
</template>

<style lang="scss">
.battle-event-window {
  height: 90%;
  max-height: 600px;
  max-width: 400px;
  transform: translateY(4%);

  .modal-window-tab {
    &__panels {
      background: linear-gradient(180deg, #23D0F9 28.51%, #278CDF 100%);
      border: 5px solid #1A5FA1;
      border-bottom: 0px;
      box-shadow: none;
    }

    &__tab {
      border: 5px solid #1A5FA1;
      border-top: 0px;
      background: linear-gradient(180deg, #83FBFF 40%, #83FBFF 92%);
      box-shadow: 0px -4px 0px 0px #74C2E5 inset;

      &_active {
        background: linear-gradient(180deg, #278CDF 0%, #0074C6 100%);
        box-shadow: 0px -4px 0px 0px #2467A6 inset;
      }

      &:first-child {
        border-right-width: 2.5px;
      }

      &:last-child {
        border-left-width: 2.5px;
      }
    }
  }

  &__mission {
    .mission {
      padding-left: 4px;
      // background: linear-gradient(360deg, #FDF7DF 0%, #F4DCB3 92.65%);
      // border-color: #032F70;
      // box-shadow: 0px -6px 0px 0px #F4DCB3 inset,
      //   0px 4px 0px 0px #FFFFFF inset,
      //   0px 2px 0px 0px #00000033;

      &__title {
        // color: #E05B3E;
      }

      &__progress {
        // background: #B35F29;
      }

      &__reward-bar::after {
        // background: #BE9371;
        // box-shadow: 0px -12px 0px 0px #00000012 inset;
      }

      &__chevron {
        // background: url('@/assets/images/temp/cevron-right-icon_creme.svg');
      }
    }


    .mission__progress-inner-wrapper {
      padding: 2px 12px 2px 1px;
    }

    &_partners {
      .mission__controls {
        flex: 0 0 40px;
      }
    }
  }

  &__leaderboard {
    .scoreboard-item {
      gap: 5px;

      &__reward-bar {
        min-width: 55px;
      }
    }
  }

  .close-button {
    --close-btn-background-color: #2666A4;
  }

  .battle-coin-mirrored .icon-bg.battle-coin-bg {
    transform: scaleX(-1);
  }
}
</style>

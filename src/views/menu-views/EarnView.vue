<script setup lang="ts">
import VTabs from '@/components/UI/VTabs.vue'
import AchievementsView from '@/components/achievements/AchievementsView.vue'
import MissionsPage from '@/components/missions/MissionsPage.vue'
import { usePlayerState } from '@/services/client/usePlayerState.ts'
import { computed } from 'vue'

const { playerState } = usePlayerState()

const tasksUnclaimed = computed(() => {
  return !!playerState.value?.hasUnclaimedTasks || !!playerState.value?.hasUnclaimedDailyTasks
})
const achievementsUnclaimed = computed(() => {
  return !!playerState.value?.hasUnclaimedAchievements
})
</script>

<template>
  <div class="earn-view view-container">
    <VTabs
      is-shadow
      :tabs="[
        { name: 'earn.name', id: 'tasks', notification: tasksUnclaimed },
        { name: 'achievements.title', id: 'achievements', notification: achievementsUnclaimed }
      ]"
    >
      <template #tasks><MissionsPage /></template>
      <template #achievements><AchievementsView /></template>
    </VTabs>
  </div>
</template>

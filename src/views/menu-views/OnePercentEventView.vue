<script setup lang="ts">
import { computed, nextTick, watch } from 'vue'

import eventBanner from '@/assets/images/temp/onepercent/banner.png'
import EventWindow from '@/components/events/EventWindow.vue'
import { useWindowQueue } from '@/composables/useWindowQueue'
import { getCurrencyRealAmount } from '@/constants/currency'
import { ONE_PERCENT_INSTRUCTION } from '@/constants/instructions.ts'
import { useOnepercentLeaderboard, useOnepercentUserInfo } from '@/services/client/useGameEvent'
import { usePlayerState } from '@/services/client/usePlayerState'
import { useOnepercentRankStore } from '@/stores/eventRankStore'
import { useToast } from '@/stores/toastStore'
import { sendAnalyticsEvent } from '@/utils/analytics'
import { formatNumberToShortString } from '@/utils/number'
import { useRouter } from 'vue-router'

const { showToast } = useToast()
const router = useRouter()

const { leaderboard, isLoading: isLoadingLeaderboard } = useOnepercentLeaderboard(() => {
  showToast('Event has ended', 'warning')
  close()
})

const {
  userInfo,
  isLoading: isLoadingUserInfo,
  isFetching: isFetchingUserInfo
} = useOnepercentUserInfo()
const { playerState } = usePlayerState()

const { closeWindowInQueue } = useWindowQueue('onepercent-welcome-window')

const rankStore = useOnepercentRankStore()

// on each event page open
const unwatch = watch(
  [userInfo, isFetchingUserInfo],
  ([newUserInfo, newIsFetching]) => {
    if (newUserInfo?.rank && !newIsFetching) {
      const rank = newUserInfo.rank
      const totalScore = newUserInfo.totalScore
      rankStore.updateLastRank(rank)
      sendAnalyticsEvent('event_view', {
        event: 'onepercent',
        current_position: rank,
        total_points: totalScore ?? 0
      })
      nextTick(() => {
        unwatch()
      })
    }
  },
  { immediate: true }
)

const targetScore = computed(() => {
  return playerState.value?.onepercentEvent?.targetTotalScore ?? 0
})

const userData = computed(() => {
  return {
    rank: userInfo.value?.rank ?? undefined,
    score: formatNumberToShortString(userInfo.value?.totalScore ?? 0),
    balance: userInfo.value?.reward
      ? getCurrencyRealAmount(userInfo.value.reward.amount, userInfo.value.reward.currency)
      : 0,
    currency: userInfo.value?.reward?.currency ?? 'hard',
    league: userInfo.value?.leagueLevel ?? 1
  }
})

const close = () => {
  closeWindowInQueue()
  router.back()
}
</script>

<template>
  <EventWindow
    class="onepercent-event"
    id="onepercentEventWindow"
    :leaderboard="leaderboard"
    :isLoading="isLoadingLeaderboard || isLoadingUserInfo"
    :userInfo="userData"
    :eventBanner="eventBanner"
    :instruction-type="ONE_PERCENT_INSTRUCTION"
    @close="close"
  >
    <template #description>
      <i18n-t
        class="text-[12px] leading-[16px] text-white text-center"
        tag="p"
        keypath="onepercent.description"
      >
        <template v-slot:targetScore>
          <span class="text-[#FFE134]">
            {{ formatNumberToShortString(targetScore) }}
          </span>
        </template>
      </i18n-t>
    </template>
  </EventWindow>
</template>

<style lang="scss">
.onepercent-event {
  top: 0;
  --event-background: linear-gradient(360deg, #6900f7 0%, #be67e4 92.65%);
  --event-list-top-shadow: linear-gradient(180deg, #b65ce5 14.82%, rgba(183, 94, 229, 0) 68.56%);
  --event-list-bottom-shadow: linear-gradient(360deg, #6a02f6 14.82%, rgba(113, 10, 245, 0) 68.56%);

  .event-view {
    .close-button {
      --close-btn-background-color: #7300aa;
    }
  }
}
</style>

import { REWARD_TO_IMAGE_CLASS } from '@/composables/useIconImage'
import type { RewardType } from '@/services/openapi'

export function coinCollectingAnimation(targets: string[], coinType: RewardType, from?: string) {
  console.log('coinCollectingAnimation', targets, coinType)
  let targetElement: HTMLElement | null = null
  for (const target of targets) {
    targetElement = document.querySelector(target)
    if (targetElement) break
  }
  const coinImageClass = REWARD_TO_IMAGE_CLASS[coinType]
  if (!targetElement || !coinImageClass) return

  const targetX = targetElement.getBoundingClientRect().x
  const targetY = targetElement.getBoundingClientRect().y

  const fromElement = from ? document.querySelector(from) : null
  const startPositionX = fromElement
    ? fromElement.getBoundingClientRect().x + fromElement.getBoundingClientRect().width / 2
    : window.innerWidth / 2
  const startPositionY = fromElement
    ? fromElement.getBoundingClientRect().y + fromElement.getBoundingClientRect().height / 2
    : window.innerHeight / 2

  const moveToX = targetX - startPositionX
  const moveToY = targetY - startPositionY
  const mainImageClass = 'animated-coin'
  const mainImage2Class = 'icon-bg'

  for (let i = 0; i < 7; i++) {
    const coinImageElement = document.createElement('div')
    const firstMoveDirectionX = Math.floor(Math.random() * 101) - 50
    const firstMoveDirectionY = Math.floor(Math.random() * 101) - 50
    coinImageElement.style.setProperty('--first-move-to-x', firstMoveDirectionX + 'px')
    coinImageElement.style.setProperty('--first-move-to-y', firstMoveDirectionY + 'px')
    coinImageElement.style.setProperty('--second-move-to-x', moveToX + 'px')
    coinImageElement.style.setProperty('--second-move-to-y', moveToY + 'px')
    coinImageElement.style.setProperty('--move-delay', 0.05 * i + 's')
    coinImageElement.style.setProperty('left', startPositionX + 'px')
    coinImageElement.style.setProperty('top', startPositionY + 'px')

    coinImageElement.classList.add(mainImageClass, mainImage2Class, coinImageClass)
    document.body.appendChild(coinImageElement)
    setTimeout(() => {
      document.body.removeChild(coinImageElement)
    }, 1500)
  }
}

import Phaser from 'phaser'

type BumperDef = { x: number; y: number; r: number; score: number }

export class PinballMatterScene extends Phaser.Scene {
  private score = 0
  private ballsLost = 0
  private ball!: Phaser.Physics.Matter.Image
  private scoreText!: Phaser.GameObjects.Text
  private infoText!: Phaser.GameObjects.Text
  private tableW = 720
  private tableH = 1280
  private drainSensor!: any

  constructor() {
    super({
      key: 'PinballMatterScene',
      physics: {
        default: 'matter',
        matter: {
          debug: false,
          gravity: { y: 1 },            // м’яка гравітація під пінбол
          positionIterations: 6,
          velocityIterations: 4
        }
      }
    })
  }

  preload() {
    const g = this.add.graphics()
    g.fillStyle(0xffffff, 1).fillCircle(8, 8, 8).generateTexture('ballTex', 16, 16).clear()
    g.fillStyle(0xaaaaaa, 1).fillRect(0, 0, 16, 16).generateTexture('wallTex', 16, 16).clear()
    g.fillStyle(0xffcc00, 1).fillCircle(10, 10, 10).generateTexture('bumperTex', 20, 20).clear()
    g.fillStyle(0x66ccff, 1).fillRect(0, 0, 18, 60).generateTexture('plungerTex', 18, 60).clear()
  }

  create() {
    this.cameras.main.setBackgroundColor('#0f1220')
    this.matter.world.setBounds(0, 0, this.tableW, this.tableH, 32, true, true, true, false) // низ відкритий

    this.buildTableGeometry()
    this.buildBumpers()
    this.buildDrainSensor()

    this.scoreText = this.add.text(16, 16, 'Score: 0', { color: '#ffffff', fontSize: '20px' }).setDepth(1000)
    this.infoText = this.add.text(16, 44, 'Balls lost: 0', { color: '#9bb0ff', fontSize: '16px' }).setDepth(1000)

    this.spawnBall()

    // Авто-плунжер: періодично “вистрілює” кулю знизу вгору
    this.time.addEvent({
      delay: 900,
      loop: true,
      callback: () => this.kickPlunger()
    })

    // Колізії для нарахування очок та дрейну
    this.matter.world.on('collisionstart', (event: any) => {
      event.pairs.forEach((pair: any) => {
        const bodyA = pair.bodyA
        const bodyB = pair.bodyB

        // Перевірка дрейну
        if ((bodyA === this.drainSensor && this.isBall(bodyB)) ||
            (bodyB === this.drainSensor && this.isBall(bodyA))) {
          this.handleDrain()
        }

        // Нарахування очок
        this.tryScore(bodyA, bodyB)
        this.tryScore(bodyB, bodyA)
      })
    })
  }

  private buildTableGeometry() {
    // Похилі стінки зверху (класичний ковш пінбола)
    const wallOpts = { isStatic: true, restitution: 0.2, friction: 0.1, label: 'wall' }

    const addWall = (x: number, y: number, w: number, h: number, angleDeg = 0) => {
      const rect = this.matter.add.rectangle(x, y, w, h, wallOpts)
      this.matter.body.setAngle(rect, Phaser.Math.DEG_TO_RAD * angleDeg)
      this.add.rectangle(x, y, w, h, 0x5a637a, 1).setAngle(angleDeg)
    }

    // Бокові стіни
    addWall(8, this.tableH / 2, 16, this.tableH - 80)     // ліва
    addWall(this.tableW - 8, this.tableH / 2, 16, this.tableH - 80) // права

    // Верхня арка — дві похилі
    addWall(this.tableW * 0.25, 120, 280, 16, -25)
    addWall(this.tableW * 0.75, 120, 280, 16, 25)

    // Невелика “полиця” над дрейном, щоб кулька інколи відбивалась
    addWall(this.tableW * 0.5, this.tableH - 120, 220, 10, 0)

    // Плунжерний канал праворуч
    addWall(this.tableW - 36, this.tableH / 2, 12, this.tableH - 200, 0)
    addWall(this.tableW - 72, this.tableH - 140, 72, 10, 0)

    // Візуальний плунжер
    this.add.image(this.tableW - 36, this.tableH - 170, 'plungerTex').setDepth(2)
  }

  private buildBumpers() {
    const bumpers: BumperDef[] = [
      { x: this.tableW * 0.35, y: 340, r: 26, score: 50 },
      { x: this.tableW * 0.65, y: 360, r: 26, score: 50 },
      { x: this.tableW * 0.50, y: 500, r: 30, score: 100 },
      { x: this.tableW * 0.30, y: 620, r: 22, score: 25 },
      { x: this.tableW * 0.70, y: 640, r: 22, score: 25 }
    ]

    bumpers.forEach(b => {
      const body = this.matter.add.circle(b.x, b.y, b.r, {
          isStatic: true,
          restitution: 1.05, // сильний відскок
          friction: 0.0,
          label: 'bumper'
        })
      ;(body as any).score = b.score

      this.add.image(b.x, b.y, 'bumperTex').setDisplaySize(b.r * 2, b.r * 2).setDepth(1)
      // Декоративне світіння при хіті (через particle/tween можна додати пізніше)
    })
  }

  private buildDrainSensor() {
    // “Ротовина” знизу, що ловить кулю
    this.drainSensor = this.matter.add.rectangle(this.tableW * 0.5, this.tableH - 12, this.tableW * 0.7, 24, {
      isStatic: true,
      isSensor: true,
      label: 'drain'
    })
  }

  private spawnBall() {
    // Стартуємо в плунжерному каналі
    const x = this.tableW - 50
    const y = this.tableH - 220

    if (!this.ball || !this.ball.body) {
      const img = this.add.image(x, y, 'ballTex').setDisplaySize(18, 18).setDepth(2)
      this.ball = this.matter.add.gameObject(img, {
        restitution: 0.9,
        friction: 0.01,
        frictionAir: 0.0015,
        label: 'ball'
      }) as Phaser.Physics.Matter.Image
      this.ball.setCircle(9)
    } else {
      this.resetBall(x, y)
    }
  }

  private resetBall(x: number, y: number) {
    this.ball.setPosition(x, y)
    this.ball.setVelocity(0, 0)
    this.ball.setAngularVelocity(0)
    this.ball.setIgnoreGravity(false)
  }

  private kickPlunger() {
    if (!this.ball?.body) return
    // Якщо кулька внизу праворуч (плунжерний канал) — штовхаємо вгору
    const pos = this.ball.body.position
    if (pos.x > this.tableW - 90 && pos.y > this.tableH - 280) {
      // легкий імпульс вгору + трохи вліво
      this.ball.applyForce({ x: -0.003, y: -0.018 })
    }
  }

  private tryScore(a: any, b: any) {
    if (a.label !== 'bumper') return
    if (!this.isBall(b)) return
    const add = (a as any).score ?? 10
    this.score += add
    this.scoreText.setText(`Score: ${this.score}`)
    // Невеликий додатковий імпульс для “соки”
    const nx = (b.position.x - a.position.x)
    const ny = (b.position.y - a.position.y)
    const len = Math.max(0.0001, Math.hypot(nx, ny))
    const fx = (nx / len) * 0.002
    const fy = (ny / len) * 0.002
    this.ball.applyForce({ x: fx, y: fy })
  }

  private handleDrain() {
    this.ballsLost += 1
    this.infoText.setText(`Balls lost: ${this.ballsLost}`)
    // Респавн через мить
    this.time.delayedCall(400, () => this.spawnBall())
  }

  private isBall(body?: any) {
    return !!body && body.label === 'ball'
  }
}

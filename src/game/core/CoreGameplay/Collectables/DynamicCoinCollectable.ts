import type { CollectableOptions } from '@/game/core/CoreGameplay/Collectables/CollectableOptions'
import { Coin<PERSON>kins } from '@/game/core/CoreGameplay/Collectables/TokenCollectable'
import { cachedPlayerState } from '@/shared/storage/cachedPlayerState'
import { SpineCoinCollectable, type CoinConfig } from './SpineCoinCollectable'

export class DynamicCoinCollectable extends SpineCoinCollectable {
  constructor(options: CollectableOptions) {
    const config: CoinConfig = {
      skinSelector: () =>
        cachedPlayerState.currentProgressiveOffer == 10000 ? CoinSkins.Moon : CoinSkins.Dive,
      platformOffsetCalculator: () => 25
    }
    super(options, config)
  }
}

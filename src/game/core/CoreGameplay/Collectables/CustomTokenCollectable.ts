import type { CollectableOptions } from '@/game/core/CoreGameplay/Collectables/CollectableOptions'
import { Coin<PERSON>kins } from '@/game/core/CoreGameplay/Collectables/TokenCollectable'
import { SpineCoinCollectable, type CoinConfig } from './SpineCoinCollectable'

export class CustomTokenCollectable extends SpineCoinCollectable {
  constructor(options: CollectableOptions) {
    const config: CoinConfig = {
      skinSelector: opts => (opts.coinType === 2 ? CoinSkins.Duck : CoinSkins.Rich),
      platformOffsetCalculator: () => 25
    }
    super(options, config)
    this.customCoinType = options.coinType || 0
  }

  override reset(options: CollectableOptions): void {
    this.customCoinType = options.coinType || 0
    super.reset(options)
  }
}

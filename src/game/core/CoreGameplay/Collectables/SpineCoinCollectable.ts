import { CollectableItemBase } from '@/game/core/CoreGameplay/Collectables/CollectableItemBase'
import type { CollectableOptions } from '@/game/core/CoreGameplay/Collectables/CollectableOptions'
import { CoinSkins } from '@/game/core/CoreGameplay/Collectables/TokenCollectable'
import type { SpineGameObject, Vector2 } from '@esotericsoftware/spine-phaser'
import { DepthOrder } from '../Constants/DephOrdering'
import {
  CoinAnimations,
  MobDataSetKeys,
  TicketAnimations
} from '../Player/PlayerStates/States/SpineAnimations'

export interface CoinConfig {
  skinSelector: (options: CollectableOptions) => CoinSkins
  platformOffsetCalculator: (options: CollectableOptions) => number
  scaleMultiplier?: number
  useTicketAnimationsForSync?: boolean
}

export abstract class SpineCoinCollectable extends CollectableItemBase {
  protected spine: SpineGameObject | null = null
  protected availableSkins: Set<string> = new Set(Object.values(Coin<PERSON>kins))
  protected skinId: CoinSkins = CoinSkins.Default
  protected config: CoinConfig
  protected customCoinType: number = 0

  protected constructor(options: CollectableOptions, config: CoinConfig) {
    super(options)
    this.config = config
    this.createSpine(this.sprite.x, this.sprite.y)
    this.skinId = config.skinSelector(options)
    this.setSkin(this.skinId)
    this.spine?.setOrigin(0, 0)
    this.startIdleAnimation()
    this.platformOffsetX = config.platformOffsetCalculator(options)
  }

  override reset(options: CollectableOptions): void {
    super.reset(options)
    this.platformOffsetX = this.config.platformOffsetCalculator(options)
    this.skinId = this.config.skinSelector(options)
    this.setSkin(this.skinId)
    if (this.spine) {
      this.spine?.setVisible(true)
      this.spine?.setOrigin(0, 0)
      this.spine?.setPosition(
        this.sprite.x + CoinAnimations.X_OFFSET,
        this.sprite.y + CoinAnimations.Y_OFFSET
      )
    }
    this.startIdleAnimation()
  }

  override returnToPool(): void {
    if (this.spine) {
      this.spine?.setVisible(false)
      this.spine?.animationState.clearTracks()
    }
    super.returnToPool()
  }

  override destroyCollectable(): void {
    this.spine?.destroy()
    this.spine = null
    super.destroyCollectable()
  }

  protected setSkin(skinName: CoinSkins) {
    if (this.availableSkins.has(skinName)) {
      this.spine?.skeleton.setSkinByName(skinName)
      this.spine?.skeleton.setSlotsToSetupPose()
      this.spine?.animationState.apply(this.spine?.skeleton)
    } else {
      console.warn(`Skin ${skinName} not found`)
    }
  }

  override collect(playerPos: Vector2) {
    super.collect(playerPos)
    this.spine?.animationState.setAnimation(0, CoinAnimations.T0_DISAPPEAR, false)
    this.returnToPoolWithDelay()
  }

  protected createSpine(x: number, y: number) {
    this.spine = this.scene!.add.spine(
      x + CoinAnimations.X_OFFSET,
      y + CoinAnimations.Y_OFFSET,
      MobDataSetKeys.TON_COIN_DATA,
      MobDataSetKeys.TON_COIN_ATLAS
    )

    const baseScale = this.config.scaleMultiplier ? this.sprite.scale : 1
    const finalScale = baseScale * CoinAnimations.SCALE
    this.spine?.setScale(finalScale)

    this.sprite.setAlpha(0)
    this.spine?.setDepth(DepthOrder.Collectables)
    if (!this.config.scaleMultiplier) {
      this.spine.x = this.staticBody.x
    }
  }

  override syncPosition(x: number) {
    if (this.config.scaleMultiplier) {
      this.sprite.x = x + this.platformOffsetX
      this.staticBody.x = x + this.platformOffsetX
      this.spine!.x = x + CoinAnimations.X_OFFSET + this.platformOffsetX
    } else {
      super.syncPosition(x)
      this.spine!.x = x + CoinAnimations.X_OFFSET + this.platformOffsetX
    }
  }

  override syncSpineAndColliderPosition() {
    if (this.config.useTicketAnimationsForSync) {
      this.spine!.x = this.sprite.x + TicketAnimations.X_OFFSET
      this.spine!.y = this.sprite.y + TicketAnimations.Y_OFFSET
    } else {
      this.spine!.x = this.sprite.x + CoinAnimations.X_OFFSET
      this.spine!.y = this.sprite.y + CoinAnimations.Y_OFFSET
    }
  }

  protected startIdleAnimation() {
    this.spine?.animationState.setAnimation(0, CoinAnimations.T0_IDLE, true)
    this.spine?.animationState.setAnimation(1, CoinAnimations.T1_IDLE_R, true)
  }
}

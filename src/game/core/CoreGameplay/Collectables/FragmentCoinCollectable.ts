import type { CollectableOptions } from '@/game/core/CoreGameplay/Collectables/CollectableOptions'
import { CoinSkins } from '@/game/core/CoreGameplay/Collectables/TokenCollectable'
import { SpineCoinCollectable, type CoinConfig } from './SpineCoinCollectable'

export class FragmentCoinCollectable extends SpineCoinCollectable {
  constructor(options: CollectableOptions) {
    const config: CoinConfig = {
      skinSelector: () => CoinSkins.Fragment,
      platformOffsetCalculator: () => 25
    }
    super(options, config)
  }
}

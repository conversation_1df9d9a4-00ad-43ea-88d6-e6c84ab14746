import type { CollectableOptions } from '@/game/core/CoreGameplay/Collectables/CollectableOptions'
import { ENTITIES_SIZES, EntityType } from '@/game/core/MapGen/MapGenerator'
import { SpineCoinCollectable, type CoinConfig } from './SpineCoinCollectable'

export enum CoinSkins {
  Default = 'default',
  Ton = 'ton',
  Uni = 'uni',
  Usdt = 'usdt',
  Trump = 'trump',
  Swag = 'swag',
  Dune = 'dune',

  Duolingo = 'Duolingo',
  Spring = 'Spring',
  Lucky = 'Lucky',
  Unicorn = 'Unicorn',
  Clown = 'clown',
  <PERSON> = 'Moon',
  Easter = 'easter',
  <PERSON> = 'rainbow',
  Minecraft = 'Minecraft',
  Banana = 'Banana',
  Dive = 'Dive',
  SW = 'SW',
  Battle = 'battle',
  PandaKF = 'PandaKF',
  SpongeB = 'SpongeB',
  Shrek = 'Shrek',
  <PERSON> = 'Batman',
  Fragment = 'Fragment',
  Scooby = 'Scooby',
  MortalKombat = 'MortalKombat',
  DogCat = 'DogCat',
  Gift = 'Gift',
  Boinkers = 'Boinkers',
  Meat = 'Meat',
  Gem = 'Gem',
  Web3 = 'Web3',
  Soon = 'Soon',
  Sword = 'Sword',
  Obsidian = 'Obsidian',
  Shards = 'Shards',
  Match = 'Match',
  PandaFit = 'PandaFit',
  Nicegram = 'Nicegram',
  Fight = 'Fight',
  Labrador = 'Labrador',
  Duck = 'Duck',
  Rich = 'Rich'
}

export interface SpawnedTokenData {
  height: string
  amount: number
  type: number
}

export class TonCollectable extends SpineCoinCollectable {
  constructor(options: CollectableOptions) {
    const config: CoinConfig = {
      skinSelector: () => CoinSkins.Ton,
      platformOffsetCalculator: opts => {
        const platformWidth = ENTITIES_SIZES[EntityType.PlatformStatic][0]
        const spriteWidth = ENTITIES_SIZES[EntityType.CollectibleTon][0]
        return platformWidth / 2 - spriteWidth / 2
      },
      scaleMultiplier: 1,
      useTicketAnimationsForSync: true
    }
    super(options, config)
  }
}

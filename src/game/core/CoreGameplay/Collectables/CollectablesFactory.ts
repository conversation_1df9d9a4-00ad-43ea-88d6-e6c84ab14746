import type { CollectableOptions } from '@/game/core/CoreGameplay/Collectables/CollectableOptions'
import { CustomTokenCollectable } from '@/game/core/CoreGameplay/Collectables/CustomTokenCollectable'
import { DynamicCoinCollectable } from '@/game/core/CoreGameplay/Collectables/DynamicCoinCollectable'
import { FragmentCoinCollectable } from '@/game/core/CoreGameplay/Collectables/FragmentCoinCollectable'
import {
  TicketCollectable,
  TicketSkins
} from '@/game/core/CoreGameplay/Collectables/TicketCollectable'
import { TonCollectable } from '@/game/core/CoreGameplay/Collectables/TokenCollectable'
import type { GameInterface } from '@/game/core/CoreGameplay/GameInterface'
import { EntityType } from '@/game/core/MapGen/MapGenerator'
import Phaser from 'phaser'
import { PoolSizeConsts } from '../Constants/PoolConsts'
import { CollectableItemBase } from './CollectableItemBase'
import { CollectablePool } from './CollectablePool'

export class CollectablesFactory {
  private ticketsPool: CollectablePool<TicketCollectable>
  private tonPool: CollectablePool<TonCollectable>
  private customCoinPool: CollectablePool<CustomTokenCollectable>
  private dynamicCoinPool: CollectablePool<DynamicCoinCollectable>
  private fragmentCoinPool: CollectablePool<FragmentCoinCollectable>

  constructor(scene: Phaser.Scene, gameInterface: GameInterface) {
    this.ticketsPool = new CollectablePool<TicketCollectable>(
      scene,
      TicketCollectable,
      gameInterface,
      PoolSizeConsts.TICKET_POOL_SIZE
    )
    this.tonPool = new CollectablePool<TonCollectable>(
      scene,
      TonCollectable,
      gameInterface,
      PoolSizeConsts.TON_POOL_SIZE
    )
    this.customCoinPool = new CollectablePool<CustomTokenCollectable>(
      scene,
      CustomTokenCollectable,
      gameInterface,
      PoolSizeConsts.CUSTOM_COIN_POOL_SIZE
    )
    this.dynamicCoinPool = new CollectablePool<DynamicCoinCollectable>(
      scene,
      DynamicCoinCollectable,
      gameInterface,
      PoolSizeConsts.DYNAMIC_COIN_POOL_SIZE
    )
    this.fragmentCoinPool = new CollectablePool<FragmentCoinCollectable>(
      scene,
      FragmentCoinCollectable,
      gameInterface,
      PoolSizeConsts.FRAGMENT_POOL_SIZE
    )
  }

  async preloadCollectables(collectableGroup: Phaser.Physics.Arcade.StaticGroup): Promise<void> {
    await Promise.all([
      this.ticketsPool.preload(5, EntityType.CollectibleTicket, collectableGroup),
      this.tonPool.preload(5, EntityType.CollectibleTon, collectableGroup),
      this.customCoinPool.preload(5, EntityType.CollectibleCustomCoin, collectableGroup),
      this.dynamicCoinPool.preload(5, EntityType.CollectibleDynamicCoin, collectableGroup),
      this.fragmentCoinPool.preload(5, EntityType.CollectiblePuzzleCoin, collectableGroup)
    ])
  }

  public createCollectable(collectableOptions: CollectableOptions): CollectableItemBase {
    switch (collectableOptions.entityType) {
      case EntityType.CollectibleTicket: {
        const ticket = this.ticketsPool.get(collectableOptions)
        switch (collectableOptions.amount) {
          case 1:
            ticket.setSkin(TicketSkins.OneTicket)
            break
          case 2:
            ticket.setSkin(TicketSkins.TwoTickets)
            break
          case 3:
            ticket.setSkin(TicketSkins.ThreeTickets)
            break
          default:
            console.warn('Invalid amount for ticket skin, defaulting to skin 1')
            ticket.setSkin(TicketSkins.OneTicket)
        }
        return ticket
      }
      case EntityType.CollectibleTon: {
        return this.tonPool.get(collectableOptions)
      }
      case EntityType.CollectibleCustomCoin: {
        return this.customCoinPool.get(collectableOptions)
      }
      case EntityType.CollectibleDynamicCoin: {
        return this.dynamicCoinPool.get(collectableOptions)
      }
      case EntityType.CollectiblePuzzleCoin: {
        return this.fragmentCoinPool.get(collectableOptions)
      }
      default:
        throw new Error('Invalid CollectableType')
    }
  }

  public returnCollectableToPool(collectable: CollectableItemBase): void {
    switch (collectable.getType()) {
      case EntityType.CollectibleTicket:
        this.ticketsPool.return(collectable as TicketCollectable)
        break
      case EntityType.CollectibleTon:
        this.tonPool.return(collectable as TonCollectable)
        break
      case EntityType.CollectibleCustomCoin:
        this.customCoinPool.return(collectable as CustomTokenCollectable)
        break
      case EntityType.CollectibleDynamicCoin:
        this.dynamicCoinPool.return(collectable as DynamicCoinCollectable)
        break
      case EntityType.CollectiblePuzzleCoin:
        this.fragmentCoinPool.return(collectable as FragmentCoinCollectable)
        break
      default:
        throw new Error('Invalid CollectableType')
    }
  }

  public clearCollectablePools(): void {
    this.ticketsPool.clear()
    this.tonPool.clear()
    this.customCoinPool.clear()
    this.dynamicCoinPool.clear()
    this.fragmentCoinPool.clear()
  }
}

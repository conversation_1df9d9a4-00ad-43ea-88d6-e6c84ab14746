.text-shadow {
  --text-shadow-weight: 2px;
  --text-stroke-weight: 3px;
  --text-bg-color: #002050;
  -webkit-text-stroke: var(--text-stroke-weight) var(--text-bg-color);
  filter: drop-shadow(0 var(--text-shadow-weight) var(--text-bg-color));
  &_fat {
    --text-shadow-weight: 3px;
    --text-stroke-weight: 5px;
  }
  &_thin {
    --text-shadow-weight: 2px;
    --text-stroke-weight: 2px;
  }
  &_black {
    --text-bg-color: black;
  }
  &_green {
    --text-bg-color: #326A0E;
  }
  &_blue {
    --text-bg-color: #035A83;
  }
  &_yellow {
    color: #FFE346;
    --text-bg-color: black;
  }
  &_purple {
    --text-bg-color: #8A2A97;
  }
  &_double {
    -webkit-text-stroke: 3px #8A2A97;
    filter: drop-shadow(0 1.5px black);
  }
  &_doubleGrey {
    -webkit-text-stroke: 3px #57535E;
    filter: drop-shadow(0 1.5px black);
  }
  &_shadow-only {
    -webkit-text-stroke: 0px;
  }
}

.text-gradient {
  background: -webkit-linear-gradient(#FFFFFF 26.59%, #FFE346 35.8%, #FFD900 50.83%, #FF9205 53.28%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  -webkit-text-stroke: 0.5px black;
}

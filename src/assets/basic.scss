@import './font/style.css';

/* color palette from <https://github.com/vuejs/theme> */
:root {
  --menu-color: "#28599C";
  --menu-background: url('@/assets/images/temp/background/menu-background.png') top center / contain no-repeat, linear-gradient(180deg, #1C9CE6 0%, #28599C 100%);

  // safe area
  --safe-area-inset-top: var(--tg-viewport-safe-area-inset-top, 0px);
  --content-safe-area-inset-top: var(--tg-viewport-content-safe-area-inset-top, 0px);
  --safe-area-inset-bottom: var(--tg-viewport-safe-area-inset-bottom, 0px);
  --content-safe-area-inset-bottom: var(--tg-viewport-content-safe-area-inset-bottom, 0px);
  --inset-top: calc(var(--safe-area-inset-top) + var(--content-safe-area-inset-top));
  --inset-bottom: calc(var(--safe-area-inset-bottom) + var(--content-safe-area-inset-bottom));

  // header
  --header-height-fixed: 48px;
  --header-height-bottom-padding: 31px;
  --header-height: calc(var(--header-height-fixed) + var(--inset-top));
  --header-color: #A2EEFF40;

  // footer
  --footer-offset: 0px;
  --footer-button-size: 44px;
  --footer-top-padding: 17px;
  --footer-bottom-padding: max(var(--footer-top-padding), var(--inset-bottom));
  --footer-height: calc(var(--footer-button-size) + var(--footer-bottom-padding) + var(--footer-top-padding));

  // small viewport phones like iPhone 8
  @media screen and (max-height: 667px) {
    --footer-height: max(var(--inset-bottom), 0px);
  }
}

.footer-high-offset {
  --footer-offset: 20px;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
}

html {
  overflow: hidden;
}

body {
  width: 100dvw;
  height: 100dvh;
  overflow: inherit;
  user-select: none;
}

a {
  color: white;
  text-decoration: none;
}

img {
  max-width: none;
}

//eruda console cheats input height increase to match some devices
.eruda-js-input {
  height: 50px!important;
}

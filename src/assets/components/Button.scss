.button {
  position: relative;
  display: flex;
  align-items: center;
  width: fit-content;
  line-height: 1;
  padding: 0 14px;
  color: white;

  &::after {
    content: '';
    position: absolute;
    top: 4px;
    left: 7px;
    width: 10px;
    height: 6px;
    background-color: white;
    border-radius: 50%;
    transform: rotate(-26deg)
  }

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    border-width: 2.5px;
    border-style: solid;
    border-radius: 6px;
    transform: skew(-10deg);
    box-shadow: 0 var(--btn-outer-shadow-y) #00000040, inset 0 var(--btn-inset1-shadow-y) var(--btn-inset1-shadow-col), inset 0 var(--btn-inset2-shadow-y) var(--btn-inset2-shadow-col);
  }

  &:not(:disabled):active::before {
    box-shadow: inset 0 100px rgba(0, 0, 0, 0.25);
  }

  &__content {
    width: 100%;
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    white-space: nowrap;
  }

  &_size {
    &_xsmall {
      height: 24px;
      font-size: 13px;
      font-weight: 800;
      padding: 0 8px;

      .button__content {
        top: -1px;
      }

      &.button_icon-only {
        width: 24px;
      }

      .button__image {
        width: 20px;
        height: 20px;
      }

      &:before {
        --btn-outer-shadow-y: 2px;
        --btn-inset1-shadow-y: 1px;
        --btn-inset2-shadow-y: -2px;
        border-radius: 8px 6px;
      }

      &::after {
        display: none;
      }
    }

    &_xxsmall {
      height: 21px;
      font-size: 16px;
      padding: 0 8px;

      .button__content {
        top: -1px;
      }

      &.button_icon-only {
        width: 21px;
      }

      .button__image {
        width: 16px;
        height: 16px;
      }

      &:before {
        --btn-outer-shadow-y: 2px;
        --btn-inset1-shadow-y: 1px;
        --btn-inset2-shadow-y: -2px;
        border-radius: 8px 6px;
      }

      &::after {
        display: none;
      }
    }

    &_small {
      height: 32px;
      font-size: 16px;

      .button__content {
        top: -1px;
      }

      &.button_icon-only {
        width: 46px;
      }

      .button__image {
        width: 20px;
        height: 20px;
      }

      &:before {
        --btn-outer-shadow-y: 1px;
        --btn-inset1-shadow-y: 1px;
        --btn-inset2-shadow-y: -2px;
      }

      &::after {
        display: none;
      }
    }

    &_medium {
      height: 42px;
      font-size: 24px;

      // &.button_icon-only {
      //   width: 66px;
      // }

      .button__image {
        width: 24px;
        height: 24px;
      }

      &:before {
        --btn-outer-shadow-y: 3px;
        --btn-inset1-shadow-y: 3px;
        --btn-inset2-shadow-y: -4px;
      }
    }

    &_large {
      height: 51px;
      font-size: 26px;

      .button__content {
        top: -2px;
      }

      &.button_icon-only {
        width: 66px;
      }

      .button__image {
        width: 28px;
        height: 28px;
      }

      &:before {
        --btn-outer-shadow-y: 3px;
        --btn-inset1-shadow-y: 3px;
        --btn-inset2-shadow-y: -6px;
      }
    }
  }

  &_default {
    &::before {
      background: linear-gradient(360deg, #0AA1FF 0%, #01DCFF 100%);
      border-color: #003F50;
      --btn-inset1-shadow-col: #C6F9FF;
      --btn-inset2-shadow-col: #1A7CC1;
    }
  }

  &_success {
    &::before {
      background: linear-gradient(360deg, #4DCA00 0%, #7AF500 108.82%);
      border-color: #025100;
      --btn-inset1-shadow-col: #E3FF95;
      --btn-inset2-shadow-col: #03AA00;
    }
  }

  &_accent {
    &::before {
      background: linear-gradient(360deg, #FF9B30 0%, #FFD900 108.82%);
      border-color: #9A4001;
      --btn-inset1-shadow-col: #FFFBAE;
      --btn-inset2-shadow-col: #F7781E;
    }
  }

  &_danger {
    &::before {
      background: linear-gradient(360deg, #D70000 0%, #FF5F37 100%);
      border-color: #313131;
      --btn-inset1-shadow-col: #FFA255;
      --btn-inset2-shadow-col: #960000;
    }
  }

  &_transparent {
    &::before {
      background-color: transparent;
    }
  }

  &:disabled {
    &::before {
      background: #BDBDBD;
      border-color: #3C3C3C;
      --btn-inset1-shadow-col: rgba(255, 255, 255, 0.25);
      --btn-inset2-shadow-col: rgba(0, 0, 0, 0.25);
    }
  }
}

.button-link {
  padding: 9px;
  border-radius: 9px;
  background-color: #00EEFF4D;
  color: #1E4073;
  font-size: 18px;
  text-align: center;
  text-decoration: underline;
}

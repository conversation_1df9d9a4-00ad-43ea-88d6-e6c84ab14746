@use "tailwindcss/base";
@use "tailwindcss/components";
@use "tailwindcss/utilities";

@use 'basic.scss';
@use 'typography.scss';
@use 'components/_style.scss';
@use 'animations.scss';

#app {
  width: inherit;
  height: inherit;
  max-width: 100dvw;
  max-height: 100dvh;
  
  font-family: 'Nunito', sans-serif;
  background: url('@/assets/images/temp/background.png') no-repeat;
  background-size: 100% 100%;
  color: white;
  line-height: 1;
  font-weight: 900;
  font-size: 1rem;
  letter-spacing: -0.5px;
  font-synthesis: none;
  paint-order: stroke fill;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app-view {
  width: inherit;
  height: inherit;
}

.main-view {
  width: inherit;
  height: inherit;
}

.view-container {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow-x: hidden;
  overflow-y: hidden;
}

.menu-item-wrapper {
  background: var(--menu-background);
  padding-top: var(--header-height);
}

.menu-item {
  width: inherit;
  height: inherit;
  overflow-y: auto;
  padding: var(--header-height-bottom-padding) 20px 16px;
}

.white-spot {
  position: absolute;
  background-color: white;
  border-radius: 50%;
}

.bottom-gradient {
  --bottom-position: 0px;

  &::after {
    content: '';
    position: absolute;
    z-index: 10;
    bottom: var(--bottom-position);
    width: 100%;
    height: 30px;
    background: linear-gradient(360deg, #29579a 14.82%, rgba(41, 87, 154, 0) 68.56%);
  }
}

.absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
